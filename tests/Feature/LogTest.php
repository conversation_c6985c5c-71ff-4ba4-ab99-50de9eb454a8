<?php

namespace Tests\Feature;

use App\Enums\Logger;
use App\Enums\SSHAlgo;
use App\Model\Entities\App;
use App\Model\Entities\Log;
use Doctrine\ORM\EntityManager;
use Doctrine\ORM\ORMInvalidArgumentException;
use Tests\TestCase;

class LogTest extends TestCase
{
    private EntityManager $em;
    private int $logId;
    private int $appId;

    public function test_insert_select_delete()
    {
        $error = '[2022-07-21 15-35-02] Warning: Undefined array key "name_required" in /web/app/FrontModule/forms/PoptavkaForm.php:100  @  http://mujweb-dek.cz/poptavka/nemopas?src=nemopas.cz%2Fexpertni-znalecka-cinnost&serv=expert&_fid=clfk  @@  exception--2022-07-21--15-35--46f72bac43.html'.PHP_EOL;
        $error .= '[2022-07-21 15-35-02] Warning: Undefined array key "custom_input" in /web/app/FrontModule/forms/PoptavkaForm.php:146  @  http://mujweb-dek.cz/poptavka/nemopas?src=nemopas.cz%2Fexpertni-znalecka-cinnost&serv=expert&_fid=clfk  @@  exception--2022-07-21--15-35--c8486984c3.html'.PHP_EOL;

        $this->em = app(EntityManager::class);

        $app = new App();
        $app->setTitle('TestApp');
        $app->setDomain('testapp.cz');
        $app->setGitlabId(0);
        $app->setSsh('');
        $app->setSshAlgo(SSHAlgo::SHA256);

        $log = new Log();
        $log->setApp($app);
        $log->setContent($error);
        $log->setFilename('error.log');
        $log->setLogger(Logger::TRACY);
        $log->setLoggerVersion('3.1.2');

        $app = $log->getApp();

        $this->assertInstanceOf(App::class, $app);

        $this->insertLogFail($log);
        $this->insertLogSuccess($log);
        $this->selectLog();
        $this->deleteLog($log);
        $this->clean($log);
    }

    private function insertLogFail(Log $log)
    {
        $this->assertException(ORMInvalidArgumentException::class, function() use ($log) {
            $this->em->persist($log);
            $this->em->flush();
        });
    }

    private function insertLogSuccess(Log $log)
    {
        $this->assertNotException(ORMInvalidArgumentException::class, function() use ($log) {
            $this->em->persist($log);
            $this->em->persist($log->getApp());
            $this->em->flush();

            $this->logId = $log->getId();
            $this->appId = $log->getApp()->getId();

            $this->assertNotNull($this->logId);
            $this->assertNotNull($this->appId);
        });
    }

    private function selectLog()
    {
        $log = $this->em->getRepository(Log::class)->find($this->logId);
        $app = $this->em->getRepository(App::class)->find($this->appId);

        $this->assertInstanceOf(Log::class, $log);
        $this->assertInstanceOf(App::class, $app);

        $this->assertEquals($log->getId(), $this->logId);
        $this->assertEquals($app->getId(), $this->appId);
        $this->assertEquals($log->getApp()->getId(), $this->appId);
    }

    private function deleteLog(Log $log)
    {
        $this->em->remove($log);
        $this->em->flush();

        $found = $this->em->getRepository(Log::class)->find($this->logId);
        $this->assertNull($found);
    }

    private function clean()
    {
        $app = $this->em->getRepository(App::class)->find($this->appId);
        $this->em->remove($app);
        $this->em->flush();
    }

    protected function assertException(string $expected, callable $callback, string $message = '')
    {
        $e = null;
        try {
            $callback();
        } catch (\Exception $e) {
        }
        $this->assertInstanceOf($expected, $e, $message);
    }

    protected function assertNotException(string $expected, callable $callback, string $message = '')
    {
        $e = null;
        try {
            $callback();
        } catch (\Exception $e) {
        }
        $this->assertNotInstanceOf($expected, $e, $message);
    }
}
