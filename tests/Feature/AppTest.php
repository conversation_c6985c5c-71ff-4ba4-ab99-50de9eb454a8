<?php

namespace Tests\Feature;

use App\Enums\ErrorType;
use App\Enums\Logger;
use App\Enums\SSHAlgo;
use App\Model\Entities\App;
use App\Model\Entities\Error;
use App\Model\Entities\Log;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\EntityManager;
use Tests\TestCase;

class AppTest extends TestCase
{
    private EntityManager $em;
    private int $appId;
    private int $logIds;
    private int $errorId;

    public function test_insert_select_delete()
    {
        $content = '[2022-07-21 15-35-02] Warning: Undefined array key "name_required" in /web/app/FrontModule/forms/PoptavkaForm.php:100  @  http://mujweb-dek.cz/poptavka/nemopas?src=nemopas.cz%2Fexpertni-znalecka-cinnost&serv=expert&_fid=clfk  @@  exception--2022-07-21--15-35--46f72bac43.html'.PHP_EOL;
        //$error .= '[2022-07-21 15-35-02] Warning: Undefined array key "custom_input" in /web/app/FrontModule/forms/PoptavkaForm.php:146  @  http://mujweb-dek.cz/poptavka/nemopas?src=nemopas.cz%2Fexpertni-znalecka-cinnost&serv=expert&_fid=clfk  @@  exception--2022-07-21--15-35--c8486984c3.html'.PHP_EOL;

        $this->em = app(EntityManager::class);

        $app = new App();
        $app->setTitle('TestApp');
        $app->setDomain('testapp.cz');
        $app->setGitlabId(0);
        $app->setSsh('');
        $app->setSshAlgo(SSHAlgo::SHA256);

        $log = new Log();
        $log->setContent($content);
        $log->setFilename('error.log');
        $log->setLogger(Logger::TRACY);
        $log->setLoggerVersion('3.1.2');

        $error = new Error();
        $error->setType(ErrorType::E_WARNING);
        $error->setMessage('Undefined array key "name_required"');
        $error->setFile('/web/app/FrontModule/forms/PoptavkaForm.php');
        $error->setLine(100);
        $error->setOriginal($content);
        $error->setUrl('http://mujweb-dek.cz/poptavka/nemopas?src=nemopas.cz%2Fexpertni-znalecka-cinnost&serv=expert&_fid=clfk');
        $error->setFilename('error.log');
        $error->setException(null);

        $app->addError($error);
        //$app->addLog($log);

        $errors = $app->getErrors();
        //$logs = $app->getLogs();

        $this->assertInstanceOf(Collection::class, $errors);
        //$this->assertInstanceOf(Collection::class, $logs);

        $this->insertApp($app);
        $this->selectApp();
        $this->deleteApp($app);
    }

    private function insertApp(App $app)
    {
        $this->em->persist($app);
        $this->em->flush();

        $this->appId = $app->getId();
        $this->logId = $app->getLogs()->first()->getId();
        $this->errorId = $app->getErrors()->first()->getId();

        $this->assertNotNull($this->appId);
        $this->assertNotNull($this->errorId);
        $this->assertNotNull($this->errorId);
    }

    private function selectApp()
    {
        $app = $this->em->getRepository(App::class)->find($this->appId);
        $log = $this->em->getRepository(Log::class)->find($this->logId);
        $error = $this->em->getRepository(Error::class)->find($this->appId);

        $this->assertInstanceOf(App::class, $app);
        $this->assertInstanceOf(Log::class, $log);
        $this->assertInstanceOf(Error::class, $error);

        $this->assertEquals($app->getId(), $this->appId);
        $this->assertEquals($app->getErrors()->first()->getId(), $this->errorId);
        $this->assertEquals($app->getLogs()->first()->getId(), $this->logId);
    }

    private function deleteApp(App $app)
    {
        $this->em->remove($app);
        $this->em->flush();

        $app = $this->em->getRepository(App::class)->find($this->appId);
        $log = $this->em->getRepository(Log::class)->find($this->logId);
        $error = $this->em->getRepository(Error::class)->find($this->errorId);

        $this->assertNull($app);
        $this->assertNull($log);
        $this->assertNull($error);
    }
}
