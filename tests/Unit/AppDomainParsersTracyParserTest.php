<?php

namespace Tests\Unit;

use App\Domain\Parsers\ErrorsCollection;
use App\Domain\Parsers\IParser;
use App\Domain\Parsers\TracyParser;
use App\Enums\Logger;
use App\Enums\SSHAlgo;
use App\Model\Entities\App;
use App\Model\Entities\Log;
use PHPUnit\Framework\TestCase;

class AppDomainParsersTracyParserTest extends TestCase
{
    private string $error = '';
    private string $exception = '';

    public function setUpErrorStrings(): void
    {
        $this->error .= '[2022-07-21 15-35-02] Warning: Undefined array key "name_required" in /web/app/FrontModule/forms/PoptavkaForm.php:100  @  http://mujweb-dek.cz/poptavka/nemopas?src=nemopas.cz%2Fexpertni-znalecka-cinnost&serv=expert&_fid=clfk  @@  exception--2022-07-21--15-35--46f72bac43.html'.PHP_EOL;
        $this->error .= '[2022-07-21 15-35-02] Warning: Undefined array key "custom_input" in /web/app/FrontModule/forms/PoptavkaForm.php:146  @  http://mujweb-dek.cz/poptavka/nemopas?src=nemopas.cz%2Fexpertni-znalecka-cinnost&serv=expert&_fid=clfk  @@  exception--2022-07-21--15-35--c8486984c3.html'.PHP_EOL;
    }

    public function test_iparser_implements()
    {
        $parser = new TracyParser();
        $this->assertInstanceOf(IParser::class, $parser);
    }

    public function test_is_error()
    {
        $log = $this->getErrorLogEntity();

        $parser = new TracyParser();
        $valid = $parser->isError($log);

        $this->assertTrue($valid);
    }

    public function test_is_not_error()
    {
        $log = $this->getTraceLogEntity();

        $parser = new TracyParser();
        $valid = $parser->isError($log);

        $this->assertFalse($valid);
    }

    public function test_parse_error()
    {
        $log = $this->getErrorLogEntity();

        $parser = new TracyParser();
        $errors = $parser->parseError($log);

        $this->assertInstanceOf(ErrorsCollection::class, $errors);
    }

    public function test_is_trace()
    {
        $log = $this->getTraceLogEntity();

        $parser = new TracyParser();
        $valid = $parser->isTrace($log);

        $this->assertTrue($valid);
    }

    public function test_is_not_trace()
    {
        $log = $this->getErrorLogEntity();

        $parser = new TracyParser();
        $valid = $parser->isTrace($log);

        $this->assertFalse($valid);
    }

    private function getErrorLogEntity(): Log
    {
        $error = '[2022-07-21 15-35-02] Warning: Undefined array key "name_required" in /web/app/FrontModule/forms/PoptavkaForm.php:100  @  http://mujweb-dek.cz/poptavka/nemopas?src=nemopas.cz%2Fexpertni-znalecka-cinnost&serv=expert&_fid=clfk  @@  exception--2022-07-21--15-35--46f72bac43.html'.PHP_EOL;
        $error .= '[2022-07-21 15-35-02] Warning: Undefined array key "custom_input" in /web/app/FrontModule/forms/PoptavkaForm.php:146  @  http://mujweb-dek.cz/poptavka/nemopas?src=nemopas.cz%2Fexpertni-znalecka-cinnost&serv=expert&_fid=clfk  @@  exception--2022-07-21--15-35--c8486984c3.html'.PHP_EOL;

        $app = new App();
        $app->setTitle('TestApp');
        $app->setDomain('testapp.cz');
        $app->setGitlabId(0);
        $app->setSsh('');
        $app->setSshAlgo(SSHAlgo::SHA256);

        $log = new Log();
        $log->setApp($app);
        $log->setContent($error);
        $log->setFilename('error.log');
        $log->setLogger(Logger::TRACY);
        $log->setLoggerVersion('2.6.8');

        return $log;
    }

    private function getTraceLogEntity(): Log
    {
        $error = '[2022-07-21 15-35-02] Warning: Undefined array key "name_required" in /web/app/FrontModule/forms/PoptavkaForm.php:100  @  http://mujweb-dek.cz/poptavka/nemopas?src=nemopas.cz%2Fexpertni-znalecka-cinnost&serv=expert&_fid=clfk  @@  exception--2022-07-21--15-35--46f72bac43.html'.PHP_EOL;
        $error .= '[2022-07-21 15-35-02] Warning: Undefined array key "custom_input" in /web/app/FrontModule/forms/PoptavkaForm.php:146  @  http://mujweb-dek.cz/poptavka/nemopas?src=nemopas.cz%2Fexpertni-znalecka-cinnost&serv=expert&_fid=clfk  @@  exception--2022-07-21--15-35--c8486984c3.html'.PHP_EOL;

        $app = new App();
        $app->setTitle('TestApp');
        $app->setDomain('testapp.cz');
        $app->setGitlabId(0);
        $app->setSsh('');
        $app->setSshAlgo(SSHAlgo::SHA256);

        $log = new Log();
        $log->setApp($app);
        $log->setContent('<html><title>Pseudoexception</title><body>Hello</body></html>');
        $log->setFilename('exception--2022-11-07--14-58--d905278328.html');
        $log->setLogger(Logger::TRACY);
        $log->setLoggerVersion('2.6.8');

        return $log;
    }
}
