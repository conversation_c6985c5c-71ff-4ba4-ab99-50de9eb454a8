<!DOCTYPE html><!-- "' --></textarea></script></style></pre></xmp></a></iframe></noembed></noframes></noscript></option></select></template></title></table></p></code>
<html>
<head>
	<meta charset="utf-8">
	<meta name="robots" content="noindex">
	<meta name="generator" content="Tracy by Nette Framework">

	<title>Fatal Error: Allowed memory size of 134217728 bytes exhausted (tried to allocate 20480 bytes)</title>
	<!-- in phar:///web/vendor/phpstan/phpstan/phpstan.phar/vendor/nikic/php-parser/lib/PhpParser/Parser/Php7.php:772 -->

	<style class="tracy-debug">
	tracy-div,tracy-div *{font: inherit;line-height: inherit;color: inherit;background: transparent;margin: 0;padding: 0;border: none;text-align: inherit;list-style: inherit;opacity: 1;border-radius: 0;box-shadow: none;text-shadow: none;box-sizing: border-box;text-decoration: none;text-transform: inherit;white-space: inherit;float: none;clear: none;max-width: initial;min-width: initial;max-height: initial;min-height: initial}tracy-div *:not(svg):not(img):not(table){width: initial;height: initial}tracy-div:before,tracy-div:after,tracy-div *:before,tracy-div *:after{all: unset}tracy-div b,tracy-div strong{font-weight: bold}tracy-div small{font-size: smaller}tracy-div i,tracy-div em{font-style: italic}tracy-div big{font-size: larger}tracy-div small,tracy-div sub,tracy-div sup{font-size: smaller}tracy-div ins{text-decoration: underline}tracy-div del{text-decoration: line-through}tracy-div table{border-collapse: collapse}tracy-div pre{font-family: monospace;white-space: pre}tracy-div code,tracy-div kbd,tracy-div samp{font-family: monospace}tracy-div input{background-color: white;padding: 1px;border: 1px solid}tracy-div textarea{background-color: white;border: 1px solid;padding: 2px;white-space: pre-wrap}tracy-div select{border: 1px solid;white-space: pre}tracy-div article,tracy-div aside,tracy-div details,tracy-div div,tracy-div figcaption,tracy-div footer,tracy-div form,tracy-div header,tracy-div hgroup,tracy-div main,tracy-div nav,tracy-div section,tracy-div summary,tracy-div pre,tracy-div p,tracy-div dl,tracy-div dd,tracy-div dt,tracy-div blockquote,tracy-div figure,tracy-div address,tracy-div h1,tracy-div h2,tracy-div h3,tracy-div h4,tracy-div h5,tracy-div h6,tracy-div ul,tracy-div ol,tracy-div li,tracy-div hr{display: block}tracy-div a,tracy-div b,tracy-div big,tracy-div code,tracy-div em,tracy-div i,tracy-div small,tracy-div span,tracy-div strong{display: inline}tracy-div table{display: table}tracy-div tr{display: table-row}tracy-div col{display: table-column}tracy-div colgroup{display: table-column-group}tracy-div tbody{display: table-row-group}tracy-div thead{display: table-header-group}tracy-div tfoot{display: table-footer-group}tracy-div td{display: table-cell}tracy-div th{display: table-cell}tracy-div .tracy-sortable>:first-child>tr:first-child>*{position: relative}tracy-div .tracy-sortable>:first-child>tr:first-child>*:hover:before{position: absolute;right: .3em;content:"\21C5";opacity: .4;font-weight: normal}tracy-div .tracy-dump div{padding-left: 3ex}tracy-div .tracy-dump div div{border-left: 1px solid rgba(0,0,0,.1);margin-left: .5ex}tracy-div .tracy-dump div div:hover{border-left-color: rgba(0,0,0,.25)}tracy-div .tracy-dump{background: #FDF5CE;padding: .4em .7em;border: 1px dotted silver;overflow: auto}tracy-div table .tracy-dump{padding: 0;margin: 0;border: none}tracy-div .tracy-dump-location{color: gray;font-size: 80%;text-decoration: none;background: none;opacity: .5;float: right;cursor: pointer}tracy-div .tracy-dump-location:hover,tracy-div .tracy-dump-location:focus{color: gray;background: none;opacity: 1}tracy-div .tracy-dump-array,tracy-div .tracy-dump-object{color: #C22}tracy-div .tracy-dump-string{color: #35D;white-space: break-spaces}tracy-div div.tracy-dump-string{position: relative;padding-left: 3.5ex}tracy-div .tracy-dump-lq{margin-left: calc(-1ex - 1px)}tracy-div div.tracy-dump-string:before{content:'';position: absolute;left: calc(3ex - 1px);top: 1.5em;bottom: 0;border-left: 1px solid rgba(0,0,0,.1)}tracy-div .tracy-dump-virtual span,tracy-div .tracy-dump-dynamic span,tracy-div .tracy-dump-string span{color: rgba(0,0,0,0.5)}tracy-div .tracy-dump-virtual i,tracy-div .tracy-dump-dynamic i,tracy-div .tracy-dump-string i{font-size: 80%;font-style: normal;color: rgba(0,0,0,0.5);user-select: none}tracy-div .tracy-dump-number{color: #090}tracy-div .tracy-dump-null,tracy-div .tracy-dump-bool{color: #850}tracy-div .tracy-dump-virtual{font-style: italic}tracy-div .tracy-dump-public::after{content:' pub'}tracy-div .tracy-dump-protected::after{content:' pro'}tracy-div .tracy-dump-private::after{content:' pri'}tracy-div .tracy-dump-public::after,tracy-div .tracy-dump-protected::after,tracy-div .tracy-dump-private::after,tracy-div .tracy-dump-hash{font-size: 85%;color: rgba(0,0,0,0.5)}tracy-div .tracy-dump-indent{display: none}tracy-div .tracy-dump-highlight{background: #C22;color: white;border-radius: 2px;padding: 0 2px;margin: 0 -2px}tracy-div span[data-tracy-href]{border-bottom: 1px dotted rgba(0,0,0,.2)}tracy-div .tracy-toggle:after{content:'';display: inline-block;vertical-align: middle;line-height: 0;border-top: .6ex solid;border-right: .6ex solid transparent;border-left: .6ex solid transparent;transform: scale(1,1.5);margin: 0 .2ex 0 .7ex;transition: .1s transform;opacity: .5}tracy-div .tracy-toggle.tracy-collapsed:after{transform: rotate(-90deg) scale(1,1.5) translate(.1ex,0)}tracy-div .tracy-tab-label{user-select: none}tracy-div .tracy-tab-panel:not(.tracy-active){display: none}:root{--tracy-space: 16px}#tracy-bs{font: 9pt/1.5 Verdana,sans-serif;background: white;color: #333;position: absolute;z-index: 20000;left: 0;top: 0;width: 100%;text-align: left}#tracy-bs a{text-decoration: none;color: #328ADC;padding: 0 4px;margin: 0 -4px}#tracy-bs a+a{margin-left: 0}#tracy-bs a:hover,#tracy-bs a:focus{color: #085AA3}#tracy-bs-toggle{position: absolute;right: .5em;top: .5em;text-decoration: none;background: #CD1818;color: white!important;padding: 3px}#tracy-bs-toggle.tracy-collapsed{position: fixed}.tracy-bs-main{display: flex;flex-direction: column;padding-bottom: 80vh}.tracy-bs-main.tracy-collapsed{display: none}#tracy-bs p,#tracy-bs table,#tracy-bs pre,#tracy-bs h1,#tracy-bs h2,#tracy-bs h3{margin: 0 0 var(--tracy-space)}#tracy-bs h1{font-size: 15pt;font-weight: normal;text-shadow: 1px 1px 2px rgba(0,0,0,.3)}#tracy-bs h1 span{white-space: pre-wrap}#tracy-bs h2{font-size: 14pt;font-weight: normal;margin-top: var(--tracy-space)}#tracy-bs h3{font-size: 10pt;font-weight: bold}#tracy-bs pre,#tracy-bs code,#tracy-bs table{font: 9pt/1.5 Consolas,monospace!important}#tracy-bs pre,#tracy-bs table{background: #FDF5CE;padding: .4em .7em;border: 2px solid #ffffffa6;box-shadow: 1px 2px 6px #00000005;overflow: auto}#tracy-bs table pre{padding: 0;margin: 0;border: none;box-shadow: none}#tracy-bs table{border-collapse: collapse;width: 100%}#tracy-bs td,#tracy-bs th{vertical-align: top;text-align: left;padding: 2px 6px;border: 1px solid #e6dfbf}#tracy-bs th{font-weight: bold}#tracy-bs tr>:first-child{width: 20%}#tracy-bs tr:nth-child(2n),#tracy-bs tr:nth-child(2n) pre{background-color: #F7F0CB}#tracy-bs .tracy-footer--sticky{position: fixed;width: 100%;bottom: 0}#tracy-bs footer ul{font-size: 7pt;padding: var(--tracy-space);margin: var(--tracy-space) 0 0;color: #777;background: #F6F5F3;border-top: 1px solid #DDD;list-style: none}#tracy-bs .tracy-footer-logo{position: relative}#tracy-bs .tracy-footer-logo a{position: absolute;bottom: 0;right: 0;width: 100px;height: 50px;background: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAFoAAAAUBAMAAAD/1DctAAAAMFBMVEWupZzj39rEvbTy8O3X0sz9/PvGwLu8tavQysHq6OS0rKP5+Pbd2dT29fPMxbzPx8DKErMJAAAACXBIWXMAAAsTAAALEwEAmpwYAAACGUlEQVQoFX3TQWgTQRQA0MWLIJJDYehBTykhG5ERTx56K1u8eEhCYtomE7x5L4iLh0ViF7egewuFFqSIYE6hIHsIYQ6CQSg9CDKn4QsNCRlB59C74J/ZNHW1+An5+bOPyf6/s46oz2P+A0yIeZZ2ieEHi6TOnLKTxvWq+b52mxlVO3xnM1s7xLX1504XQH65OnW2dBqn7cCkYsFsfYsWpyY/2salmFTpEyzeR8zosYqMdiPDXdyU52K1wgEa/SjGpdEwUAxqvRfckQCDOyFearsEHe2grvkh/cFAHKvdtI3lcVceKQIOFpv+FOZaNPQBwJZLPp+hfrvT5JZXaUFsR8zqQc9qSgAharkfS5M/5F6nGJJAtXq/eLr3ucZpHccSxOOIPaQhtHohpCH2Xu6rLmQ0djnr4/+J3C6v+AW8/XWYxwYNdlhWj/P5fPSTQwVr0T9lGxdaBCqErNZaqYnEwbkjEB3NasGF3lPdrHa1nnxNOMgj0+neePUPjd2v/qVvUv29ifvc19huQ48qwXShy/9o8o3OSk0cs37mOFd0Ydgvsf/oZEnPVtggfd66lORn9mDyyzXU13SRtH2L6aR5T/snGAcZPfAXz5J1YlJWBEuxdMYqQecpBrlM49xAbmqyHA+xlA1FxBtqT2xmJoNXZlIt74ZBLeJ9ZGDqByNI7p543idzJ23vXEv7IgnsxiS+eNtwNbFdLq7+Bi4wQ0I4SVb9AAAAAElFTkSuQmCC') no-repeat;opacity: .6;padding: 0;margin: 0}#tracy-bs .tracy-footer-logo a:hover,#tracy-bs .tracy-footer-logo a:focus{opacity: 1;transition: opacity 0.1s}#tracy-bs .tracy-section{padding-left: calc(1.5 * var(--tracy-space));padding-right: calc(1.5 * var(--tracy-space))}#tracy-bs .tracy-section-panel{background: #F4F3F1;padding: var(--tracy-space) var(--tracy-space) 0;margin: 0 0 var(--tracy-space);border-radius: 8px;box-shadow: inset 1px 1px 0px 0 #00000005;overflow: hidden}#tracy-bs .outer,#tracy-bs .tracy-pane{overflow: auto}#tracy-bs.tracy-mac .tracy-pane{padding-bottom: 12px}#tracy-bs .tracy-section--error{background: #CD1818;color: white;font-size: 13pt;padding-top: var(--tracy-space)}#tracy-bs .tracy-section--error h1{color: white}#tracy-bs .tracy-section--error::selection,#tracy-bs .tracy-section--error ::selection{color: black!important;background: #FDF5CE!important}#tracy-bs .tracy-section--error a{color: #ffefa1!important}#tracy-bs .tracy-section--error span span{font-size: 80%;color: rgba(255,255,255,0.5);text-shadow: none}#tracy-bs .tracy-section--error a.tracy-action{color: white!important;opacity: 0;font-size: .7em;border-bottom: none!important}#tracy-bs .tracy-section--error:hover a.tracy-action{opacity: .6}#tracy-bs .tracy-section--error a.tracy-action:hover{opacity: 1}#tracy-bs .tracy-section--error i{color: #ffefa1;font-style: normal}#tracy-bs pre.tracy-code>div{min-width: 100%;float: left;white-space: pre}#tracy-bs .tracy-line-highlight{background: #CD1818;color: white;font-weight: bold;font-style: normal;display: block;padding: 0 1ch;margin: 0 -1ch}#tracy-bs .tracy-column-highlight{display: inline-block;backdrop-filter: grayscale(1);margin: 0 -1px;padding: 0 1px}#tracy-bs .tracy-line{color: #9F9C7F;font-weight: normal;font-style: normal}#tracy-bs a.tracy-editor{color: inherit;border-bottom: 1px dotted rgba(0,0,0,.3);border-radius: 3px}#tracy-bs a.tracy-editor:hover{background: #0001}#tracy-bs span[data-tracy-href]{border-bottom: 1px dotted rgba(0,0,0,.3)}#tracy-bs .tracy-dump-whitespace{color: #0003}#tracy-bs .tracy-caused{float: right;padding: .3em calc(1.5 * var(--tracy-space));background: #df8075;border-radius: 0 0 0 8px;white-space: nowrap}#tracy-bs .tracy-caused a{color: white}#tracy-bs .tracy-callstack{display: grid;grid-template-columns: max-content 1fr;margin-bottom: calc(.5 * var(--tracy-space))}#tracy-bs .tracy-callstack-file{text-align: right;padding-right: var(--tracy-space);white-space: nowrap;height: calc(1.5 * var(--tracy-space))}#tracy-bs .tracy-callstack-callee{white-space: nowrap;height: calc(1.5 * var(--tracy-space))}#tracy-bs .tracy-callstack-additional{grid-column-start: 1;grid-column-end: 3}#tracy-bs .tracy-callstack-args tr:first-child>*{position: relative}#tracy-bs .tracy-callstack-args tr:first-child td:before{position: absolute;right: .3em;content:'may not be true';opacity: .4}#tracy-bs .tracy-panel-fadein{animation: tracy-panel-fadein .12s ease}@keyframes tracy-panel-fadein{0%{opacity: 0}}#tracy-bs .tracy-section--causedby{flex-direction: column;padding: 0}#tracy-bs .tracy-section--causedby:not(.tracy-collapsed){display: flex}#tracy-bs .tracy-section--causedby .tracy-section--error{background: #cd1818a6}#tracy-bs .tracy-section--error+.tracy-section--stack{margin-top: calc(1.5 * var(--tracy-space))}#tracy-bs .tracy-tab-bar{display: flex;list-style: none;padding-left: 0;margin: 0;width: 100%;font-size: 110%}#tracy-bs .tracy-tab-bar>*:not(:first-child){margin-left: var(--tracy-space)}#tracy-bs .tracy-tab-bar a{display: block;padding: calc(.5 * var(--tracy-space)) var(--tracy-space);margin: 0;height: 100%;box-sizing: border-box;border-radius: 5px 5px 0 0;text-decoration: none;transition: all 0.1s}#tracy-bs .tracy-tab-bar>.tracy-active a{background: white}#tracy-bs .tracy-tab-panel{border-top: 2px solid white;padding-top: var(--tracy-space);overflow: auto}.tracy-collapsed{display: none}.tracy-toggle.tracy-collapsed{display: inline}.tracy-toggle{cursor: pointer;user-select: none;white-space: nowrap}.tracy-toggle:after{content:'';display: inline-block;vertical-align: middle;line-height: 0;border-top: .6ex solid;border-right: .6ex solid transparent;border-left: .6ex solid transparent;transform: scale(1,1.5);margin: 0 .2ex 0 .7ex;transition: .1s transform;opacity: .5}.tracy-toggle.tracy-collapsed:after{transform: rotate(-90deg) scale(1,1.5) translate(.1ex,0)}.tracy-sortable>:first-child>tr:first-child>*{position: relative}.tracy-sortable>:first-child>tr:first-child>*:hover:before{position: absolute;right: .3em;content:"\21C5";opacity: .4;font-weight: normal}.tracy-tab-label{user-select: none}.tracy-tab-panel:not(.tracy-active){display: none}.tracy-dump.tracy-light{text-align: left;color: #444;background: #fdf9e2;border-radius: 4px;padding: 1em;margin: 1em 0;word-break: break-all;white-space: pre-wrap}.tracy-dump.tracy-light div{padding-left: 2.5ex}.tracy-dump.tracy-light div div{border-left: 1px solid rgba(0,0,0,.1);margin-left: .5ex}.tracy-dump.tracy-light div div:hover{border-left-color: rgba(0,0,0,.25)}.tracy-light .tracy-dump-location{color: gray;font-size: 80%;text-decoration: none;background: none;opacity: .5;float: right;cursor: pointer}.tracy-light .tracy-dump-location:hover,.tracy-light .tracy-dump-location:focus{opacity: 1}.tracy-light .tracy-dump-array,.tracy-light .tracy-dump-object{color: #C22;user-select: text}.tracy-light .tracy-dump-string{color: #35D;white-space: break-spaces}.tracy-light div.tracy-dump-string{position: relative;padding-left: 3.5ex}.tracy-light .tracy-dump-lq{margin-left: calc(-1ex - 1px)}.tracy-light div.tracy-dump-string:before{content:'';position: absolute;left: calc(3ex - 1px);top: 1.5em;bottom: 0;border-left: 1px solid rgba(0,0,0,.1)}.tracy-light .tracy-dump-virtual span,.tracy-light .tracy-dump-dynamic span,.tracy-light .tracy-dump-string span{color: rgba(0,0,0,0.5)}.tracy-light .tracy-dump-virtual i,.tracy-light .tracy-dump-dynamic i,.tracy-light .tracy-dump-string i{font-size: 80%;font-style: normal;color: rgba(0,0,0,0.5);user-select: none}.tracy-light .tracy-dump-number{color: #090}.tracy-light .tracy-dump-null,.tracy-light .tracy-dump-bool{color: #850}.tracy-light .tracy-dump-virtual{font-style: italic}.tracy-light .tracy-dump-public::after{content:' pub'}.tracy-light .tracy-dump-protected::after{content:' pro'}.tracy-light .tracy-dump-private::after{content:' pri'}.tracy-light .tracy-dump-public::after,.tracy-light .tracy-dump-protected::after,.tracy-light .tracy-dump-private::after,.tracy-light .tracy-dump-hash{font-size: 85%;color: rgba(0,0,0,0.35)}.tracy-light .tracy-dump-indent{display: none}.tracy-light .tracy-dump-highlight{background: #C22;color: white;border-radius: 2px;padding: 0 2px;margin: 0 -2px}span[data-tracy-href]{border-bottom: 1px dotted rgba(0,0,0,.2)}.tracy-light .tracy-dump-flash{animation: tracy-dump-flash .2s ease}@keyframes tracy-dump-flash{0%{background: #c0c0c033}}	</style>
</head>


<body>
<tracy-div id="tracy-bs" itemscope>
	<a id="tracy-bs-toggle" href="#" class="tracy-toggle"></a>
	<div class="tracy-bs-main">
<section class="tracy-section tracy-section--error">
	<p>Fatal Error</p>

	<h1><span>Allowed memory size of 134217728 bytes exhausted (tried to allocate 20480 bytes)</span>
	</h1>
</section>




<section class="tracy-section">
	<h2 class="tracy-section-label"><a href="#" data-tracy-ref="^+" class="tracy-toggle">Source file</a></h2>

	<div class="tracy-section-panel">
		<p><b>File:</b> <a href="editor://open/?file=phar%3A%2F%2F%2Fweb%2Fvendor%2Fphpstan%2Fphpstan%2Fphpstan.phar%2Fvendor%2Fnikic%2Fphp-parser%2Flib%2FPhpParser%2FParser%2FPhp7.php&amp;line=772&amp;search=&amp;replace=" title="phar:///web/vendor/phpstan/phpstan/phpstan.phar/vendor/nikic/php-parser/lib/PhpParser/Parser/Php7.php:772" class="tracy-editor">.../vendor/nikic/php-parser/lib/PhpParser/Parser/<b>Php7.php</b>:772</a></p>
		