<!DOCTYPE html><!-- "' --></textarea></script></style></pre></xmp></a></iframe></noembed></noframes></noscript></option></select></template></title></table></p></code>
<html>
<head>
	<meta charset="utf-8">
	<meta name="robots" content="noindex">
	<meta name="generator" content="Tracy by Nette Framework">

	<title>Fatal Error: Allowed memory size of 134217728 bytes exhausted (tried to allocate 20480 bytes)</title>
	<!-- in phar:///web/vendor/phpstan/phpstan/phpstan.phar/vendor/nikic/php-parser/lib/PhpParser/Lexer.php:78 -->

	<style class="tracy-debug">
	tracy-div,tracy-div *{font: inherit;line-height: inherit;color: inherit;background: transparent;margin: 0;padding: 0;border: none;text-align: inherit;list-style: inherit;opacity: 1;border-radius: 0;box-shadow: none;text-shadow: none;box-sizing: border-box;text-decoration: none;text-transform: inherit;white-space: inherit;float: none;clear: none;max-width: initial;min-width: initial;max-height: initial;min-height: initial}tracy-div *:not(svg):not(img):not(table){width: initial;height: initial}tracy-div:before,tracy-div:after,tracy-div *:before,tracy-div *:after{all: unset}tracy-div b,tracy-div strong{font-weight: bold}tracy-div small{font-size: smaller}tracy-div i,tracy-div em{font-style: italic}tracy-div big{font-size: larger}tracy-div small,tracy-div sub,tracy-div sup{font-size: smaller}tracy-div ins{text-decoration: underline}tracy-div del{text-decoration: line-through}tracy-div table{border-collapse: collapse}tracy-div pre{font-family: monospace;white-space: pre}tracy-div code,tracy-div kbd,tracy-div samp{font-family: monospace}tracy-div input{background-color: white;padding: 1px;border: 1px solid}tracy-div textarea{background-color: white;border: 1px solid;padding: 2px;white-space: pre-wrap}tracy-div select{border: 1px solid;white-space: pre}tracy-div article,tracy-div aside,tracy-div details,tracy-div div,tracy-div figcaption,tracy-div footer,tracy-div form,tracy-div header,tracy-div hgroup,tracy-div main,tracy-div nav,tracy-div section,tracy-div summary,tracy-div pre,tracy-div p,tracy-div dl,tracy-div dd,tracy-div dt,tracy-div blockquote,tracy-div figure,tracy-div address,tracy-div h1,tracy-div h2,tracy-div h3,tracy-div h4,tracy-div h5,tracy-div h6,tracy-div ul,tracy-div ol,tracy-div li,tracy-div hr{display: block}tracy-div a,tracy-div b,tracy-div big,tracy-div code,tracy-div em,tracy-div i,tracy-div small,tracy-div span,tracy-div strong{display: inline}tracy-div table{display: table}tracy-div tr{display: table-row}tracy-div col{display: table-column}tracy-div colgroup{display: table-column-group}tracy-div tbody{display: table-row-group}tracy-div thead{display: table-header-group}tracy-div tfoot{display: table-footer-group}tracy-div td{display: table-cell}tracy-div th{display: table-cell}tracy-div .tracy-sortable>:first-child>tr:first-child>*{position: relative}tracy-div .tracy-sortable>:first-child>tr:first-child>*:hover:before{position: absolute;right: .3em;content:"\21C5";opacity: .4;font-weight: normal}tracy-div .tracy-dump div{padding-left: 3ex}tracy-div .tracy-dump div div{border-left: 1px solid rgba(0,0,0,.1);margin-left: .5ex}tracy-div .tracy-dump div div:hover{border-left-color: rgba(0,0,0,.25)}tracy-div .tracy-dump{background: #FDF5CE;padding: .4em .7em;border: 1px dotted silver;overflow: auto}tracy-div table .tracy-dump{padding: 0;margin: 0;border: none}tracy-div .tracy-dump-location{color: gray;font-size: 80%;text-decoration: none;background: none;opacity: .5;float: right;cursor: pointer}tracy-div .tracy-dump-location:hover,tracy-div .tracy-dump-location:focus{color: gray;background: none;opacity: 1}tracy-div .tracy-dump-array,tracy-div .tracy-dump-object{color: #C22}tracy-div .tracy-dump-string{color: #35D;white-space: break-spaces}tracy-div div.tracy-dump-string{position: relative;padding-left: 3.5ex}tracy-div .tracy-dump-lq{margin-left: calc(-1ex - 1px)}tracy-div div.tracy-dump-string:before{content:'';position: absolute;left: calc(3ex - 1px);top: 1.5em;bottom: 0;border-left: 1px solid rgba(0,0,0,.1)}tracy-div .tracy-dump-virtual span,tracy-div .tracy-dump-dynamic span,tracy-div .tracy-dump-string span{color: rgba(0,0,0,0.5)}tracy-div .tracy-dump-virtual i,tracy-div .tracy-dump-dynamic i,tracy-div .tracy-dump-string i{font-size: 80%;font-style: normal;color: rgba(0,0,0,0.5);user-select: none}tracy-div .tracy-dump-number{color: #090}tracy-div .tracy-dump-null,tracy-div .tracy-dump-bool{color: #850}tracy-div .tracy-dump-virtual{font-style: italic}tracy-div .tracy-dump-public::after{content:' pub'}tracy-div .tracy-dump-protected::after{content:' pro'}tracy-div .tracy-dump-private::after{content:' pri'}tracy-div .tracy-dump-public::after,tracy-div .tracy-dump-protected::after,tracy-div .tracy-dump-private::after,tracy-div .tracy-dump-hash{font-size: 85%;color: rgba(0,0,0,0.5)}tracy-div .tracy-dump-indent{display: none}tracy-div .tracy-dump-highlight{background: #C22;color: white;border-radius: 2px;padding: 0 2px;margin: 0 -2px}tracy-div span[data-tracy-href]{border-bottom: 1px dotted rgba(0,0,0,.2)}tracy-div .tracy-toggle:after{content:'';display: inline-block;vertical-align: middle;line-height: 0;border-top: .6ex solid;border-right: .6ex solid transparent;border-left: .6ex solid transparent;transform: scale(1,1.5);margin: 0 .2ex 0 .7ex;transition: .1s transform;opacity: .5}tracy-div .tracy-toggle.tracy-collapsed:after{transform: rotate(-90deg) scale(1,1.5) translate(.1ex,0)}tracy-div .tracy-tab-label{user-select: none}tracy-div .tracy-tab-panel:not(.tracy-active){display: none}:root{--tracy-space: 16px}#tracy-bs{font: 9pt/1.5 Verdana,sans-serif;background: white;color: #333;position: absolute;z-index: 20000;left: 0;top: 0;width: 100%;text-align: left}#tracy-bs a{text-decoration: none;color: #328ADC;padding: 0 4px;margin: 0 -4px}#tracy-bs a+a{margin-left: 0}#tracy-bs a:hover,#tracy-bs a:focus{color: #085AA3}#tracy-bs-toggle{position: absolute;right: .5em;top: .5em;text-decoration: none;background: #CD1818;color: white!important;padding: 3px}#tracy-bs-toggle.tracy-collapsed{position: fixed}.tracy-bs-main{display: flex;flex-direction: column;padding-bottom: 80vh}.tracy-bs-main.tracy-collapsed{display: none}#tracy-bs p,#tracy-bs table,#tracy-bs pre,#tracy-bs h1,#tracy-bs h2,#tracy-bs h3{margin: 0 0 var(--tracy-space)}#tracy-bs h1{font-size: 15pt;font-weight: normal;text-shadow: 1px 1px 2px rgba(0,0,0,.3)}#tracy-bs h1 span{white-space: pre-wrap}#tracy-bs h2{font-size: 14pt;font-weight: normal;margin-top: var(--tracy-space)}#tracy-bs h3{font-size: 10pt;font-weight: bold}#tracy-bs pre,#tracy-bs code,#tracy-bs table{font: 9pt/1.5 Consolas,monospace!important}#tracy-bs pre,#tracy-bs table{background: #FDF5CE;padding: .4em .7em;border: 2px solid #ffffffa6;box-shadow: 1px 2px 6px #00000005;overflow: auto}#tracy-bs table pre{padding: 0;margin: 0;border: none;box-shadow: none}#tracy-bs table{border-collapse: collapse;width: 100%}#tracy-bs td,#tracy-bs th{vertical-align: top;text-align: left;padding: 2px 6px;border: 1px solid #e6dfbf}#tracy-bs th{font-weight: bold}#tracy-bs tr>:first-child{width: 20%}#tracy-bs tr:nth-child(2n),#tracy-bs tr:nth-child(2n) pre{background-color: #F7F0CB}#tracy-bs .tracy-footer--sticky{position: fixed;width: 100%;bottom: 0}#tracy-bs footer ul{font-size: 7pt;padding: var(--tracy-space);margin: var(--tracy-space) 0 0;color: #777;background: #F6F5F3;border-top: 1px solid #DDD;list-style: none}#tracy-bs .tracy-footer-logo{position: relative}#tracy-bs .tracy-footer-logo a{position: absolute;bottom: 0;right: 0;width: 100px;height: 50px;background: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAFoAAAAUBAMAAAD/1DctAAAAMFBMVEWupZzj39rEvbTy8O3X0sz9/PvGwLu8tavQysHq6OS0rKP5+Pbd2dT29fPMxbzPx8DKErMJAAAACXBIWXMAAAsTAAALEwEAmpwYAAACGUlEQVQoFX3TQWgTQRQA0MWLIJJDYehBTykhG5ERTx56K1u8eEhCYtomE7x5L4iLh0ViF7egewuFFqSIYE6hIHsIYQ6CQSg9CDKn4QsNCRlB59C74J/ZNHW1+An5+bOPyf6/s46oz2P+A0yIeZZ2ieEHi6TOnLKTxvWq+b52mxlVO3xnM1s7xLX1504XQH65OnW2dBqn7cCkYsFsfYsWpyY/2salmFTpEyzeR8zosYqMdiPDXdyU52K1wgEa/SjGpdEwUAxqvRfckQCDOyFearsEHe2grvkh/cFAHKvdtI3lcVceKQIOFpv+FOZaNPQBwJZLPp+hfrvT5JZXaUFsR8zqQc9qSgAharkfS5M/5F6nGJJAtXq/eLr3ucZpHccSxOOIPaQhtHohpCH2Xu6rLmQ0djnr4/+J3C6v+AW8/XWYxwYNdlhWj/P5fPSTQwVr0T9lGxdaBCqErNZaqYnEwbkjEB3NasGF3lPdrHa1nnxNOMgj0+neePUPjd2v/qVvUv29ifvc19huQ48qwXShy/9o8o3OSk0cs37mOFd0Ydgvsf/oZEnPVtggfd66lORn9mDyyzXU13SRtH2L6aR5T/snGAcZPfAXz5J1YlJWBEuxdMYqQecpBrlM49xAbmqyHA+xlA1FxBtqT2xmJoNXZlIt74ZBLeJ9ZGDqByNI7p543idzJ23vXEv7IgnsxiS+eNtwNbFdLq7+Bi4wQ0I4SVb9AAAAAElFTkSuQmCC') no-repeat;opacity: .6;padding: 0;margin: 0}#tracy-bs .tracy-footer-logo a:hover,#tracy-bs .tracy-footer-logo a:focus{opacity: 1;transition: opacity 0.1s}#tracy-bs .tracy-section{padding-left: calc(1.5 * var(--tracy-space));padding-right: calc(1.5 * var(--tracy-space))}#tracy-bs .tracy-section-panel{background: #F4F3F1;padding: var(--tracy-space) var(--tracy-space) 0;margin: 0 0 var(--tracy-space);border-radius: 8px;box-shadow: inset 1px 1px 0px 0 #00000005;overflow: hidden}#tracy-bs .outer,#tracy-bs .tracy-pane{overflow: auto}#tracy-bs.tracy-mac .tracy-pane{padding-bottom: 12px}#tracy-bs .tracy-section--error{background: #CD1818;color: white;font-size: 13pt;padding-top: var(--tracy-space)}#tracy-bs .tracy-section--error h1{color: white}#tracy-bs .tracy-section--error::selection,#tracy-bs .tracy-section--error ::selection{color: black!important;background: #FDF5CE!important}#tracy-bs .tracy-section--error a{color: #ffefa1!important}#tracy-bs .tracy-section--error span span{font-size: 80%;color: rgba(255,255,255,0.5);text-shadow: none}#tracy-bs .tracy-section--error a.tracy-action{color: white!important;opacity: 0;font-size: .7em;border-bottom: none!important}#tracy-bs .tracy-section--error:hover a.tracy-action{opacity: .6}#tracy-bs .tracy-section--error a.tracy-action:hover{opacity: 1}#tracy-bs .tracy-section--error i{color: #ffefa1;font-style: normal}#tracy-bs pre.tracy-code>div{min-width: 100%;float: left;white-space: pre}#tracy-bs .tracy-line-highlight{background: #CD1818;color: white;font-weight: bold;font-style: normal;display: block;padding: 0 1ch;margin: 0 -1ch}#tracy-bs .tracy-column-highlight{display: inline-block;backdrop-filter: grayscale(1);margin: 0 -1px;padding: 0 1px}#tracy-bs .tracy-line{color: #9F9C7F;font-weight: normal;font-style: normal}#tracy-bs a.tracy-editor{color: inherit;border-bottom: 1px dotted rgba(0,0,0,.3);border-radius: 3px}#tracy-bs a.tracy-editor:hover{background: #0001}#tracy-bs span[data-tracy-href]{border-bottom: 1px dotted rgba(0,0,0,.3)}#tracy-bs .tracy-dump-whitespace{color: #0003}#tracy-bs .tracy-caused{float: right;padding: .3em calc(1.5 * var(--tracy-space));background: #df8075;border-radius: 0 0 0 8px;white-space: nowrap}#tracy-bs .tracy-caused a{color: white}#tracy-bs .tracy-callstack{display: grid;grid-template-columns: max-content 1fr;margin-bottom: calc(.5 * var(--tracy-space))}#tracy-bs .tracy-callstack-file{text-align: right;padding-right: var(--tracy-space);white-space: nowrap;height: calc(1.5 * var(--tracy-space))}#tracy-bs .tracy-callstack-callee{white-space: nowrap;height: calc(1.5 * var(--tracy-space))}#tracy-bs .tracy-callstack-additional{grid-column-start: 1;grid-column-end: 3}#tracy-bs .tracy-callstack-args tr:first-child>*{position: relative}#tracy-bs .tracy-callstack-args tr:first-child td:before{position: absolute;right: .3em;content:'may not be true';opacity: .4}#tracy-bs .tracy-panel-fadein{animation: tracy-panel-fadein .12s ease}@keyframes tracy-panel-fadein{0%{opacity: 0}}#tracy-bs .tracy-section--causedby{flex-direction: column;padding: 0}#tracy-bs .tracy-section--causedby:not(.tracy-collapsed){display: flex}#tracy-bs .tracy-section--causedby .tracy-section--error{background: #cd1818a6}#tracy-bs .tracy-section--error+.tracy-section--stack{margin-top: calc(1.5 * var(--tracy-space))}#tracy-bs .tracy-tab-bar{display: flex;list-style: none;padding-left: 0;margin: 0;width: 100%;font-size: 110%}#tracy-bs .tracy-tab-bar>*:not(:first-child){margin-left: var(--tracy-space)}#tracy-bs .tracy-tab-bar a{display: block;padding: calc(.5 * var(--tracy-space)) var(--tracy-space);margin: 0;height: 100%;box-sizing: border-box;border-radius: 5px 5px 0 0;text-decoration: none;transition: all 0.1s}#tracy-bs .tracy-tab-bar>.tracy-active a{background: white}#tracy-bs .tracy-tab-panel{border-top: 2px solid white;padding-top: var(--tracy-space);overflow: auto}.tracy-collapsed{display: none}.tracy-toggle.tracy-collapsed{display: inline}.tracy-toggle{cursor: pointer;user-select: none;white-space: nowrap}.tracy-toggle:after{content:'';display: inline-block;vertical-align: middle;line-height: 0;border-top: .6ex solid;border-right: .6ex solid transparent;border-left: .6ex solid transparent;transform: scale(1,1.5);margin: 0 .2ex 0 .7ex;transition: .1s transform;opacity: .5}.tracy-toggle.tracy-collapsed:after{transform: rotate(-90deg) scale(1,1.5) translate(.1ex,0)}.tracy-sortable>:first-child>tr:first-child>*{position: relative}.tracy-sortable>:first-child>tr:first-child>*:hover:before{position: absolute;right: .3em;content:"\21C5";opacity: .4;font-weight: normal}.tracy-tab-label{user-select: none}.tracy-tab-panel:not(.tracy-active){display: none}.tracy-dump.tracy-light{text-align: left;color: #444;background: #fdf9e2;border-radius: 4px;padding: 1em;margin: 1em 0;word-break: break-all;white-space: pre-wrap}.tracy-dump.tracy-light div{padding-left: 2.5ex}.tracy-dump.tracy-light div div{border-left: 1px solid rgba(0,0,0,.1);margin-left: .5ex}.tracy-dump.tracy-light div div:hover{border-left-color: rgba(0,0,0,.25)}.tracy-light .tracy-dump-location{color: gray;font-size: 80%;text-decoration: none;background: none;opacity: .5;float: right;cursor: pointer}.tracy-light .tracy-dump-location:hover,.tracy-light .tracy-dump-location:focus{opacity: 1}.tracy-light .tracy-dump-array,.tracy-light .tracy-dump-object{color: #C22;user-select: text}.tracy-light .tracy-dump-string{color: #35D;white-space: break-spaces}.tracy-light div.tracy-dump-string{position: relative;padding-left: 3.5ex}.tracy-light .tracy-dump-lq{margin-left: calc(-1ex - 1px)}.tracy-light div.tracy-dump-string:before{content:'';position: absolute;left: calc(3ex - 1px);top: 1.5em;bottom: 0;border-left: 1px solid rgba(0,0,0,.1)}.tracy-light .tracy-dump-virtual span,.tracy-light .tracy-dump-dynamic span,.tracy-light .tracy-dump-string span{color: rgba(0,0,0,0.5)}.tracy-light .tracy-dump-virtual i,.tracy-light .tracy-dump-dynamic i,.tracy-light .tracy-dump-string i{font-size: 80%;font-style: normal;color: rgba(0,0,0,0.5);user-select: none}.tracy-light .tracy-dump-number{color: #090}.tracy-light .tracy-dump-null,.tracy-light .tracy-dump-bool{color: #850}.tracy-light .tracy-dump-virtual{font-style: italic}.tracy-light .tracy-dump-public::after{content:' pub'}.tracy-light .tracy-dump-protected::after{content:' pro'}.tracy-light .tracy-dump-private::after{content:' pri'}.tracy-light .tracy-dump-public::after,.tracy-light .tracy-dump-protected::after,.tracy-light .tracy-dump-private::after,.tracy-light .tracy-dump-hash{font-size: 85%;color: rgba(0,0,0,0.35)}.tracy-light .tracy-dump-indent{display: none}.tracy-light .tracy-dump-highlight{background: #C22;color: white;border-radius: 2px;padding: 0 2px;margin: 0 -2px}span[data-tracy-href]{border-bottom: 1px dotted rgba(0,0,0,.2)}.tracy-light .tracy-dump-flash{animation: tracy-dump-flash .2s ease}@keyframes tracy-dump-flash{0%{background: #c0c0c033}}	</style>
</head>


<body>
<tracy-div id="tracy-bs" itemscope>
	<a id="tracy-bs-toggle" href="#" class="tracy-toggle"></a>
	<div class="tracy-bs-main">
<section class="tracy-section tracy-section--error">
	<p>Fatal Error</p>

	<h1><span>Allowed memory size of 134217728 bytes exhausted (tried to allocate 20480 bytes)</span>
	</h1>
</section>




<section class="tracy-section">
	<h2 class="tracy-section-label"><a href="#" data-tracy-ref="^+" class="tracy-toggle">Source file</a></h2>

	<div class="tracy-section-panel">
		<p><b>File:</b> <a href="editor://open/?file=phar%3A%2F%2F%2Fweb%2Fvendor%2Fphpstan%2Fphpstan%2Fphpstan.phar%2Fvendor%2Fnikic%2Fphp-parser%2Flib%2FPhpParser%2FLexer.php&amp;line=78&amp;search=&amp;replace=" title="phar:///web/vendor/phpstan/phpstan/phpstan.phar/vendor/nikic/php-parser/lib/PhpParser/Lexer.php:78" class="tracy-editor">.../phpstan.phar/vendor/nikic/php-parser/lib/PhpParser/<b>Lexer.php</b>:78</a></p>
		<pre title="Ctrl-Click to open in editor" data-tracy-href="editor://open/?file=phar%3A%2F%2F%2Fweb%2Fvendor%2Fphpstan%2Fphpstan%2Fphpstan.phar%2Fvendor%2Fnikic%2Fphp-parser%2Flib%2FPhpParser%2FLexer.php&amp;line=78&amp;search=&amp;replace=" class='tracy-code'><div><code><span style="color: #06B"><span style="color: #D24; font-weight: bold"><span class='tracy-line'>68:</span>            }
<span class='tracy-line'>69:</span>            </span><span style="color: #000">$this</span><span style="color: #D24; font-weight: bold">-&gt;</span><span style="color: #000">code </span><span style="color: #D24; font-weight: bold">= </span><span style="color: #000">$code</span><span style="color: #D24; font-weight: bold">;
<span class='tracy-line'>70:</span>            </span><span style="color: #998; font-style: italic">// keep the code around for __halt_compiler() handling
<span class='tracy-line'>71:</span>            </span><span style="color: #000">$this</span><span style="color: #D24; font-weight: bold">-&gt;</span><span style="color: #000">pos </span><span style="color: #D24; font-weight: bold">= -</span><span style="color: #000">1</span><span style="color: #D24; font-weight: bold">;
<span class='tracy-line'>72:</span>            </span><span style="color: #000">$this</span><span style="color: #D24; font-weight: bold">-&gt;</span><span style="color: #000">line </span><span style="color: #D24; font-weight: bold">= </span><span style="color: #000">1</span><span style="color: #D24; font-weight: bold">;
<span class='tracy-line'>73:</span>            </span><span style="color: #000">$this</span><span style="color: #D24; font-weight: bold">-&gt;</span><span style="color: #000">filePos </span><span style="color: #D24; font-weight: bold">= </span><span style="color: #000">0</span><span style="color: #D24; font-weight: bold">;
<span class='tracy-line'>74:</span>            </span><span style="color: #998; font-style: italic">// If inline HTML occurs without preceding code, treat it as if it had a leading newline.
<span class='tracy-line'>75:</span>            // This ensures proper composability, because having a newline is the "safe" assumption.
<span class='tracy-line'>76:</span>            </span><span style="color: #000">$this</span><span style="color: #D24; font-weight: bold">-&gt;</span><span style="color: #000">prevCloseTagHasNewline </span><span style="color: #D24; font-weight: bold">= </span><span style="color: #000">\true</span><span style="color: #D24; font-weight: bold">;
<span class='tracy-line'>77:</span>            </span><span style="color: #000">$scream </span><span style="color: #D24; font-weight: bold">= </span><span style="color: #000">\ini_set</span><span style="color: #D24; font-weight: bold">(</span><span style="color: #080">'xdebug.scream'</span><span style="color: #D24; font-weight: bold">, </span><span style="color: #080">'0'</span><span style="color: #D24; font-weight: bold">);
<span class='tracy-line-highlight'>78:            $this-&gt;tokens = @\token_get_all($code);
</span></span><span style="color: #000"></span><span style="color: #D24; font-weight: bold"></span><span style="color: #000"></span><span style="color: #D24; font-weight: bold"></span><span style="color: #000"></span><span style="color: #D24; font-weight: bold"></span><span style="color: #000"></span><span style="color: #D24; font-weight: bold"><span class='tracy-line'>79:</span>            </span><span style="color: #000">$this</span><span style="color: #D24; font-weight: bold">-&gt;</span><span style="color: #000">postprocessTokens</span><span style="color: #D24; font-weight: bold">(</span><span style="color: #000">$errorHandler</span><span style="color: #D24; font-weight: bold">);
<span class='tracy-line'>80:</span>            if (</span><span style="color: #000">\false </span><span style="color: #D24; font-weight: bold">!== </span><span style="color: #000">$scream</span><span style="color: #D24; font-weight: bold">) {
<span class='tracy-line'>81:</span>                </span><span style="color: #000">\ini_set</span><span style="color: #D24; font-weight: bold">(</span><span style="color: #080">'xdebug.scream'</span><span style="color: #D24; font-weight: bold">, </span><span style="color: #000">$scream</span><span style="color: #D24; font-weight: bold">);
<span class='tracy-line'>82:</span>            }
</span></span></code></div></pre>
	</div>
</section>


<section class="tracy-section">
	<h2 class="tracy-section-label"><a href="#" data-tracy-ref="^+" class="tracy-toggle tracy-collapsed">Exception</a></h2>
	<div class="tracy-section-panel tracy-collapsed">
		<pre class="tracy-dump tracy-light" data-tracy-dump='{"ref":83232}'></pre>
	</div>
</section>





<section class="tracy-section">
	<h2 class="tracy-section-label"><a href="#" data-tracy-ref="^+" class="tracy-toggle tracy-collapsed">CLI request</a></h2>

	<div class="tracy-section-panel tracy-collapsed">
		<h3>Process ID 405</h3>
		<pre>php vendor/bin/phpstan worker --configuration /web/phpstan.neon --port 34839 --identifier vj3vjg250o --</pre>

		<h3>Arguments</h3>
		<div class="tracy-pane">
			<table>
				<tr><th>0</th><td><pre class="tracy-dump tracy-light"
><span class="tracy-dump-string" title="18 characters"><span>'</span>vendor/bin/phpstan<span>'</span></span></pre>
</td></tr>
				<tr><th>1</th><td><pre class="tracy-dump tracy-light"><span class="tracy-dump-string" title="6 characters"><span>'</span>worker<span>'</span></span></pre>
</td></tr>
				<tr><th>2</th><td><pre class="tracy-dump tracy-light"
><span class="tracy-dump-string" title="15 characters"><span>'</span>--configuration<span>'</span></span></pre>
</td></tr>
				<tr><th>3</th><td><pre class="tracy-dump tracy-light"
><span class="tracy-dump-string" title="17 characters"><span>'</span>/web/phpstan.neon<span>'</span></span></pre>
</td></tr>
				<tr><th>4</th><td><pre class="tracy-dump tracy-light"><span class="tracy-dump-string" title="6 characters"><span>'</span>--port<span>'</span></span></pre>
</td></tr>
				<tr><th>5</th><td><pre class="tracy-dump tracy-light"><span class="tracy-dump-string" title="5 characters"><span>'</span>34839<span>'</span></span></pre>
</td></tr>
				<tr><th>6</th><td><pre class="tracy-dump tracy-light"
><span class="tracy-dump-string" title="12 characters"><span>'</span>--identifier<span>'</span></span></pre>
</td></tr>
				<tr><th>7</th><td><pre class="tracy-dump tracy-light"><span class="tracy-dump-string" title="10 characters"><span>'</span>vj3vjg250o<span>'</span></span></pre>
</td></tr>
				<tr><th>8</th><td><pre class="tracy-dump tracy-light"><span class="tracy-dump-string" title="2 characters"><span>'</span>--<span>'</span></span></pre>
</td></tr>
			</table>
		</div>
	</div>
</section>



		<footer>
			<ul>
				<li><b><a href="https://github.com/sponsors/dg" target="_blank" rel="noreferrer noopener">Please support Tracy via a donation 💙️</a></b></li>
				<li>Report generated at 2022/05/25 11:09:06</li>
				<li>PHP 8.1.3</li><li>Tracy 2.9.2</li>			</ul>
			<div class="tracy-footer-logo"><a href="https://tracy.nette.org" rel="noreferrer"></a></div>
		</footer>
	</div>
	<meta itemprop=tracy-snapshot content='{"83232":{"object":"ErrorException","items":[["severity",1,1],["message","Allowed memory size of 134217728 bytes exhausted (tried to allocate 20480 bytes)",1],["string","","Exception"],["code",0,1],["file","phar:///web/vendor/phpstan/phpstan/phpstan.phar/vendor/nikic/php-parser/lib/PhpParser/Lexer.php",1],["line",78,1],["trace",[[0,[["function","shutdownHandler"],["class","Tracy\\Debugger"],["type","::"],["args",[]]]]],"Exception"],["previous",null,"Exception"]]}}'>
</tracy-div>

<script>
'use strict';
(function(){const MOVE_THRESHOLD=100;class Toggle{static init(){let start;document.documentElement.addEventListener('mousedown',(e)=>{start=[e.clientX,e.clientY];});document.documentElement.addEventListener('click',(e)=>{let el;if(!e.shiftKey&&!e.ctrlKey&&!e.metaKey&&(el=e.target.closest('.tracy-toggle'))&&Math.pow(start[0]-e.clientX,2)+Math.pow(start[1]-e.clientY,2)<MOVE_THRESHOLD){Toggle.toggle(el,undefined,e);e.preventDefault();e.stopImmediatePropagation();}});Toggle.init=function(){};}static toggle(el,expand,e){let collapsed=el.classList.contains('tracy-collapsed'),ref=el.getAttribute('data-tracy-ref')||el.getAttribute('href',2),dest=el;if(typeof expand==='undefined'){expand=collapsed;}if(!ref||ref==='#'){ref='+';}else if(ref.substr(0,1)==='#'){dest=document;}ref=ref.match(/(\^\s*([^+\s]*)\s*)?(\+\s*(\S*)\s*)?(.*)/);dest=ref[1]?dest.parentNode:dest;dest=ref[2]?dest.closest(ref[2]):dest;dest=ref[3]?Toggle.nextElement(dest.nextElementSibling,ref[4]):dest;dest=ref[5]?dest.querySelector(ref[5]):dest;el.classList.toggle('tracy-collapsed',!expand);dest.classList.toggle('tracy-collapsed',!expand);el.dispatchEvent(new CustomEvent('tracy-toggle',{bubbles:true,detail:{relatedTarget:dest,collapsed:!expand,originalEvent:e}}));}static persist(baseEl,restore){let saved=[];baseEl.addEventListener('tracy-toggle',(e)=>{if(saved.indexOf(e.target)<0){saved.push(e.target);}});let toggles=JSON.parse(sessionStorage.getItem('tracy-toggles-'+baseEl.id));if(toggles&&restore!==false){toggles.forEach((item)=>{let el=baseEl;for(let i in item.path){if(!(el=el.children[item.path[i]])){return;}}if(el.textContent===item.text){Toggle.toggle(el,item.expand);}});}window.addEventListener('unload',()=>{toggles=saved.map((el)=>{let item={path:[],text:el.textContent,expand:!el.classList.contains('tracy-collapsed')};do{item.path.unshift(Array.from(el.parentNode.children).indexOf(el));el=el.parentNode;}while(el&&el!==baseEl);return item;});sessionStorage.setItem('tracy-toggles-'+baseEl.id,JSON.stringify(toggles));});}static nextElement(el,selector){while(el&&selector&&!el.matches(selector)){el=el.nextElementSibling;}return el;}}let Tracy=window.Tracy=window.Tracy||{};Tracy.Toggle=Tracy.Toggle||Toggle;})();(function(){class TableSort{static init(){document.documentElement.addEventListener('click',(e)=>{if(e.target.matches('.tracy-sortable > :first-child > tr:first-child *')){TableSort.sort(e.target.closest('td,th'));}});TableSort.init=function(){};}static sort(tcell){let tbody=tcell.closest('table').tBodies[0];let preserveFirst=!tcell.closest('thead')&&!tcell.parentNode.querySelectorAll('td').length;let asc=!(tbody.tracyAsc===tcell.cellIndex);tbody.tracyAsc=asc?tcell.cellIndex:null;let getText=(cell)=>{return cell?(cell.getAttribute('data-order')||cell.innerText):'';};Array.from(tbody.children).slice(preserveFirst?1:0).sort((a,b)=>{return function(v1,v2){return v1!==''&&v2!==''&&!isNaN(v1)&&!isNaN(v2)?v1-v2:v1.toString().localeCompare(v2);}(getText((asc?a:b).children[tcell.cellIndex]),getText((asc?b:a).children[tcell.cellIndex]));}).forEach((tr)=>{tbody.appendChild(tr);});}}let Tracy=window.Tracy=window.Tracy||{};Tracy.TableSort=Tracy.TableSort||TableSort;})();(function(){class Tabs{static init(){document.documentElement.addEventListener('click',(e)=>{let label,context;if(!e.shiftKey&&!e.ctrlKey&&!e.metaKey&&(label=e.target.closest('.tracy-tab-label'))&&(context=e.target.closest('.tracy-tabs'))){Tabs.toggle(context,label);e.preventDefault();e.stopImmediatePropagation();}});Tabs.init=function(){};}static toggle(context,label){let labels=context.querySelector('.tracy-tab-label').parentNode.querySelectorAll('.tracy-tab-label'),panels=context.querySelector('.tracy-tab-panel').parentNode.querySelectorAll(':scope > .tracy-tab-panel');for(let i=0;i<labels.length;i++){labels[i].classList.toggle('tracy-active',labels[i]===label);}for(let i=0;i<panels.length;i++){panels[i].classList.toggle('tracy-active',labels[i]===label);}}}let Tracy=window.Tracy=window.Tracy||{};Tracy.Tabs=Tracy.Tabs||Tabs;})();(function(){const COLLAPSE_COUNT=7,COLLAPSE_COUNT_TOP=14,TYPE_ARRAY='a',TYPE_OBJECT='o',TYPE_RESOURCE='r',PROP_VIRTUAL=4,PROP_PRIVATE=2;const HINT_CTRL='Ctrl-Click to open in editor',HINT_ALT='Alt-Click to expand/collapse all child nodes';class Dumper{static init(context){(context||document).querySelectorAll('[data-tracy-snapshot][data-tracy-dump]').forEach((pre)=>{let snapshot=JSON.parse(pre.getAttribute('data-tracy-snapshot'));pre.removeAttribute('data-tracy-snapshot');pre.appendChild(build(JSON.parse(pre.getAttribute('data-tracy-dump')),snapshot,pre.classList.contains('tracy-collapsed')));pre.removeAttribute('data-tracy-dump');pre.classList.remove('tracy-collapsed');});(context||document).querySelectorAll('meta[itemprop=tracy-snapshot]').forEach((meta)=>{let snapshot=JSON.parse(meta.getAttribute('content'));meta.parentElement.querySelectorAll('[data-tracy-dump]').forEach((pre)=>{if(pre.closest('[data-tracy-snapshot]')){return;}pre.appendChild(build(JSON.parse(pre.getAttribute('data-tracy-dump')),snapshot,pre.classList.contains('tracy-collapsed')));pre.removeAttribute('data-tracy-dump');pre.classList.remove('tracy-collapsed');});});if(Dumper.inited){return;}Dumper.inited=true;document.documentElement.addEventListener('click',(e)=>{let el;if(e.ctrlKey&&(el=e.target.closest('[data-tracy-href]'))){location.href=el.getAttribute('data-tracy-href');return false;}if((el=e.target.closest('[data-tracy-snapshot]'))){let snapshot=JSON.parse(el.getAttribute('data-tracy-snapshot'));el.removeAttribute('data-tracy-snapshot');el.querySelectorAll('[data-tracy-dump]').forEach((toggler)=>{if(!toggler.nextSibling){toggler.after(document.createTextNode('\n'));}toggler.nextSibling.after(buildStruct(JSON.parse(toggler.getAttribute('data-tracy-dump')),snapshot,toggler,true,[]));toggler.removeAttribute('data-tracy-dump');});}});document.documentElement.addEventListener('tracy-toggle',(e)=>{if(!e.target.matches('.tracy-dump *')){return;}let cont=e.detail.relatedTarget;let origE=e.detail.originalEvent;if(origE&&origE.usedIds){toggleChildren(cont,origE.usedIds);return;}else if(origE&&origE.altKey&&cont.querySelector('.tracy-toggle')){if(e.detail.collapsed){e.target.classList.toggle('tracy-collapsed',false);cont.classList.toggle('tracy-collapsed',false);e.detail.collapsed=false;}let expand=e.target.tracyAltExpand=!e.target.tracyAltExpand;toggleChildren(cont,expand?{}:false);}cont.classList.toggle('tracy-dump-flash',!e.detail.collapsed);});document.documentElement.addEventListener('animationend',(e)=>{if(e.animationName==='tracy-dump-flash'){e.target.classList.toggle('tracy-dump-flash',false);}});document.addEventListener('mouseover',(e)=>{if(!e.target.matches('.tracy-dump *')){return;}let el;if(e.target.matches('.tracy-dump-hash')&&(el=e.target.closest('.tracy-dump'))){el.querySelectorAll('.tracy-dump-hash').forEach((el)=>{if(el.textContent===e.target.textContent){el.classList.add('tracy-dump-highlight');}});return;}if((el=e.target.closest('.tracy-toggle'))&&!el.title){el.title=HINT_ALT;}});document.addEventListener('mouseout',(e)=>{if(e.target.matches('.tracy-dump-hash')){document.querySelectorAll('.tracy-dump-hash.tracy-dump-highlight').forEach((el)=>{el.classList.remove('tracy-dump-highlight');});}});Tracy.Toggle.init();}}function build(data,repository,collapsed,parentIds,keyType){let id,type=data===null?'null':typeof data,collapseCount=collapsed===null?COLLAPSE_COUNT:COLLAPSE_COUNT_TOP;if(type==='null'||type==='number'||type==='boolean'){return createEl(null,null,[createEl('span',{'class':'tracy-dump-'+type.replace('ean','')},[data+''])]);}else if(type==='string'){data={string:data.replace(/&/g,'&amp;').replace(/</g,'&lt;'),length:[...data].length};}else if(Array.isArray(data)){data={array:null,items:data};}else if(data.ref){id=data.ref;data=repository[id];if(!data){throw new UnknownEntityException;}}if(data.string!==undefined||data.bin!==undefined){let s=data.string===undefined?data.bin:data.string;if(keyType===TYPE_ARRAY){return createEl(null,null,[createEl('span',{'class':'tracy-dump-string'},{html:'<span class="tracy-dump-lq">\'<\/span>'+s+'<span>\'<\/span>'}),]);}else if(keyType!==undefined){if(type!=='string'){s='<span class="tracy-dump-lq">\'<\/span>'+s+'<span>\'<\/span>';}const classes=['tracy-dump-public','tracy-dump-protected','tracy-dump-private','tracy-dump-dynamic','tracy-dump-virtual',];return createEl(null,null,[createEl('span',{'class':classes[typeof keyType==='string'?PROP_PRIVATE:keyType],'title':typeof keyType==='string'?'declared in '+keyType:null,},{html:s}),]);}let count=(s.match(/\n/g)||[]).length;if(count){let collapsed=count>=COLLAPSE_COUNT;return createEl(null,null,[createEl('span',{'class':collapsed?'tracy-toggle tracy-collapsed':'tracy-toggle'},['string']),'\n',createEl('div',{'class':'tracy-dump-string'+(collapsed?' tracy-collapsed':''),'title':data.length+(data.bin?' bytes':' characters'),},{html:'<span class="tracy-dump-lq">\'<\/span>'+s+'<span>\'<\/span>'}),]);}return createEl(null,null,[createEl('span',{'class':'tracy-dump-string','title':data.length+(data.bin?' bytes':' characters'),},{html:'<span>\'<\/span>'+s+'<span>\'<\/span>'}),]);}else if(data.number){return createEl(null,null,[createEl('span',{'class':'tracy-dump-number'},[data.number])]);}else if(data.text!==undefined){return createEl(null,null,[createEl('span',{class:'tracy-dump-virtual'},[data.text])]);}else{let pos,nameEl;nameEl=data.object&&(pos=data.object.lastIndexOf('\\'))>0?[data.object.substr(0,pos+1),createEl('b',null,[data.object.substr(pos+1)])]:[data.object||data.resource];let span=data.array!==undefined?[createEl('span',{'class':'tracy-dump-array'},['array']),' ('+(data.length||data.items.length)+')']:[createEl('span',{'class':data.object?'tracy-dump-object':'tracy-dump-resource',title:data.editor?'Declared in file '+data.editor.file+' on line '+data.editor.line+(data.editor.url?'\n'+HINT_CTRL:'')+'\n'+HINT_ALT:null,'data-tracy-href':data.editor?data.editor.url:null},nameEl),...(id?[' ',createEl('span',{'class':'tracy-dump-hash'},[data.resource?'@'+id.substr(1):'#'+id])]:[])];parentIds=parentIds?parentIds.slice():[];let recursive=id&&parentIds.indexOf(id)>-1;parentIds.push(id);if(recursive||!data.items||!data.items.length){span.push(recursive?' RECURSION':(!data.items||data.items.length?' …':''));return createEl(null,null,span);}collapsed=collapsed===true||data.collapsed||(data.items&&data.items.length>=collapseCount);let toggle=createEl('span',{'class':collapsed?'tracy-toggle tracy-collapsed':'tracy-toggle'},span);return createEl(null,null,[toggle,'\n',buildStruct(data,repository,toggle,collapsed,parentIds),]);}}function buildStruct(data,repository,toggle,collapsed,parentIds){if(Array.isArray(data)){data={items:data};}else if(data.ref){parentIds=parentIds.slice();parentIds.push(data.ref);data=repository[data.ref];}let cut=data.items&&data.length>data.items.length;let type=data.object?TYPE_OBJECT:data.resource?TYPE_RESOURCE:TYPE_ARRAY;let div=createEl('div',{'class':collapsed?'tracy-collapsed':null});if(collapsed){let handler;toggle.addEventListener('tracy-toggle',handler=function(){toggle.removeEventListener('tracy-toggle',handler);createItems(div,data.items,type,repository,parentIds,null);if(cut){createEl(div,null,['…\n']);}});}else{createItems(div,data.items,type,repository,parentIds,true);if(cut){createEl(div,null,['…\n']);}}return div;}function createEl(el,attrs,content){if(!(el instanceof Node)){el=el?document.createElement(el):document.createDocumentFragment();}for(let id in attrs||{}){if(attrs[id]!==null){el.setAttribute(id,attrs[id]);}}if(content&&content.html!==undefined){el.innerHTML=content.html;return el;}content=content||[];el.append(...content.filter((child)=>(child!==null)));return el;}function createItems(el,items,type,repository,parentIds,collapsed){let key,val,vis,ref,i,tmp;for(i=0;i<items.length;i++){if(type===TYPE_ARRAY){[key,val,ref]=items[i];}else{[key,val,vis=PROP_VIRTUAL,ref]=items[i];}createEl(el,null,[build(key,null,null,null,type===TYPE_ARRAY?TYPE_ARRAY:vis),type===TYPE_ARRAY?' => ':': ',...(ref?[createEl('span',{'class':'tracy-dump-hash'},['&'+ref]),' ']:[]),tmp=build(val,repository,collapsed,parentIds),tmp.lastElementChild.tagName==='DIV'?'':'\n',]);}}function toggleChildren(cont,usedIds){let hashEl,id;cont.querySelectorAll(':scope > .tracy-toggle').forEach((el)=>{hashEl=(el.querySelector('.tracy-dump-hash')||el.previousElementSibling);id=hashEl&&hashEl.matches('.tracy-dump-hash')?hashEl.textContent:null;if(!usedIds||(id&&usedIds[id])){Tracy.Toggle.toggle(el,false);}else{usedIds[id]=true;Tracy.Toggle.toggle(el,true,{usedIds:usedIds});}});}function UnknownEntityException(){}let Tracy=window.Tracy=window.Tracy||{};Tracy.Dumper=Tracy.Dumper||Dumper;function init(){Tracy.Dumper.init();}if(document.readyState==='loading'){document.addEventListener('DOMContentLoaded',init);}else{init();}})();(function(){class BlueScreen{static init(ajax){let blueScreen=document.getElementById('tracy-bs');if(navigator.platform.indexOf('Mac')>-1){blueScreen.classList.add('tracy-mac');}if(!ajax){document.body.appendChild(blueScreen);let id=location.href+document.querySelector('.tracy-section--error').textContent;Tracy.Toggle.persist(blueScreen,sessionStorage.getItem('tracy-toggles-bskey')===id);sessionStorage.setItem('tracy-toggles-bskey',id);}if(inited){return;}inited=true;document.addEventListener('keyup',(e)=>{if(e.keyCode===27&&!e.shiftKey&&!e.altKey&&!e.ctrlKey&&!e.metaKey){Tracy.Toggle.toggle(document.getElementById('tracy-bs-toggle'));}});blueScreen.addEventListener('tracy-toggle',(e)=>{if(!e.target.matches('.tracy-dump *')&&e.detail.originalEvent){e.detail.relatedTarget.classList.toggle('tracy-panel-fadein',!e.detail.collapsed);}});Tracy.TableSort.init();Tracy.Tabs.init();window.addEventListener('scroll',stickyFooter);(new ResizeObserver(stickyFooter)).observe(blueScreen);}static loadAjax(content){let ajaxBs=document.getElementById('tracy-bs');if(ajaxBs){ajaxBs.remove();}document.body.insertAdjacentHTML('beforeend',content);ajaxBs=document.getElementById('tracy-bs');Tracy.Dumper.init(ajaxBs);BlueScreen.init(true);window.scrollTo(0,0);}}function stickyFooter(){let footer=document.querySelector('#tracy-bs footer');footer.classList.toggle('tracy-footer--sticky',false);footer.classList.toggle('tracy-footer--sticky',footer.offsetHeight+footer.offsetTop-window.innerHeight-document.documentElement.scrollTop<0);}let inited;let Tracy=window.Tracy=window.Tracy||{};Tracy.BlueScreen=Tracy.BlueScreen||BlueScreen;})();Tracy.BlueScreen.init();
</script>
</body>
</html>
