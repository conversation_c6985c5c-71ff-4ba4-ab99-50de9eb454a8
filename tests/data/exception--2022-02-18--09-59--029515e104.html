<!DOCTYPE html><!-- "' --></textarea></script></style></pre></xmp></a></iframe></noembed></noframes></noscript></option></select></template></title></table>
<html>
<head>
	<meta charset="utf-8">
	<meta name="robots" content="noindex">
	<meta name="generator" content="Tracy by Nette Framework">

	<title>Warning: Undefined array key &quot;schema_tool_update_command&quot;</title>
	<!-- in /web/vendor/dek-apps/nette-doctrine/bin/console.php:133 -->

	<style class="tracy-debug">
	:root{--tracy-space: 16px}#tracy-bs{font: 9pt/1.5 Verdana,sans-serif;background: white;color: #333;position: absolute;z-index: 20000;left: 0;top: 0;width: 100%;text-align: left}#tracy-bs a{text-decoration: none;color: #328ADC;padding: 0 4px;margin: 0 -4px}#tracy-bs a+a{margin-left: 0}#tracy-bs a:hover,#tracy-bs a:focus{color: #085AA3}#tracy-bs-toggle{position: absolute;right: .5em;top: .5em;text-decoration: none;background: #CD1818;color: white!important;padding: 3px}.tracy-bs-main{display: flex;flex-direction: column;min-height: 100vh}.tracy-bs-main.tracy-collapsed{display: none}#tracy-bs .section:last-of-type{flex: 1}#tracy-bs p,#tracy-bs table,#tracy-bs pre,#tracy-bs h1,#tracy-bs h2,#tracy-bs h3{margin: 0 0 var(--tracy-space)}#tracy-bs h1{font-size: 15pt;font-weight: normal;text-shadow: 1px 1px 2px rgba(0,0,0,.3)}#tracy-bs h1 span{white-space: pre-wrap}#tracy-bs h2{font-size: 14pt;font-weight: normal;margin-top: var(--tracy-space)}#tracy-bs h3{font-size: 10pt;font-weight: bold}#tracy-bs pre,#tracy-bs code,#tracy-bs table{font: 9pt/1.5 Consolas,monospace!important}#tracy-bs pre,#tracy-bs table{background: #FDF5CE;padding: .4em .7em;border: 2px solid #ffffffa6;box-shadow: 1px 2px 6px #00000005;overflow: auto}#tracy-bs table pre{padding: 0;margin: 0;border: none;box-shadow: none}#tracy-bs table{border-collapse: collapse;width: 100%}#tracy-bs td,#tracy-bs th{vertical-align: top;text-align: left;padding: 2px 6px;border: 1px solid #e6dfbf}#tracy-bs th{font-weight: bold}#tracy-bs tr>:first-child{width: 20%}#tracy-bs tr:nth-child(2n),#tracy-bs tr:nth-child(2n) pre{background-color: #F7F0CB}#tracy-bs footer ul{font-size: 7pt;padding: var(--tracy-space);margin: var(--tracy-space) 0 0;color: #777;background: #F6F5F3;border-top: 1px solid #DDD;list-style: none}#tracy-bs .footer-logo a{position: absolute;bottom: 0;right: 0;width: 100px;height: 50px;background: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAFoAAAAUBAMAAAD/1DctAAAAMFBMVEWupZzj39rEvbTy8O3X0sz9/PvGwLu8tavQysHq6OS0rKP5+Pbd2dT29fPMxbzPx8DKErMJAAAACXBIWXMAAAsTAAALEwEAmpwYAAACGUlEQVQoFX3TQWgTQRQA0MWLIJJDYehBTykhG5ERTx56K1u8eEhCYtomE7x5L4iLh0ViF7egewuFFqSIYE6hIHsIYQ6CQSg9CDKn4QsNCRlB59C74J/ZNHW1+An5+bOPyf6/s46oz2P+A0yIeZZ2ieEHi6TOnLKTxvWq+b52mxlVO3xnM1s7xLX1504XQH65OnW2dBqn7cCkYsFsfYsWpyY/2salmFTpEyzeR8zosYqMdiPDXdyU52K1wgEa/SjGpdEwUAxqvRfckQCDOyFearsEHe2grvkh/cFAHKvdtI3lcVceKQIOFpv+FOZaNPQBwJZLPp+hfrvT5JZXaUFsR8zqQc9qSgAharkfS5M/5F6nGJJAtXq/eLr3ucZpHccSxOOIPaQhtHohpCH2Xu6rLmQ0djnr4/+J3C6v+AW8/XWYxwYNdlhWj/P5fPSTQwVr0T9lGxdaBCqErNZaqYnEwbkjEB3NasGF3lPdrHa1nnxNOMgj0+neePUPjd2v/qVvUv29ifvc19huQ48qwXShy/9o8o3OSk0cs37mOFd0Ydgvsf/oZEnPVtggfd66lORn9mDyyzXU13SRtH2L6aR5T/snGAcZPfAXz5J1YlJWBEuxdMYqQecpBrlM49xAbmqyHA+xlA1FxBtqT2xmJoNXZlIt74ZBLeJ9ZGDqByNI7p543idzJ23vXEv7IgnsxiS+eNtwNbFdLq7+Bi4wQ0I4SVb9AAAAAElFTkSuQmCC') no-repeat;opacity: .6;padding: 0;margin: 0}#tracy-bs .footer-logo a:hover,#tracy-bs .footer-logo a:focus{opacity: 1;transition: opacity 0.1s}#tracy-bs .section{padding-left: calc(1.5 * var(--tracy-space));padding-right: calc(1.5 * var(--tracy-space))}#tracy-bs .section-panel{background: #F4F3F1;padding: var(--tracy-space) var(--tracy-space) 0;margin: 0 0 var(--tracy-space);border-radius: 8px;box-shadow: inset 1px 1px 0px 0 #00000005;overflow: hidden}#tracy-bs .outer,#tracy-bs .pane{overflow: auto}#tracy-bs.mac .pane{padding-bottom: 12px}#tracy-bs .section--error{background: #CD1818;color: white;font-size: 13pt;padding-top: var(--tracy-space)}#tracy-bs .section--error h1{color: white}#tracy-bs .section--error::selection,#tracy-bs .section--error ::selection{color: black!important;background: #FDF5CE!important}#tracy-bs .section--error a{color: #ffefa1!important}#tracy-bs .section--error span span{font-size: 80%;color: rgba(255,255,255,0.5);text-shadow: none}#tracy-bs .section--error a.action{color: white!important;opacity: 0;font-size: .7em;border-bottom: none!important}#tracy-bs .section--error:hover a.action{opacity: .6}#tracy-bs .section--error a.action:hover{opacity: 1}#tracy-bs .section--error i{color: #ffefa1;font-style: normal}#tracy-bs pre.code>div{min-width: 100%;float: left;white-space: pre}#tracy-bs .highlight{background: #CD1818;color: white;font-weight: bold;font-style: normal;display: block;padding: 0 .4em;margin: 0 -.4em}#tracy-bs .line{color: #9F9C7F;font-weight: normal;font-style: normal}#tracy-bs a.tracy-editor{color: inherit;border-bottom: 1px dotted rgba(0,0,0,.3);border-radius: 3px}#tracy-bs a.tracy-editor:hover{background: #0001}#tracy-bs span[data-tracy-href]{border-bottom: 1px dotted rgba(0,0,0,.3)}#tracy-bs .tracy-dump-whitespace{color: #0003}#tracy-bs .caused{float: right;padding: .3em .6em;background: #df8075;border-radius: 0 0 0 8px;white-space: nowrap}#tracy-bs .caused a{color: white}#tracy-bs .callstack{display: grid;grid-template-columns: max-content 1fr;margin-bottom: calc(.5 * var(--tracy-space))}#tracy-bs .callstack-file{text-align: right;padding-right: var(--tracy-space);white-space: nowrap;height: calc(1.5 * var(--tracy-space))}#tracy-bs .callstack-callee{white-space: nowrap;height: calc(1.5 * var(--tracy-space))}#tracy-bs .callstack-additional{grid-column-start: 1;grid-column-end: 3}#tracy-bs .args tr:first-child>*{position: relative}#tracy-bs .args tr:first-child td:before{position: absolute;right: .3em;content:'may not be true';opacity: .4}#tracy-bs .panel-fadein{animation: panel-fadein .12s ease}@keyframes panel-fadein{0%{opacity: 0}}#tracy-bs .section--causedby{flex-direction: column;padding: 0}#tracy-bs .section--causedby:not(.tracy-collapsed){display: flex}#tracy-bs .section--causedby .section--error{background: #cd1818a6}#tracy-bs .tabs-bar{display: flex;list-style: none;padding-left: 0;margin: 0;width: 100%;font-size: 110%}#tracy-bs .tabs-bar>*:not(:first-child){margin-left: var(--tracy-space)}#tracy-bs .tabs-bar a{display: block;padding: calc(.5 * var(--tracy-space)) var(--tracy-space);margin: 0;height: 100%;box-sizing: border-box;border-radius: 5px 5px 0 0;text-decoration: none;transition: all 0.1s}#tracy-bs .tabs-bar>.tracy-active a{background: white}#tracy-bs .tabs-panel{border-top: 2px solid white;padding-top: var(--tracy-space);overflow: auto}.tracy-collapsed{display: none}.tracy-toggle.tracy-collapsed{display: inline}.tracy-toggle{cursor: pointer;user-select: none;white-space: nowrap}.tracy-toggle:after{content:'';display: inline-block;vertical-align: middle;line-height: 0;border-top: .6ex solid;border-right: .6ex solid transparent;border-left: .6ex solid transparent;transform: scale(1,1.5);margin: 0 .2ex 0 .7ex;transition: .1s transform;opacity: .5}.tracy-toggle.tracy-collapsed:after{transform: rotate(-90deg) scale(1,1.5) translate(.1ex,0)}.tracy-sortable>:first-child>tr:first-child>*{position: relative}.tracy-sortable>:first-child>tr:first-child>*:hover:before{position: absolute;right: .3em;content:"\21C5";opacity: .4;font-weight: normal}.tracy-tab-label{user-select: none}.tracy-tab-panel:not(.tracy-active){display: none}.tracy-dump.tracy-light{text-align: left;color: #444;background: #fdf9e2;border-radius: 4px;padding: 1em;margin: 1em 0;word-break: break-all;white-space: pre-wrap}.tracy-dump.tracy-light div{padding-left: 2.5ex}.tracy-dump.tracy-light div div{border-left: 1px solid rgba(0,0,0,.1);margin-left: .5ex}.tracy-dump.tracy-light div div:hover{border-left-color: rgba(0,0,0,.25)}.tracy-light .tracy-dump-location{color: gray;font-size: 80%;text-decoration: none;background: none;opacity: .5;float: right;cursor: pointer}.tracy-light .tracy-dump-location:hover,.tracy-light .tracy-dump-location:focus{opacity: 1}.tracy-light .tracy-dump-array,.tracy-light .tracy-dump-object{color: #C22;user-select: text}.tracy-light .tracy-dump-string{color: #35D;white-space: break-spaces}.tracy-light div.tracy-dump-string{position: relative;padding-left: 3.5ex}.tracy-light .tracy-dump-lq{margin-left: calc(-1ex - 1px)}.tracy-light div.tracy-dump-string:before{content:'';position: absolute;left: calc(3ex - 1px);top: 1.5em;bottom: 0;border-left: 1px solid rgba(0,0,0,.1)}.tracy-light .tracy-dump-virtual span,.tracy-light .tracy-dump-dynamic span,.tracy-light .tracy-dump-string span{color: rgba(0,0,0,0.5)}.tracy-light .tracy-dump-virtual i,.tracy-light .tracy-dump-dynamic i,.tracy-light .tracy-dump-string i{font-size: 80%;font-style: normal;color: rgba(0,0,0,0.5);user-select: none}.tracy-light .tracy-dump-number{color: #090}.tracy-light .tracy-dump-null,.tracy-light .tracy-dump-bool{color: #850}.tracy-light .tracy-dump-virtual{font-style: italic}.tracy-light .tracy-dump-public::after{content:' pub'}.tracy-light .tracy-dump-protected::after{content:' pro'}.tracy-light .tracy-dump-private::after{content:' pri'}.tracy-light .tracy-dump-public::after,.tracy-light .tracy-dump-protected::after,.tracy-light .tracy-dump-private::after,.tracy-light .tracy-dump-hash{font-size: 85%;color: rgba(0,0,0,0.5)}.tracy-light .tracy-dump-indent{display: none}.tracy-light .tracy-dump-highlight{background: #C22;color: white;border-radius: 2px;padding: 0 2px;margin: 0 -2px}span[data-tracy-href]{border-bottom: 1px dotted rgba(0,0,0,.2)}.tracy-light .tracy-dump-flash{animation: tracy-dump-flash .2s ease}@keyframes tracy-dump-flash{0%{background: #c0c0c033}}	</style>
</head>


<body>
<div id="tracy-bs" itemscope>
	<a id="tracy-bs-toggle" href="#" class="tracy-toggle"></a>
	<div class="tracy-bs-main">
<section class="section section--error">
	<p>Warning</p>

	<h1><span>Undefined array key <i>"schema_tool_update_command"</i></span>
	</h1>
</section>



<section class="section">
	<h2 class="section-label"><a href="#" data-tracy-ref="^+" class="tracy-toggle">Source file</a></h2>

	<div class="section-panel">
		<p><b>File:</b> <a href="editor://open/?file=%2Fweb%2Fvendor%2Fdek-apps%2Fnette-doctrine%2Fbin%2Fconsole.php&amp;line=133&amp;search=&amp;replace=" title="/web/vendor/dek-apps/nette-doctrine/bin/console.php:133" class="tracy-editor">.../web/vendor/dek-apps/nette-doctrine/bin/<b>console.php</b>:133</a></p>
		<pre title="Ctrl-Click to open in editor" data-tracy-href="editor://open/?file=%2Fweb%2Fvendor%2Fdek-apps%2Fnette-doctrine%2Fbin%2Fconsole.php&amp;line=133&amp;search=&amp;replace=" class='code'><div><code><span style="color: #06B"><span style="color: #D24; font-weight: bold"><span class='line'>123:</span>                new </span><span style="color: #000">ORMConsole\Command\GenerateEntitiesCommand</span><span style="color: #D24; font-weight: bold">(),
<span class='line'>124:</span>                new </span><span style="color: #000">ORMConsole\Command\GenerateProxiesCommand</span><span style="color: #D24; font-weight: bold">(),
<span class='line'>125:</span>                new </span><span style="color: #000">ORMConsole\Command\ConvertMappingCommand</span><span style="color: #D24; font-weight: bold">(),
<span class='line'>126:</span>                new </span><span style="color: #000">ORMConsole\Command\RunDqlCommand</span><span style="color: #D24; font-weight: bold">(),
<span class='line'>127:</span>                new </span><span style="color: #000">ORMConsole\Command\ValidateSchemaCommand</span><span style="color: #D24; font-weight: bold">(),
<span class='line'>128:</span>                new </span><span style="color: #000">ORMConsole\Command\InfoCommand</span><span style="color: #D24; font-weight: bold">(),
<span class='line'>129:</span>                new </span><span style="color: #000">ORMConsole\Command\MappingDescribeCommand</span><span style="color: #D24; font-weight: bold">(),
<span class='line'>130:</span>            ]
<span class='line'>131:</span>        );
<span class='line'>132:</span>        
<span class='highlight'>133:        if ($parameters['orm']['schema_tool_update_command'] || !$migrations) {
</span></span><span style="color: #000"></span><span style="color: #D24; font-weight: bold"></span><span style="color: #080"></span><span style="color: #D24; font-weight: bold"></span><span style="color: #080"></span><span style="color: #D24; font-weight: bold"></span><span style="color: #000"></span><span style="color: #D24; font-weight: bold"><span class='line'>134:</span>            </span><span style="color: #000">$application</span><span style="color: #D24; font-weight: bold">-&gt;</span><span style="color: #000">add</span><span style="color: #D24; font-weight: bold">(new </span><span style="color: #000">ORMConsole\Command\SchemaTool\UpdateCommand</span><span style="color: #D24; font-weight: bold">());
<span class='line'>135:</span>        }
<span class='line'>136:</span>    }
<span class='line'>137:</span>    
</span></span></code></div></pre>
	</div>
</section>

<section class="section">
	<h2 class="section-label"><a href="#" data-tracy-ref="^+" class="tracy-toggle">Call stack</a></h2>

	<div class="section-panel">
	<div class="callstack">

		<div class="callstack-file">
			<a href="editor://open/?file=%2Fweb%2Fvendor%2Fdek-apps%2Fnette-doctrine%2Fbin%2Fconsole&amp;line=4&amp;search=&amp;replace=" title="/web/vendor/dek-apps/nette-doctrine/bin/console:4" class="tracy-editor">/web/vendor/dek-apps/nette-doctrine/bin/<b>console</b>:4</a>
		</div>

		<div class="callstack-callee">
			<a href="#" data-tracy-ref="^div + div" class="tracy-toggle tracy-collapsed"><b>include</b> (...)</a>
		</div>

		<div class="callstack-additional tracy-collapsed">
			<pre title="Ctrl-Click to open in editor" data-tracy-href="editor://open/?file=%2Fweb%2Fvendor%2Fdek-apps%2Fnette-doctrine%2Fbin%2Fconsole&amp;line=4&amp;search=&amp;replace=" class='code'><div><code><span style="color: #06B"><span class='line'>1:</span>    #!/usr/bin/env php
<span class='line'>2:</span>    <span style="color: #000">&lt;?php
<span class='line'>3:</span>    
<span class='highlight'>4:    include('console.php');
</span></span><span style="color: #D24; font-weight: bold"></span><span style="color: #080"></span><span style="color: #D24; font-weight: bold"><span class='line'>5:</span>    </span>
</span></code></div></pre>

			<table class="args">
<tr><th>#0</th><td><pre class="tracy-dump tracy-light"
><span class="tracy-dump-string" title="51 characters"><span>'</span>/web/vendor/dek-apps/nette-doctrine/bin/console.php<span>'</span></span></pre>
</td></tr>
			</table>
		</div>

		<div class="callstack-file">
			<a href="editor://open/?file=%2Fweb%2Fvendor%2Fbin%2Fconsole&amp;line=107&amp;search=&amp;replace=" title="/web/vendor/bin/console:107" class="tracy-editor">/web/vendor/bin/<b>console</b>:107</a>
		</div>

		<div class="callstack-callee">
			<a href="#" data-tracy-ref="^div + div" class="tracy-toggle tracy-collapsed"><b>include</b> (...)</a>
		</div>

		<div class="callstack-additional tracy-collapsed">
			<pre title="Ctrl-Click to open in editor" data-tracy-href="editor://open/?file=%2Fweb%2Fvendor%2Fbin%2Fconsole&amp;line=107&amp;search=&amp;replace=" class='code'><div><code><span style="color: #06B"><span style="color: #D24; font-weight: bold"><span class='line'> 97:</span>                }
<span class='line'> 98:</span>            }
<span class='line'> 99:</span>        }
<span class='line'>100:</span>    
<span class='line'>101:</span>        if (</span><span style="color: #000">function_exists</span><span style="color: #D24; font-weight: bold">(</span><span style="color: #080">'stream_wrapper_register'</span><span style="color: #D24; font-weight: bold">) &amp;&amp; </span><span style="color: #000">stream_wrapper_register</span><span style="color: #D24; font-weight: bold">(</span><span style="color: #080">'phpvfscomposer'</span><span style="color: #D24; font-weight: bold">, </span><span style="color: #080">'Composer\BinProxyWrapper'</span><span style="color: #D24; font-weight: bold">)) {
<span class='line'>102:</span>            include(</span><span style="color: #080">"phpvfscomposer://" </span><span style="color: #D24; font-weight: bold">. </span><span style="color: #000">__DIR__ </span><span style="color: #D24; font-weight: bold">. </span><span style="color: #080">'/..'</span><span style="color: #D24; font-weight: bold">.</span><span style="color: #080">'/dek-apps/nette-doctrine/bin/console'</span><span style="color: #D24; font-weight: bold">);
<span class='line'>103:</span>            exit(</span><span style="color: #000">0</span><span style="color: #D24; font-weight: bold">);
<span class='line'>104:</span>        }
<span class='line'>105:</span>    }
<span class='line'>106:</span>    
<span class='highlight'>107:    include __DIR__ . '/..'.'/dek-apps/nette-doctrine/bin/console';
</span></span><span style="color: #000"></span><span style="color: #D24; font-weight: bold"></span><span style="color: #080"></span><span style="color: #D24; font-weight: bold"></span><span style="color: #080"></span><span style="color: #D24; font-weight: bold"><span class='line'>108:</span>    </span>
</span></code></div></pre>

			<table class="args">
<tr><th>#0</th><td><pre class="tracy-dump tracy-light"
><span class="tracy-dump-string" title="47 characters"><span>'</span>/web/vendor/dek-apps/nette-doctrine/bin/console<span>'</span></span></pre>
</td></tr>
			</table>
		</div>
	</div>
	</div>
</section>


<section class="section">
	<h2 class="section-label"><a href="#" data-tracy-ref="^+" class="tracy-toggle tracy-collapsed">Exception</a></h2>
	<div class="section-panel tracy-collapsed">
		<pre class="tracy-dump tracy-light" data-tracy-dump='{"ref":574}'></pre>
	</div>
</section>




<section class="section">
	<h2 class="section-label"><a href="#" data-tracy-ref="^+" class="tracy-toggle tracy-collapsed">Environment</a></h2>

	<div class="section-panel tracy-collapsed">

	<div class="tracy-tabs">
		<ul class="tabs-bar">
			<li class="tracy-tab-label tracy-active"><a href="#">$_SERVER</a></li>
			<li class="tracy-tab-label"><a href="#">Constants</a></li>
			<li class="tracy-tab-label"><a href="#">Configuration</a></li>

		</ul>


		<div>
			<div class="tracy-tab-panel pane tracy-active">
				<table class="tracy-sortable">
				<tr><th>no_proxy</th><td><pre class="tracy-dump tracy-light"
><span class="tracy-dump-string" title="29 characters"><span>'</span>composer.dek.cz,debian.dek.cz<span>'</span></span></pre>
</td></tr>
				<tr><th>HOSTNAME</th><td><pre class="tracy-dump tracy-light"
><span class="tracy-dump-string" title="12 characters"><span>'</span>9246855c23c5<span>'</span></span></pre>
</td></tr>
				<tr><th>PHP_VERSION</th><td><pre class="tracy-dump tracy-light"><span class="tracy-dump-string" title="6 characters"><span>'</span>8.0.14<span>'</span></span></pre>
</td></tr>
				<tr><th>PHP_MD5</th><td><pre class="tracy-dump tracy-light"><span class="tracy-dump-string"><span>'</span><span>'</span></span></pre>
</td></tr>
				<tr><th>PHP_INI_DIR</th><td><pre class="tracy-dump tracy-light"
><span class="tracy-dump-string" title="18 characters"><span>'</span>/usr/local/etc/php<span>'</span></span></pre>
</td></tr>
				<tr><th>GPG_KEYS</th><td><pre class="tracy-dump tracy-light"
><span class="tracy-dump-string" title="81 characters"><span>'</span>1729F83938DA44E27BA0F4D3DBDB397470D12172 BFDDD28642824F8118EF77909B67A5C12229118F<span>'</span></span></pre>
</td></tr>
				<tr><th>PHP_LDFLAGS</th><td><pre class="tracy-dump tracy-light"
><span class="tracy-dump-string" title="12 characters"><span>'</span>-Wl,-O1 -pie<span>'</span></span></pre>
</td></tr>
				<tr><th>PWD</th><td><pre class="tracy-dump tracy-light" data-tracy-dump='{"text":"***** (string)"}'></pre>
</td></tr>
				<tr><th>HTTPD_VERSION</th><td><pre class="tracy-dump tracy-light"><span class="tracy-dump-string" title="6 characters"><span>'</span>2.4.52<span>'</span></span></pre>
</td></tr>
				<tr><th>TZ</th><td><pre class="tracy-dump tracy-light"
><span class="tracy-dump-string" title="13 characters"><span>'</span>Europe/Prague<span>'</span></span></pre>
</td></tr>
				<tr><th>HOME</th><td><pre class="tracy-dump tracy-light"><span class="tracy-dump-string" title="9 characters"><span>'</span>/home/<USER>'</span></span></pre>
</td></tr>
				<tr><th>https_proxy</th><td><pre class="tracy-dump tracy-light"
><span class="tracy-dump-string" title="24 characters"><span>'</span>http://proxy.dek.cz:3128<span>'</span></span></pre>
</td></tr>
				<tr><th>PHP_SHA256</th><td><pre class="tracy-dump tracy-light"
><span class="tracy-dump-string" title="64 characters"><span>'</span>fbde8247ac200e4de73449d9fefc8b495d323b5be9c10cdb645fb431c91156e3<span>'</span></span></pre>
</td></tr>
				<tr><th>APACHE_PID_FILE</th><td><pre class="tracy-dump tracy-light"
><span class="tracy-dump-string" title="16 characters"><span>'</span>/tmp/apache2.pid<span>'</span></span></pre>
</td></tr>
				<tr><th>PHPIZE_DEPS</th><td><pre class="tracy-dump tracy-light" data-tracy-dump='{"string":"autoconf <i>\\t</i>    <i>\\t</i>    dpkg-dev <i>\\t</i>    <i>\\t</i>    file <i>\\t</i>    <i>\\t</i>    g++ <i>\\t</i>    <i>\\t</i>    gcc <i>\\t</i>    <i>\\t</i>    libc-dev <i>\\t</i>    <i>\\t</i>    make <i>\\t</i>    <i>\\t</i>    pkg-config <i>\\t</i>    <i>\\t</i>    re2c","length":76}'></pre>
</td></tr>
				<tr><th>HTTPD_PATCHES</th><td><pre class="tracy-dump tracy-light"><span class="tracy-dump-string"><span>'</span><span>'</span></span></pre>
</td></tr>
				<tr><th>TERM</th><td><pre class="tracy-dump tracy-light"><span class="tracy-dump-string" title="5 characters"><span>'</span>xterm<span>'</span></span></pre>
</td></tr>
				<tr><th>HOST</th><td><pre class="tracy-dump tracy-light"
><span class="tracy-dump-string" title="24 characters"><span>'</span>zapisy-porady.mik.dek.cz<span>'</span></span></pre>
</td></tr>
				<tr><th>PHP_URL</th><td><pre class="tracy-dump tracy-light"
><span class="tracy-dump-string" title="51 characters"><span>'</span>https://www.php.net/distributions/php-8.0.14.tar.xz<span>'</span></span></pre>
</td></tr>
				<tr><th>HTTPD_SHA256</th><td><pre class="tracy-dump tracy-light"
><span class="tracy-dump-string" title="64 characters"><span>'</span>0127f7dc497e9983e9c51474bed75e45607f2f870a7675a86dc90af6d572f5c9<span>'</span></span></pre>
</td></tr>
				<tr><th>APACHE_RUN_GROUP</th><td><pre class="tracy-dump tracy-light"><span class="tracy-dump-string" title="3 characters"><span>'</span>mik<span>'</span></span></pre>
</td></tr>
				<tr><th>PHP_EXTRA_CONFIGURE_ARGS</th><td><pre class="tracy-dump tracy-light"
><span class="tracy-dump-string" title="26 characters"><span>'</span>--with-apxs2 --disable-cgi<span>'</span></span></pre>
</td></tr>
				<tr><th>SHLVL</th><td><pre class="tracy-dump tracy-light"><span class="tracy-dump-string"><span>'</span>1<span>'</span></span></pre>
</td></tr>
				<tr><th>COMPOSER_VERSION</th><td><pre class="tracy-dump tracy-light"><span class="tracy-dump-string" title="5 characters"><span>'</span>2.2.3<span>'</span></span></pre>
</td></tr>
				<tr><th>PHP_CFLAGS</th><td><pre class="tracy-dump tracy-light"
><span class="tracy-dump-string" title="83 characters"><span>'</span>-fstack-protector-strong -fpic -fpie -O2 -D_LARGEFILE_SOURCE -D_FILE_OFFSET_BITS=64<span>'</span></span></pre>
</td></tr>
				<tr><th>http_proxy</th><td><pre class="tracy-dump tracy-light"
><span class="tracy-dump-string" title="24 characters"><span>'</span>http://proxy.dek.cz:3128<span>'</span></span></pre>
</td></tr>
				<tr><th>APACHE_RUN_USER</th><td><pre class="tracy-dump tracy-light"><span class="tracy-dump-string" title="3 characters"><span>'</span>mik<span>'</span></span></pre>
</td></tr>
				<tr><th>PATH</th><td><pre class="tracy-dump tracy-light"
><span class="tracy-dump-string" title="83 characters"><span>'</span>/usr/local/apache2/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin<span>'</span></span></pre>
</td></tr>
				<tr><th>PHP_ASC_URL</th><td><pre class="tracy-dump tracy-light"
><span class="tracy-dump-string" title="55 characters"><span>'</span>https://www.php.net/distributions/php-8.0.14.tar.xz.asc<span>'</span></span></pre>
</td></tr>
				<tr><th>PHP_CPPFLAGS</th><td><pre class="tracy-dump tracy-light"
><span class="tracy-dump-string" title="83 characters"><span>'</span>-fstack-protector-strong -fpic -fpie -O2 -D_LARGEFILE_SOURCE -D_FILE_OFFSET_BITS=64<span>'</span></span></pre>
</td></tr>
				<tr><th>HTTPD_PREFIX</th><td><pre class="tracy-dump tracy-light"
><span class="tracy-dump-string" title="18 characters"><span>'</span>/usr/local/apache2<span>'</span></span></pre>
</td></tr>
				<tr><th>_</th><td><pre class="tracy-dump tracy-light"
><span class="tracy-dump-string" title="18 characters"><span>'</span>/usr/local/bin/php<span>'</span></span></pre>
</td></tr>
				<tr><th>PHP_SELF</th><td><pre class="tracy-dump tracy-light"
><span class="tracy-dump-string" title="18 characters"><span>'</span>vendor/bin/console<span>'</span></span></pre>
</td></tr>
				<tr><th>SCRIPT_NAME</th><td><pre class="tracy-dump tracy-light"
><span class="tracy-dump-string" title="18 characters"><span>'</span>vendor/bin/console<span>'</span></span></pre>
</td></tr>
				<tr><th>SCRIPT_FILENAME</th><td><pre class="tracy-dump tracy-light"
><span class="tracy-dump-string" title="18 characters"><span>'</span>vendor/bin/console<span>'</span></span></pre>
</td></tr>
				<tr><th>PATH_TRANSLATED</th><td><pre class="tracy-dump tracy-light"
><span class="tracy-dump-string" title="18 characters"><span>'</span>vendor/bin/console<span>'</span></span></pre>
</td></tr>
				<tr><th>DOCUMENT_ROOT</th><td><pre class="tracy-dump tracy-light"><span class="tracy-dump-string"><span>'</span><span>'</span></span></pre>
</td></tr>
				<tr><th>REQUEST_TIME_FLOAT</th><td><pre class="tracy-dump tracy-light"><span class="tracy-dump-number">1645178375.63415</span></pre>
</td></tr>
				<tr><th>REQUEST_TIME</th><td><pre class="tracy-dump tracy-light"><span class="tracy-dump-number">1645178375</span></pre>
</td></tr>
				<tr><th>argv</th><td><pre class="tracy-dump tracy-light" data-tracy-dump='[[0,"vendor/bin/console"]]'></pre>
</td></tr>
				<tr><th>argc</th><td><pre class="tracy-dump tracy-light"><span class="tracy-dump-number">1</span></pre>
</td></tr>
				<tr><th>APP_NAME</th><td><pre class="tracy-dump tracy-light"><span class="tracy-dump-string" title="7 characters"><span>'</span>Laravel<span>'</span></span></pre>
</td></tr>
				<tr><th>APP_ENV</th><td><pre class="tracy-dump tracy-light"><span class="tracy-dump-string" title="5 characters"><span>'</span>local<span>'</span></span></pre>
</td></tr>
				<tr><th>APP_KEY</th><td><pre class="tracy-dump tracy-light"
><span class="tracy-dump-string" title="51 characters"><span>'</span>base64:UtsxnZZlc++dy34C5pMIlyW8uMYAVIHrx6fB7IhB8Pc=<span>'</span></span></pre>
</td></tr>
				<tr><th>APP_DEBUG</th><td><pre class="tracy-dump tracy-light"><span class="tracy-dump-string" title="4 characters"><span>'</span>true<span>'</span></span></pre>
</td></tr>
				<tr><th>APP_URL</th><td><pre class="tracy-dump tracy-light"
><span class="tracy-dump-string" title="16 characters"><span>'</span>http://localhost<span>'</span></span></pre>
</td></tr>
				<tr><th>LOG_CHANNEL</th><td><pre class="tracy-dump tracy-light"><span class="tracy-dump-string" title="5 characters"><span>'</span>stack<span>'</span></span></pre>
</td></tr>
				<tr><th>LOG_DEPRECATIONS_CHANNEL</th><td><pre class="tracy-dump tracy-light"><span class="tracy-dump-string" title="4 characters"><span>'</span>null<span>'</span></span></pre>
</td></tr>
				<tr><th>LOG_LEVEL</th><td><pre class="tracy-dump tracy-light"><span class="tracy-dump-string" title="5 characters"><span>'</span>debug<span>'</span></span></pre>
</td></tr>
				<tr><th>DB_CONNECTION</th><td><pre class="tracy-dump tracy-light"><span class="tracy-dump-string" title="5 characters"><span>'</span>mysql<span>'</span></span></pre>
</td></tr>
				<tr><th>DB_HOST</th><td><pre class="tracy-dump tracy-light"
><span class="tracy-dump-string" title="18 characters"><span>'</span>mariadbtest.dek.cz<span>'</span></span></pre>
</td></tr>
				<tr><th>DB_PORT</th><td><pre class="tracy-dump tracy-light"><span class="tracy-dump-string" title="4 characters"><span>'</span>3306<span>'</span></span></pre>
</td></tr>
				<tr><th>DB_DATABASE</th><td><pre class="tracy-dump tracy-light"
><span class="tracy-dump-string" title="12 characters"><span>'</span>zapisyporady<span>'</span></span></pre>
</td></tr>
				<tr><th>DB_USERNAME</th><td><pre class="tracy-dump tracy-light"
><span class="tracy-dump-string" title="12 characters"><span>'</span>zapisyporady<span>'</span></span></pre>
</td></tr>
				<tr><th>DB_PASSWORD</th><td><pre class="tracy-dump tracy-light"
><span class="tracy-dump-string" title="25 characters"><span>'</span>rcphVbudHaEQfBjq4NWv7tnwC<span>'</span></span></pre>
</td></tr>
				<tr><th>BROADCAST_DRIVER</th><td><pre class="tracy-dump tracy-light"><span class="tracy-dump-string" title="3 characters"><span>'</span>log<span>'</span></span></pre>
</td></tr>
				<tr><th>CACHE_DRIVER</th><td><pre class="tracy-dump tracy-light"><span class="tracy-dump-string" title="4 characters"><span>'</span>file<span>'</span></span></pre>
</td></tr>
				<tr><th>FILESYSTEM_DISK</th><td><pre class="tracy-dump tracy-light"><span class="tracy-dump-string" title="5 characters"><span>'</span>local<span>'</span></span></pre>
</td></tr>
				<tr><th>QUEUE_CONNECTION</th><td><pre class="tracy-dump tracy-light"><span class="tracy-dump-string" title="4 characters"><span>'</span>sync<span>'</span></span></pre>
</td></tr>
				<tr><th>SESSION_DRIVER</th><td><pre class="tracy-dump tracy-light"><span class="tracy-dump-string" title="4 characters"><span>'</span>file<span>'</span></span></pre>
</td></tr>
				<tr><th>SESSION_LIFETIME</th><td><pre class="tracy-dump tracy-light"><span class="tracy-dump-string" title="3 characters"><span>'</span>120<span>'</span></span></pre>
</td></tr>
				<tr><th>MEMCACHED_HOST</th><td><pre class="tracy-dump tracy-light"><span class="tracy-dump-string" title="9 characters"><span>'</span>127.0.0.1<span>'</span></span></pre>
</td></tr>
				<tr><th>REDIS_HOST</th><td><pre class="tracy-dump tracy-light"><span class="tracy-dump-string" title="9 characters"><span>'</span>127.0.0.1<span>'</span></span></pre>
</td></tr>
				<tr><th>REDIS_PASSWORD</th><td><pre class="tracy-dump tracy-light"><span class="tracy-dump-string" title="4 characters"><span>'</span>null<span>'</span></span></pre>
</td></tr>
				<tr><th>REDIS_PORT</th><td><pre class="tracy-dump tracy-light"><span class="tracy-dump-string" title="4 characters"><span>'</span>6379<span>'</span></span></pre>
</td></tr>
				<tr><th>MAIL_MAILER</th><td><pre class="tracy-dump tracy-light"><span class="tracy-dump-string" title="4 characters"><span>'</span>smtp<span>'</span></span></pre>
</td></tr>
				<tr><th>MAIL_HOST</th><td><pre class="tracy-dump tracy-light"><span class="tracy-dump-string" title="7 characters"><span>'</span>mailhog<span>'</span></span></pre>
</td></tr>
				<tr><th>MAIL_PORT</th><td><pre class="tracy-dump tracy-light"><span class="tracy-dump-string" title="4 characters"><span>'</span>1025<span>'</span></span></pre>
</td></tr>
				<tr><th>MAIL_USERNAME</th><td><pre class="tracy-dump tracy-light"><span class="tracy-dump-string" title="4 characters"><span>'</span>null<span>'</span></span></pre>
</td></tr>
				<tr><th>MAIL_PASSWORD</th><td><pre class="tracy-dump tracy-light"><span class="tracy-dump-string" title="4 characters"><span>'</span>null<span>'</span></span></pre>
</td></tr>
				<tr><th>MAIL_ENCRYPTION</th><td><pre class="tracy-dump tracy-light"><span class="tracy-dump-string" title="4 characters"><span>'</span>null<span>'</span></span></pre>
</td></tr>
				<tr><th>MAIL_FROM_ADDRESS</th><td><pre class="tracy-dump tracy-light"><span class="tracy-dump-string" title="4 characters"><span>'</span>null<span>'</span></span></pre>
</td></tr>
				<tr><th>MAIL_FROM_NAME</th><td><pre class="tracy-dump tracy-light"><span class="tracy-dump-string" title="7 characters"><span>'</span>Laravel<span>'</span></span></pre>
</td></tr>
				<tr><th>AWS_ACCESS_KEY_ID</th><td><pre class="tracy-dump tracy-light"><span class="tracy-dump-string"><span>'</span><span>'</span></span></pre>
</td></tr>
				<tr><th>AWS_SECRET_ACCESS_KEY</th><td><pre class="tracy-dump tracy-light"><span class="tracy-dump-string"><span>'</span><span>'</span></span></pre>
</td></tr>
				<tr><th>AWS_DEFAULT_REGION</th><td><pre class="tracy-dump tracy-light"><span class="tracy-dump-string" title="9 characters"><span>'</span>us-east-1<span>'</span></span></pre>
</td></tr>
				<tr><th>AWS_BUCKET</th><td><pre class="tracy-dump tracy-light"><span class="tracy-dump-string"><span>'</span><span>'</span></span></pre>
</td></tr>
				<tr><th>AWS_USE_PATH_STYLE_ENDPOINT</th><td><pre class="tracy-dump tracy-light"><span class="tracy-dump-string" title="5 characters"><span>'</span>false<span>'</span></span></pre>
</td></tr>
				<tr><th>PUSHER_APP_ID</th><td><pre class="tracy-dump tracy-light"><span class="tracy-dump-string"><span>'</span><span>'</span></span></pre>
</td></tr>
				<tr><th>PUSHER_APP_KEY</th><td><pre class="tracy-dump tracy-light"><span class="tracy-dump-string"><span>'</span><span>'</span></span></pre>
</td></tr>
				<tr><th>PUSHER_APP_SECRET</th><td><pre class="tracy-dump tracy-light"><span class="tracy-dump-string"><span>'</span><span>'</span></span></pre>
</td></tr>
				<tr><th>PUSHER_APP_CLUSTER</th><td><pre class="tracy-dump tracy-light"><span class="tracy-dump-string" title="3 characters"><span>'</span>mt1<span>'</span></span></pre>
</td></tr>
				<tr><th>MIX_PUSHER_APP_KEY</th><td><pre class="tracy-dump tracy-light"><span class="tracy-dump-string"><span>'</span><span>'</span></span></pre>
</td></tr>
				<tr><th>MIX_PUSHER_APP_CLUSTER</th><td><pre class="tracy-dump tracy-light"><span class="tracy-dump-string" title="3 characters"><span>'</span>mt1<span>'</span></span></pre>
</td></tr>
				</table>
			</div>





			<div class="tracy-tab-panel pane">
				<table class="tracy-sortable">
					<tr><th>MYSQLI_REFRESH_REPLICA</th><td><pre class="tracy-dump tracy-light"><span class="tracy-dump-number">64</span></pre>
</td></tr>
					<tr><th>ARTISAN_BINARY</th><td><pre class="tracy-dump tracy-light"><span class="tracy-dump-string" title="7 characters"><span>'</span>artisan<span>'</span></span></pre>
</td></tr>
				</table>
			</div>


			<div class="tracy-tab-panel tabs-panel">
				<pre class="tracy-dump tracy-light">phpinfo()


 _______________________________________________________________________


Configuration

bcmath

BCMath support =&gt; enabled

Directive =&gt; Local Value =&gt; Master Value
bcmath.scale =&gt; 0 =&gt; 0

bz2

BZip2 Support =&gt; Enabled
Stream Wrapper support =&gt; compress.bzip2://
Stream Filter support =&gt; bzip2.decompress, bzip2.compress
BZip2 Version =&gt; 1.0.6, 6-Sept-2010

calendar

Calendar support =&gt; enabled

Core

PHP Version =&gt; 8.0.14

Directive =&gt; Local Value =&gt; Master Value
allow_url_fopen =&gt; On =&gt; On
allow_url_include =&gt; Off =&gt; Off
arg_separator.input =&gt; &amp; =&gt; &amp;
arg_separator.output =&gt; &amp; =&gt; &amp;
auto_append_file =&gt; no value =&gt; no value
auto_globals_jit =&gt; On =&gt; On
auto_prepend_file =&gt; no value =&gt; no value
browscap =&gt; no value =&gt; no value
default_charset =&gt; UTF-8 =&gt; UTF-8
default_mimetype =&gt; text/html =&gt; text/html
disable_classes =&gt; no value =&gt; no value
disable_functions =&gt; no value =&gt; no value
display_errors =&gt; STDOUT =&gt; STDOUT
display_startup_errors =&gt; On =&gt; On
doc_root =&gt; no value =&gt; no value
docref_ext =&gt; no value =&gt; no value
docref_root =&gt; no value =&gt; no value
enable_dl =&gt; On =&gt; On
enable_post_data_reading =&gt; On =&gt; On
error_append_string =&gt; no value =&gt; no value
error_log =&gt; no value =&gt; no value
error_prepend_string =&gt; no value =&gt; no value
error_reporting =&gt; 32767 =&gt; no value
expose_php =&gt; Off =&gt; Off
extension_dir =&gt; /usr/local/lib/php/extensions/no-debug-zts-20200930 =&gt; /usr/local/lib/php/extensions/no-debug-zts-20200930
file_uploads =&gt; On =&gt; On
hard_timeout =&gt; 2 =&gt; 2
highlight.comment =&gt; &lt;font style=&quot;color: #998; font-style: italic&quot;&gt;#998; font-style: italic&lt;/font&gt; =&gt; &lt;font style=&quot;color: #FF8000&quot;&gt;#FF8000&lt;/font&gt;
highlight.default =&gt; &lt;font style=&quot;color: #000&quot;&gt;#000&lt;/font&gt; =&gt; &lt;font style=&quot;color: #0000BB&quot;&gt;#0000BB&lt;/font&gt;
highlight.html =&gt; &lt;font style=&quot;color: #06B&quot;&gt;#06B&lt;/font&gt; =&gt; &lt;font style=&quot;color: #000000&quot;&gt;#000000&lt;/font&gt;
highlight.keyword =&gt; &lt;font style=&quot;color: #D24; font-weight: bold&quot;&gt;#D24; font-weight: bold&lt;/font&gt; =&gt; &lt;font style=&quot;color: #007700&quot;&gt;#007700&lt;/font&gt;
highlight.string =&gt; &lt;font style=&quot;color: #080&quot;&gt;#080&lt;/font&gt; =&gt; &lt;font style=&quot;color: #DD0000&quot;&gt;#DD0000&lt;/font&gt;
html_errors =&gt; Off =&gt; Off
ignore_repeated_errors =&gt; Off =&gt; Off
ignore_repeated_source =&gt; Off =&gt; Off
ignore_user_abort =&gt; Off =&gt; Off
implicit_flush =&gt; On =&gt; On
include_path =&gt; .:/usr/local/lib/php =&gt; .:/usr/local/lib/php
input_encoding =&gt; no value =&gt; no value
internal_encoding =&gt; no value =&gt; no value
log_errors =&gt; Off =&gt; On
log_errors_max_len =&gt; 1024 =&gt; 1024
mail.add_x_header =&gt; Off =&gt; Off
mail.force_extra_parameters =&gt; no value =&gt; no value
mail.log =&gt; no value =&gt; no value
max_execution_time =&gt; 0 =&gt; 0
max_file_uploads =&gt; 20 =&gt; 20
max_input_nesting_level =&gt; 64 =&gt; 64
max_input_time =&gt; -1 =&gt; -1
max_input_vars =&gt; 1000 =&gt; 1000
memory_limit =&gt; 128M =&gt; 128M
open_basedir =&gt; no value =&gt; no value
output_buffering =&gt; 0 =&gt; 0
output_encoding =&gt; no value =&gt; no value
output_handler =&gt; no value =&gt; no value
post_max_size =&gt; 8M =&gt; 8M
precision =&gt; 14 =&gt; 14
realpath_cache_size =&gt; 4096K =&gt; 4096K
realpath_cache_ttl =&gt; 120 =&gt; 120
register_argc_argv =&gt; On =&gt; On
report_memleaks =&gt; On =&gt; On
report_zend_debug =&gt; Off =&gt; Off
request_order =&gt; no value =&gt; no value
sendmail_from =&gt; no value =&gt; no value
sendmail_path =&gt; /usr/bin/msmtp -t =&gt; /usr/bin/msmtp -t
serialize_precision =&gt; -1 =&gt; -1
short_open_tag =&gt; On =&gt; On
SMTP =&gt; localhost =&gt; localhost
smtp_port =&gt; 25 =&gt; 25
sys_temp_dir =&gt; no value =&gt; no value
syslog.facility =&gt; LOG_USER =&gt; LOG_USER
syslog.filter =&gt; no-ctrl =&gt; no-ctrl
syslog.ident =&gt; php =&gt; php
unserialize_callback_func =&gt; no value =&gt; no value
upload_max_filesize =&gt; 2M =&gt; 2M
upload_tmp_dir =&gt; no value =&gt; no value
user_dir =&gt; no value =&gt; no value
user_ini.cache_ttl =&gt; 300 =&gt; 300
user_ini.filename =&gt; .user.ini =&gt; .user.ini
variables_order =&gt; EGPCS =&gt; EGPCS
xmlrpc_error_number =&gt; 0 =&gt; 0
xmlrpc_errors =&gt; Off =&gt; Off
zend.assertions =&gt; 1 =&gt; 1
zend.detect_unicode =&gt; On =&gt; On
zend.enable_gc =&gt; On =&gt; On
zend.exception_ignore_args =&gt; Off =&gt; Off
zend.exception_string_param_max_len =&gt; 15 =&gt; 15
zend.multibyte =&gt; Off =&gt; Off
zend.script_encoding =&gt; no value =&gt; no value
zend.signal_check =&gt; Off =&gt; Off

ctype

ctype functions =&gt; enabled

curl

cURL support =&gt; enabled
cURL Information =&gt; 7.64.0
Age =&gt; 4
Features
AsynchDNS =&gt; Yes
CharConv =&gt; No
Debug =&gt; No
GSS-Negotiate =&gt; No
IDN =&gt; Yes
IPv6 =&gt; Yes
krb4 =&gt; No
Largefile =&gt; Yes
libz =&gt; Yes
NTLM =&gt; Yes
NTLMWB =&gt; Yes
SPNEGO =&gt; Yes
SSL =&gt; Yes
SSPI =&gt; No
TLS-SRP =&gt; Yes
HTTP2 =&gt; Yes
GSSAPI =&gt; Yes
KERBEROS5 =&gt; Yes
UNIX_SOCKETS =&gt; Yes
PSL =&gt; Yes
HTTPS_PROXY =&gt; Yes
MULTI_SSL =&gt; No
BROTLI =&gt; No
Protocols =&gt; dict, file, ftp, ftps, gopher, http, https, imap, imaps, ldap, ldaps, pop3, pop3s, rtmp, rtsp, scp, sftp, smb, smbs, smtp, smtps, telnet, tftp
Host =&gt; x86_64-pc-linux-gnu
SSL Version =&gt; OpenSSL/1.1.1d
ZLib Version =&gt; 1.2.11
libSSH Version =&gt; libssh2/1.8.0

Directive =&gt; Local Value =&gt; Master Value
curl.cainfo =&gt; no value =&gt; no value

date

date/time support =&gt; enabled
timelib version =&gt; 2020.03
&quot;Olson&quot; Timezone Database Version =&gt; 2021.5
Timezone Database =&gt; internal
Default timezone =&gt; UTC

Directive =&gt; Local Value =&gt; Master Value
date.default_latitude =&gt; 31.7667 =&gt; 31.7667
date.default_longitude =&gt; 35.2333 =&gt; 35.2333
date.sunrise_zenith =&gt; 90.833333 =&gt; 90.833333
date.sunset_zenith =&gt; 90.833333 =&gt; 90.833333
date.timezone =&gt; Europe/Prague =&gt; Europe/Prague

dom

DOM/XML =&gt; enabled
DOM/XML API Version =&gt; 20031129
libxml Version =&gt; 2.9.4
HTML Support =&gt; enabled
XPath Support =&gt; enabled
XPointer Support =&gt; enabled
Schema Support =&gt; enabled
RelaxNG Support =&gt; enabled

exif

EXIF Support =&gt; enabled
Supported EXIF Version =&gt; 0220
Supported filetypes =&gt; JPEG, TIFF
Multibyte decoding support using mbstring =&gt; enabled
Extended EXIF tag formats =&gt; Canon, Casio, Fujifilm, Nikon, Olympus, Samsung, Panasonic, DJI, Sony, Pentax, Minolta, Sigma, Foveon, Kyocera, Ricoh, AGFA, Epson

Directive =&gt; Local Value =&gt; Master Value
exif.decode_jis_intel =&gt; JIS =&gt; JIS
exif.decode_jis_motorola =&gt; JIS =&gt; JIS
exif.decode_unicode_intel =&gt; UCS-2LE =&gt; UCS-2LE
exif.decode_unicode_motorola =&gt; UCS-2BE =&gt; UCS-2BE
exif.encode_jis =&gt; no value =&gt; no value
exif.encode_unicode =&gt; ISO-8859-15 =&gt; ISO-8859-15

fileinfo

fileinfo support =&gt; enabled
libmagic =&gt; 539

filter

Input Validation and Filtering =&gt; enabled

Directive =&gt; Local Value =&gt; Master Value
filter.default =&gt; unsafe_raw =&gt; unsafe_raw
filter.default_flags =&gt; no value =&gt; no value

ftp

FTP support =&gt; enabled
FTPS support =&gt; enabled

gd

GD Support =&gt; enabled
GD Version =&gt; bundled (2.1.0 compatible)
FreeType Support =&gt; enabled
FreeType Linkage =&gt; with freetype
FreeType Version =&gt; 2.9.1
GIF Read Support =&gt; enabled
GIF Create Support =&gt; enabled
JPEG Support =&gt; enabled
libJPEG Version =&gt; 6b
PNG Support =&gt; enabled
libPNG Version =&gt; 1.6.36
WBMP Support =&gt; enabled
XPM Support =&gt; enabled
libXpm Version =&gt; 30411
XBM Support =&gt; enabled
WebP Support =&gt; enabled
BMP Support =&gt; enabled
TGA Read Support =&gt; enabled

Directive =&gt; Local Value =&gt; Master Value
gd.jpeg_ignore_warning =&gt; 1 =&gt; 1

gettext

GetText Support =&gt; enabled

hash

hash support =&gt; enabled
Hashing Engines =&gt; md2 md4 md5 sha1 sha224 sha256 sha384 sha512/224 sha512/256 sha512 sha3-224 sha3-256 sha3-384 sha3-512 ripemd128 ripemd160 ripemd256 ripemd320 whirlpool tiger128,3 tiger160,3 tiger192,3 tiger128,4 tiger160,4 tiger192,4 snefru snefru256 gost gost-crypto adler32 crc32 crc32b crc32c fnv132 fnv1a32 fnv164 fnv1a64 joaat haval128,3 haval160,3 haval192,3 haval224,3 haval256,3 haval128,4 haval160,4 haval192,4 haval224,4 haval256,4 haval128,5 haval160,5 haval192,5 haval224,5 haval256,5 

MHASH support =&gt; Enabled
MHASH API Version =&gt; Emulated Support

iconv

iconv support =&gt; enabled
iconv implementation =&gt; glibc
iconv library version =&gt; 2.28

Directive =&gt; Local Value =&gt; Master Value
iconv.input_encoding =&gt; no value =&gt; no value
iconv.internal_encoding =&gt; no value =&gt; no value
iconv.output_encoding =&gt; no value =&gt; no value

imap

IMAP c-Client Version =&gt; 2007f
SSL Support =&gt; enabled
Kerberos Support =&gt; enabled

Directive =&gt; Local Value =&gt; Master Value
imap.enable_insecure_rsh =&gt; Off =&gt; Off

intl

Internationalization support =&gt; enabled
ICU version =&gt; 63.1
ICU Data version =&gt; 63.1
ICU Unicode version =&gt; 11.0

Directive =&gt; Local Value =&gt; Master Value
intl.default_locale =&gt; no value =&gt; no value
intl.error_level =&gt; 0 =&gt; 0
intl.use_exceptions =&gt; Off =&gt; Off

json

json support =&gt; enabled

ldap

LDAP Support =&gt; enabled
Total Links =&gt; 0/unlimited
API Version =&gt; 3001
Vendor Name =&gt; OpenLDAP
Vendor Version =&gt; 20447

Directive =&gt; Local Value =&gt; Master Value
ldap.max_links =&gt; Unlimited =&gt; Unlimited

libxml

libXML support =&gt; active
libXML Compiled Version =&gt; 2.9.4
libXML Loaded Version =&gt; 20904
libXML streams =&gt; enabled

mbstring

Multibyte Support =&gt; enabled
Multibyte string engine =&gt; libmbfl
HTTP input encoding translation =&gt; disabled
libmbfl version =&gt; 1.3.2

mbstring extension makes use of &quot;streamable kanji code filter and converter&quot;, which is distributed under the GNU Lesser General Public License version 2.1.

Multibyte (japanese) regex support =&gt; enabled
Multibyte regex (oniguruma) version =&gt; 6.9.1

Directive =&gt; Local Value =&gt; Master Value
mbstring.detect_order =&gt; no value =&gt; no value
mbstring.encoding_translation =&gt; Off =&gt; Off
mbstring.http_input =&gt; no value =&gt; no value
mbstring.http_output =&gt; no value =&gt; no value
mbstring.http_output_conv_mimetypes =&gt; ^(text/|application/xhtml\+xml) =&gt; ^(text/|application/xhtml\+xml)
mbstring.internal_encoding =&gt; no value =&gt; no value
mbstring.language =&gt; neutral =&gt; neutral
mbstring.regex_retry_limit =&gt; 1000000 =&gt; 1000000
mbstring.regex_stack_limit =&gt; 100000 =&gt; 100000
mbstring.strict_detection =&gt; Off =&gt; Off
mbstring.substitute_character =&gt; no value =&gt; no value

mysqli

MysqlI Support =&gt; enabled
Client API library version =&gt; mysqlnd 8.0.14
Active Persistent Links =&gt; 0
Inactive Persistent Links =&gt; 0
Active Links =&gt; 0

Directive =&gt; Local Value =&gt; Master Value
mysqli.allow_local_infile =&gt; Off =&gt; Off
mysqli.allow_persistent =&gt; On =&gt; On
mysqli.default_host =&gt; no value =&gt; no value
mysqli.default_port =&gt; 3306 =&gt; 3306
mysqli.default_pw =&gt; no value =&gt; no value
mysqli.default_socket =&gt; no value =&gt; no value
mysqli.default_user =&gt; no value =&gt; no value
mysqli.max_links =&gt; Unlimited =&gt; Unlimited
mysqli.max_persistent =&gt; Unlimited =&gt; Unlimited
mysqli.reconnect =&gt; Off =&gt; Off
mysqli.rollback_on_cached_plink =&gt; Off =&gt; Off

mysqlnd

mysqlnd =&gt; enabled
Version =&gt; mysqlnd 8.0.14
Compression =&gt; supported
core SSL =&gt; supported
extended SSL =&gt; supported
Command buffer size =&gt; 4096
Read buffer size =&gt; 32768
Read timeout =&gt; 86400
Collecting statistics =&gt; Yes
Collecting memory statistics =&gt; No
Tracing =&gt; n/a
Loaded plugins =&gt; mysqlnd,debug_trace,auth_plugin_mysql_native_password,auth_plugin_mysql_clear_password,auth_plugin_caching_sha2_password,auth_plugin_sha256_password
API Extensions =&gt; pdo_mysql,mysqli

odbc

ODBC Support =&gt; enabled
Active Persistent Links =&gt; 0
Active Links =&gt; 0
ODBC library =&gt; unixODBC
ODBCVER =&gt; 0x0380
ODBC_CFLAGS =&gt; -I/usr/include
ODBC_LFLAGS =&gt; -L/usr/lib/x86_64-linux-gnu
ODBC_LIBS =&gt; -lodbc

Directive =&gt; Local Value =&gt; Master Value
odbc.allow_persistent =&gt; On =&gt; On
odbc.check_persistent =&gt; On =&gt; On
odbc.default_cursortype =&gt; Static cursor =&gt; Static cursor
odbc.default_db =&gt; no value =&gt; no value
odbc.default_pw =&gt; no value =&gt; no value
odbc.default_user =&gt; no value =&gt; no value
odbc.defaultbinmode =&gt; return as is =&gt; return as is
odbc.defaultlrl =&gt; return up to 4096 bytes =&gt; return up to 4096 bytes
odbc.max_links =&gt; Unlimited =&gt; Unlimited
odbc.max_persistent =&gt; Unlimited =&gt; Unlimited

openssl

OpenSSL support =&gt; enabled
OpenSSL Library Version =&gt; OpenSSL 1.1.1d  10 Sep 2019
OpenSSL Header Version =&gt; OpenSSL 1.1.1d  10 Sep 2019
Openssl default config =&gt; /usr/lib/ssl/openssl.cnf

Directive =&gt; Local Value =&gt; Master Value
openssl.cafile =&gt; no value =&gt; no value
openssl.capath =&gt; no value =&gt; no value

pcntl

pcntl support =&gt; enabled

pcre

PCRE (Perl Compatible Regular Expressions) Support =&gt; enabled
PCRE Library Version =&gt; 10.35 2020-05-09
PCRE Unicode Version =&gt; 13.0.0
PCRE JIT Support =&gt; enabled
PCRE JIT Target =&gt; x86 64bit (little endian + unaligned)

Directive =&gt; Local Value =&gt; Master Value
pcre.backtrack_limit =&gt; 1000000 =&gt; 1000000
pcre.jit =&gt; 1 =&gt; 1
pcre.recursion_limit =&gt; 100000 =&gt; 100000

PDO

PDO support =&gt; enabled
PDO drivers =&gt; odbc, pgsql, sqlite, mysql, sqlsrv

pdo_mysql

PDO Driver for MySQL =&gt; enabled
Client API version =&gt; mysqlnd 8.0.14

Directive =&gt; Local Value =&gt; Master Value
pdo_mysql.default_socket =&gt; /tmp/mysql.sock =&gt; /tmp/mysql.sock

PDO_ODBC

PDO Driver for ODBC (unixODBC) =&gt; enabled
ODBC Connection Pooling =&gt; Enabled, strict matching

pdo_pgsql

PDO Driver for PostgreSQL =&gt; enabled
PostgreSQL(libpq) Version =&gt; 11.14

pdo_sqlite

PDO Driver for SQLite 3.x =&gt; enabled
SQLite Library =&gt; 3.27.2

pdo_sqlsrv

pdo_sqlsrv support =&gt; enabled
ExtensionVer =&gt; 5.9.0

Directive =&gt; Local Value =&gt; Master Value
pdo_sqlsrv.client_buffer_max_kb_size =&gt; 10240 =&gt; 10240
pdo_sqlsrv.log_severity =&gt; 0 =&gt; 0
pdo_sqlsrv.report_additional_errors =&gt; 1 =&gt; 1
pdo_sqlsrv.set_locale_info =&gt; 2 =&gt; 2

pgsql

PostgreSQL Support =&gt; enabled
PostgreSQL (libpq) Version =&gt; 11.14
Multibyte character support =&gt; enabled
Active Persistent Links =&gt; 0
Active Links =&gt; 0

Directive =&gt; Local Value =&gt; Master Value
pgsql.allow_persistent =&gt; On =&gt; On
pgsql.auto_reset_persistent =&gt; Off =&gt; Off
pgsql.ignore_notice =&gt; Off =&gt; Off
pgsql.log_notice =&gt; Off =&gt; Off
pgsql.max_links =&gt; Unlimited =&gt; Unlimited
pgsql.max_persistent =&gt; Unlimited =&gt; Unlimited

Phar

Phar: PHP Archive support =&gt; enabled
Phar API version =&gt; 1.1.1
Phar-based phar archives =&gt; enabled
Tar-based phar archives =&gt; enabled
ZIP-based phar archives =&gt; enabled
gzip compression =&gt; enabled
bzip2 compression =&gt; enabled
Native OpenSSL support =&gt; enabled


Phar based on pear/PHP_Archive, original concept by Davey Shafik.
Phar fully realized by Gregory Beaver and Marcus Boerger.
Portions of tar implementation Copyright (c) 2003-2009 Tim Kientzle.
Directive =&gt; Local Value =&gt; Master Value
phar.cache_list =&gt; no value =&gt; no value
phar.readonly =&gt; On =&gt; On
phar.require_hash =&gt; On =&gt; On

posix

POSIX support =&gt; enabled

readline

Readline Support =&gt; enabled
Readline library =&gt; EditLine wrapper

Directive =&gt; Local Value =&gt; Master Value
cli.pager =&gt; no value =&gt; no value
cli.prompt =&gt; \b \&gt;  =&gt; \b \&gt; 

redis

Redis Support =&gt; enabled
Redis Version =&gt; 5.3.5
Redis Sentinel Version =&gt; 0.1
Available serializers =&gt; php, json

Directive =&gt; Local Value =&gt; Master Value
redis.arrays.algorithm =&gt; no value =&gt; no value
redis.arrays.auth =&gt; no value =&gt; no value
redis.arrays.autorehash =&gt; 0 =&gt; 0
redis.arrays.connecttimeout =&gt; 0 =&gt; 0
redis.arrays.consistent =&gt; 0 =&gt; 0
redis.arrays.distributor =&gt; no value =&gt; no value
redis.arrays.functions =&gt; no value =&gt; no value
redis.arrays.hosts =&gt; no value =&gt; no value
redis.arrays.index =&gt; 0 =&gt; 0
redis.arrays.lazyconnect =&gt; 0 =&gt; 0
redis.arrays.names =&gt; no value =&gt; no value
redis.arrays.pconnect =&gt; 0 =&gt; 0
redis.arrays.previous =&gt; no value =&gt; no value
redis.arrays.readtimeout =&gt; 0 =&gt; 0
redis.arrays.retryinterval =&gt; 0 =&gt; 0
redis.clusters.auth =&gt; no value =&gt; no value
redis.clusters.cache_slots =&gt; 0 =&gt; 0
redis.clusters.persistent =&gt; 0 =&gt; 0
redis.clusters.read_timeout =&gt; 0 =&gt; 0
redis.clusters.seeds =&gt; no value =&gt; no value
redis.clusters.timeout =&gt; 0 =&gt; 0
redis.pconnect.connection_limit =&gt; 0 =&gt; 0
redis.pconnect.echo_check_liveness =&gt; 1 =&gt; 1
redis.pconnect.pool_detect_dirty =&gt; 0 =&gt; 0
redis.pconnect.pool_pattern =&gt; no value =&gt; no value
redis.pconnect.pool_poll_timeout =&gt; 0 =&gt; 0
redis.pconnect.pooling_enabled =&gt; 1 =&gt; 1
redis.session.lock_expire =&gt; 0 =&gt; 0
redis.session.lock_retries =&gt; 10 =&gt; 10
redis.session.lock_wait_time =&gt; 2000 =&gt; 2000
redis.session.locking_enabled =&gt; 0 =&gt; 0

Reflection

Reflection =&gt; enabled

session

Session Support =&gt; enabled
Registered save handlers =&gt; files user redis rediscluster 
Registered serializer handlers =&gt; php_serialize php php_binary 

Directive =&gt; Local Value =&gt; Master Value
session.auto_start =&gt; Off =&gt; Off
session.cache_expire =&gt; 180 =&gt; 180
session.cache_limiter =&gt; nocache =&gt; nocache
session.cookie_domain =&gt; no value =&gt; no value
session.cookie_httponly =&gt; 0 =&gt; 0
session.cookie_lifetime =&gt; 0 =&gt; 0
session.cookie_path =&gt; / =&gt; /
session.cookie_samesite =&gt; no value =&gt; no value
session.cookie_secure =&gt; 0 =&gt; 0
session.gc_divisor =&gt; 100 =&gt; 100
session.gc_maxlifetime =&gt; 1440 =&gt; 1440
session.gc_probability =&gt; 1 =&gt; 1
session.lazy_write =&gt; On =&gt; On
session.name =&gt; PHPSESSID =&gt; PHPSESSID
session.referer_check =&gt; no value =&gt; no value
session.save_handler =&gt; files =&gt; files
session.save_path =&gt; no value =&gt; no value
session.serialize_handler =&gt; php =&gt; php
session.sid_bits_per_character =&gt; 4 =&gt; 4
session.sid_length =&gt; 32 =&gt; 32
session.upload_progress.cleanup =&gt; On =&gt; On
session.upload_progress.enabled =&gt; On =&gt; On
session.upload_progress.freq =&gt; 1% =&gt; 1%
session.upload_progress.min_freq =&gt; 1 =&gt; 1
session.upload_progress.name =&gt; PHP_SESSION_UPLOAD_PROGRESS =&gt; PHP_SESSION_UPLOAD_PROGRESS
session.upload_progress.prefix =&gt; upload_progress_ =&gt; upload_progress_
session.use_cookies =&gt; 1 =&gt; 1
session.use_only_cookies =&gt; 1 =&gt; 1
session.use_strict_mode =&gt; 0 =&gt; 0
session.use_trans_sid =&gt; 0 =&gt; 0

shmop

shmop support =&gt; enabled

SimpleXML

SimpleXML support =&gt; enabled
Schema support =&gt; enabled

soap

Soap Client =&gt; enabled
Soap Server =&gt; enabled

Directive =&gt; Local Value =&gt; Master Value
soap.wsdl_cache =&gt; 1 =&gt; 1
soap.wsdl_cache_dir =&gt; /tmp =&gt; /tmp
soap.wsdl_cache_enabled =&gt; On =&gt; On
soap.wsdl_cache_limit =&gt; 5 =&gt; 5
soap.wsdl_cache_ttl =&gt; 86400 =&gt; 86400

sockets

Sockets Support =&gt; enabled

SPL

SPL support =&gt; enabled
Interfaces =&gt; OuterIterator, RecursiveIterator, SeekableIterator, SplObserver, SplSubject
Classes =&gt; AppendIterator, ArrayIterator, ArrayObject, BadFunctionCallException, BadMethodCallException, CachingIterator, CallbackFilterIterator, DirectoryIterator, DomainException, EmptyIterator, FilesystemIterator, FilterIterator, GlobIterator, InfiniteIterator, InvalidArgumentException, IteratorIterator, LengthException, LimitIterator, LogicException, MultipleIterator, NoRewindIterator, OutOfBoundsException, OutOfRangeException, OverflowException, ParentIterator, RangeException, RecursiveArrayIterator, RecursiveCachingIterator, RecursiveCallbackFilterIterator, RecursiveDirectoryIterator, RecursiveFilterIterator, RecursiveIteratorIterator, RecursiveRegexIterator, RecursiveTreeIterator, RegexIterator, RuntimeException, SplDoublyLinkedList, SplFileInfo, SplFileObject, SplFixedArray, SplHeap, SplMinHeap, SplMaxHeap, SplObjectStorage, SplPriorityQueue, SplQueue, SplStack, SplTempFileObject, UnderflowException, UnexpectedValueException

sqlite3

SQLite3 support =&gt; enabled
SQLite Library =&gt; 3.27.2

Directive =&gt; Local Value =&gt; Master Value
sqlite3.defensive =&gt; On =&gt; On
sqlite3.extension_dir =&gt; no value =&gt; no value

sqlsrv

sqlsrv support =&gt; enabled
ExtensionVer =&gt; 5.9.0

Directive =&gt; Local Value =&gt; Master Value
sqlsrv.ClientBufferMaxKBSize =&gt; 10240 =&gt; 10240
sqlsrv.LogSeverity =&gt; 0 =&gt; 0
sqlsrv.LogSubsystems =&gt; 0 =&gt; 0
sqlsrv.SetLocaleInfo =&gt; 2 =&gt; 2
sqlsrv.WarningsReturnAsErrors =&gt; On =&gt; On

standard

Dynamic Library Support =&gt; enabled
Path to sendmail =&gt; /usr/bin/msmtp -t

Directive =&gt; Local Value =&gt; Master Value
assert.active =&gt; On =&gt; On
assert.bail =&gt; Off =&gt; Off
assert.callback =&gt; no value =&gt; no value
assert.exception =&gt; On =&gt; On
assert.warning =&gt; On =&gt; On
auto_detect_line_endings =&gt; Off =&gt; Off
default_socket_timeout =&gt; 60 =&gt; 60
from =&gt; no value =&gt; no value
session.trans_sid_hosts =&gt; no value =&gt; no value
session.trans_sid_tags =&gt; a=href,area=href,frame=src,form= =&gt; a=href,area=href,frame=src,form=
unserialize_max_depth =&gt; 4096 =&gt; 4096
url_rewriter.hosts =&gt; no value =&gt; no value
url_rewriter.tags =&gt; form= =&gt; form=
user_agent =&gt; no value =&gt; no value

sysvmsg

sysvmsg support =&gt; enabled

sysvsem

sysvsem support =&gt; enabled

sysvshm

sysvshm support =&gt; enabled

tokenizer

Tokenizer Support =&gt; enabled

xml

XML Support =&gt; active
XML Namespace Support =&gt; active
libxml2 Version =&gt; 2.9.4

xmlreader

XMLReader =&gt; enabled

xmlwriter

XMLWriter =&gt; enabled

xsl

XSL =&gt; enabled
libxslt Version =&gt; 1.1.32
libxslt compiled against libxml Version =&gt; 2.9.4
EXSLT =&gt; enabled
libexslt Version =&gt; 1.1.32

zip

Zip =&gt; enabled
Zip version =&gt; 1.19.5
Libzip version =&gt; 1.5.1

zlib

ZLib Support =&gt; enabled
Stream Wrapper =&gt; compress.zlib://
Stream Filter =&gt; zlib.inflate, zlib.deflate
Compiled Version =&gt; 1.2.11
Linked Version =&gt; 1.2.11

Directive =&gt; Local Value =&gt; Master Value
zlib.output_compression =&gt; Off =&gt; Off
zlib.output_compression_level =&gt; -1 =&gt; -1
zlib.output_handler =&gt; no value =&gt; no value

Additional Modules

Module Name
</pre>
			</div>


		</div>
	</div>
	</div>
</section>

<section class="section">
	<h2 class="section-label"><a href="#" data-tracy-ref="^+" class="tracy-toggle tracy-collapsed">CLI request</a></h2>

	<div class="section-panel tracy-collapsed">
		<h3>Process ID 757</h3>
		<pre>php vendor/bin/console</pre>

		<h3>Arguments</h3>
		<div class="pane">
			<table>
				<tr><th>0</th><td><pre class="tracy-dump tracy-light"
><span class="tracy-dump-string" title="18 characters"><span>'</span>vendor/bin/console<span>'</span></span></pre>
</td></tr>
			</table>
		</div>
	</div>
</section>



		<footer>
			<ul>
				<li><b><a href="https://github.com/sponsors/dg" target="_blank" rel="noreferrer noopener">Please support Tracy via a donation 💙️</a></b></li>
				<li>Report generated at 2022/02/18 09:59:35</li>
				<li>PHP 8.0.14</li><li>Tracy 2.9.0</li>			</ul>
			<div class="footer-logo"><a href="https://tracy.nette.org" rel="noreferrer"></a></div>
		</footer>
	</div>
	<meta itemprop=tracy-snapshot content='{"574":{"object":"ErrorException","items":[["context",null,3],["skippable",true,3],["severity",2,1],["message","Undefined array key \"schema_tool_update_command\"",1],["string","","Exception"],["code",0,1],["file","/web/vendor/dek-apps/nette-doctrine/bin/console.php",1],["line",133,1],["trace",[[0,[["file","/web/vendor/tracy/tracy/src/Tracy/Debugger/Debugger.php"],["line",377],["function","handleError"],["class","Tracy\\DevelopmentStrategy"],["type","->"],["args",[[0,2],[1,"Undefined array key \"schema_tool_update_command\""],[2,"/web/vendor/dek-apps/nette-doctrine/bin/console.php"],[3,133],[4,null]]]]],[1,[["file","/web/vendor/dek-apps/nette-doctrine/bin/console.php"],["line",133],["function","errorHandler"],["class","Tracy\\Debugger"],["type","::"],["args",[[0,2],[1,"Undefined array key \"schema_tool_update_command\""],[2,"/web/vendor/dek-apps/nette-doctrine/bin/console.php"],[3,133]]]]],[2,[["file","/web/vendor/dek-apps/nette-doctrine/bin/console"],["line",4],["args",[[0,"/web/vendor/dek-apps/nette-doctrine/bin/console.php"]]],["function","include"]]],[3,[["file","/web/vendor/bin/console"],["line",107],["args",[[0,"/web/vendor/dek-apps/nette-doctrine/bin/console"]]],["function","include"]]]],"Exception"],["previous",null,"Exception"]]}}'>
</div>

<script>
'use strict';
(function(){const MOVE_THRESHOLD=100;class Toggle{static init(){let start;document.documentElement.addEventListener('mousedown',(e)=>{start=[e.clientX,e.clientY];});document.documentElement.addEventListener('click',(e)=>{let el;if(!e.shiftKey&&!e.ctrlKey&&!e.metaKey&&(el=e.target.closest('.tracy-toggle'))&&Math.pow(start[0]-e.clientX,2)+Math.pow(start[1]-e.clientY,2)<MOVE_THRESHOLD){Toggle.toggle(el,undefined,e);e.preventDefault();e.stopImmediatePropagation();}});Toggle.init=function(){};}static toggle(el,expand,e){let collapsed=el.classList.contains('tracy-collapsed'),ref=el.getAttribute('data-tracy-ref')||el.getAttribute('href',2),dest=el;if(typeof expand==='undefined'){expand=collapsed;}if(!ref||ref==='#'){ref='+';}else if(ref.substr(0,1)==='#'){dest=document;}ref=ref.match(/(\^\s*([^+\s]*)\s*)?(\+\s*(\S*)\s*)?(.*)/);dest=ref[1]?dest.parentNode:dest;dest=ref[2]?dest.closest(ref[2]):dest;dest=ref[3]?Toggle.nextElement(dest.nextElementSibling,ref[4]):dest;dest=ref[5]?dest.querySelector(ref[5]):dest;el.classList.toggle('tracy-collapsed',!expand);dest.classList.toggle('tracy-collapsed',!expand);el.dispatchEvent(new CustomEvent('tracy-toggle',{bubbles:true,detail:{relatedTarget:dest,collapsed:!expand,originalEvent:e}}));}static persist(baseEl,restore){let saved=[];baseEl.addEventListener('tracy-toggle',(e)=>{if(saved.indexOf(e.target)<0){saved.push(e.target);}});let toggles=JSON.parse(sessionStorage.getItem('tracy-toggles-'+baseEl.id));if(toggles&&restore!==false){toggles.forEach((item)=>{let el=baseEl;for(let i in item.path){if(!(el=el.children[item.path[i]])){return;}}if(el.textContent===item.text){Toggle.toggle(el,item.expand);}});}window.addEventListener('unload',()=>{toggles=saved.map((el)=>{let item={path:[],text:el.textContent,expand:!el.classList.contains('tracy-collapsed')};do{item.path.unshift(Array.from(el.parentNode.children).indexOf(el));el=el.parentNode;}while(el&&el!==baseEl);return item;});sessionStorage.setItem('tracy-toggles-'+baseEl.id,JSON.stringify(toggles));});}static nextElement(el,selector){while(el&&selector&&!el.matches(selector)){el=el.nextElementSibling;}return el;}}let Tracy=window.Tracy=window.Tracy||{};Tracy.Toggle=Tracy.Toggle||Toggle;})();(function(){class TableSort{static init(){document.documentElement.addEventListener('click',(e)=>{if(e.target.matches('.tracy-sortable > :first-child > tr:first-child *')){TableSort.sort(e.target.closest('td,th'));}});TableSort.init=function(){};}static sort(tcell){let tbody=tcell.closest('table').tBodies[0];let preserveFirst=!tcell.closest('thead')&&!tcell.parentNode.querySelectorAll('td').length;let asc=!(tbody.tracyAsc===tcell.cellIndex);tbody.tracyAsc=asc?tcell.cellIndex:null;let getText=(cell)=>{return cell?(cell.getAttribute('data-order')||cell.innerText):'';};Array.from(tbody.children).slice(preserveFirst?1:0).sort((a,b)=>{return function(v1,v2){return v1!==''&&v2!==''&&!isNaN(v1)&&!isNaN(v2)?v1-v2:v1.toString().localeCompare(v2);}(getText((asc?a:b).children[tcell.cellIndex]),getText((asc?b:a).children[tcell.cellIndex]));}).forEach((tr)=>{tbody.appendChild(tr);});}}let Tracy=window.Tracy=window.Tracy||{};Tracy.TableSort=Tracy.TableSort||TableSort;})();(function(){class Tabs{static init(){document.documentElement.addEventListener('click',(e)=>{let label,context;if(!e.shiftKey&&!e.ctrlKey&&!e.metaKey&&(label=e.target.closest('.tracy-tab-label'))&&(context=e.target.closest('.tracy-tabs'))){Tabs.toggle(context,label);e.preventDefault();e.stopImmediatePropagation();}});Tabs.init=function(){};}static toggle(context,label){let labels=context.querySelector('.tracy-tab-label').parentNode.querySelectorAll('.tracy-tab-label'),panels=context.querySelector('.tracy-tab-panel').parentNode.querySelectorAll(':scope > .tracy-tab-panel');for(let i=0;i<labels.length;i++){labels[i].classList.toggle('tracy-active',labels[i]===label);}for(let i=0;i<panels.length;i++){panels[i].classList.toggle('tracy-active',labels[i]===label);}}}let Tracy=window.Tracy=window.Tracy||{};Tracy.Tabs=Tracy.Tabs||Tabs;})();(function(){const COLLAPSE_COUNT=7,COLLAPSE_COUNT_TOP=14,TYPE_ARRAY='a',TYPE_OBJECT='o',TYPE_RESOURCE='r',PROP_VIRTUAL=4,PROP_PRIVATE=2;const HINT_CTRL='Ctrl-Click to open in editor',HINT_ALT='Alt-Click to expand/collapse all child nodes';class Dumper{static init(context){(context||document).querySelectorAll('[data-tracy-snapshot][data-tracy-dump]').forEach((pre)=>{let snapshot=JSON.parse(pre.getAttribute('data-tracy-snapshot'));pre.removeAttribute('data-tracy-snapshot');pre.appendChild(build(JSON.parse(pre.getAttribute('data-tracy-dump')),snapshot,pre.classList.contains('tracy-collapsed')));pre.removeAttribute('data-tracy-dump');pre.classList.remove('tracy-collapsed');});(context||document).querySelectorAll('meta[itemprop=tracy-snapshot]').forEach((meta)=>{let snapshot=JSON.parse(meta.getAttribute('content'));meta.parentElement.querySelectorAll('[data-tracy-dump]').forEach((pre)=>{if(pre.closest('[data-tracy-snapshot]')){return;}pre.appendChild(build(JSON.parse(pre.getAttribute('data-tracy-dump')),snapshot,pre.classList.contains('tracy-collapsed')));pre.removeAttribute('data-tracy-dump');pre.classList.remove('tracy-collapsed');});});if(Dumper.inited){return;}Dumper.inited=true;document.documentElement.addEventListener('click',(e)=>{let el;if(e.ctrlKey&&(el=e.target.closest('[data-tracy-href]'))){location.href=el.getAttribute('data-tracy-href');return false;}if((el=e.target.closest('[data-tracy-snapshot]'))){let snapshot=JSON.parse(el.getAttribute('data-tracy-snapshot'));el.removeAttribute('data-tracy-snapshot');el.querySelectorAll('[data-tracy-dump]').forEach((toggler)=>{if(!toggler.nextSibling){toggler.after(document.createTextNode('\n'));}toggler.nextSibling.after(buildStruct(JSON.parse(toggler.getAttribute('data-tracy-dump')),snapshot,toggler,true,[]));toggler.removeAttribute('data-tracy-dump');});}});document.documentElement.addEventListener('tracy-toggle',(e)=>{if(!e.target.matches('.tracy-dump *')){return;}let cont=e.detail.relatedTarget;let origE=e.detail.originalEvent;if(origE&&origE.usedIds){toggleChildren(cont,origE.usedIds);return;}else if(origE&&origE.altKey&&cont.querySelector('.tracy-toggle')){if(e.detail.collapsed){e.target.classList.toggle('tracy-collapsed',false);cont.classList.toggle('tracy-collapsed',false);e.detail.collapsed=false;}let expand=e.target.tracyAltExpand=!e.target.tracyAltExpand;toggleChildren(cont,expand?{}:false);}cont.classList.toggle('tracy-dump-flash',!e.detail.collapsed);});document.documentElement.addEventListener('animationend',(e)=>{if(e.animationName==='tracy-dump-flash'){e.target.classList.toggle('tracy-dump-flash',false);}});document.addEventListener('mouseover',(e)=>{if(!e.target.matches('.tracy-dump *')){return;}let el;if(e.target.matches('.tracy-dump-hash')&&(el=e.target.closest('.tracy-dump'))){el.querySelectorAll('.tracy-dump-hash').forEach((el)=>{if(el.textContent===e.target.textContent){el.classList.add('tracy-dump-highlight');}});return;}if((el=e.target.closest('.tracy-toggle'))&&!el.title){el.title=HINT_ALT;}});document.addEventListener('mouseout',(e)=>{if(e.target.matches('.tracy-dump-hash')){document.querySelectorAll('.tracy-dump-hash.tracy-dump-highlight').forEach((el)=>{el.classList.remove('tracy-dump-highlight');});}});Tracy.Toggle.init();}}function build(data,repository,collapsed,parentIds,keyType){let id,type=data===null?'null':typeof data,collapseCount=collapsed===null?COLLAPSE_COUNT:COLLAPSE_COUNT_TOP;if(type==='null'||type==='number'||type==='boolean'){return createEl(null,null,[createEl('span',{'class':'tracy-dump-'+type.replace('ean','')},[data+''])]);}else if(type==='string'){data={string:data.replace(/&/g,'&amp;').replace(/</g,'&lt;'),length:[...data].length};}else if(Array.isArray(data)){data={array:null,items:data};}else if(data.ref){id=data.ref;data=repository[id];if(!data){throw new UnknownEntityException;}}if(data.string!==undefined||data.bin!==undefined){let s=data.string===undefined?data.bin:data.string;if(keyType===TYPE_ARRAY){return createEl(null,null,[createEl('span',{'class':'tracy-dump-string'},{html:'<span class="tracy-dump-lq">\'<\/span>'+s+'<span>\'<\/span>'}),]);}else if(keyType!==undefined){if(type!=='string'){s='<span class="tracy-dump-lq">\'<\/span>'+s+'<span>\'<\/span>';}const classes=['tracy-dump-public','tracy-dump-protected','tracy-dump-private','tracy-dump-dynamic','tracy-dump-virtual',];return createEl(null,null,[createEl('span',{'class':classes[typeof keyType==='string'?PROP_PRIVATE:keyType],'title':typeof keyType==='string'?'declared in '+keyType:null,},{html:s}),]);}let count=(s.match(/\n/g)||[]).length;if(count){let collapsed=count>=COLLAPSE_COUNT;return createEl(null,null,[createEl('span',{'class':collapsed?'tracy-toggle tracy-collapsed':'tracy-toggle'},['string']),'\n',createEl('div',{'class':'tracy-dump-string'+(collapsed?' tracy-collapsed':''),'title':data.length+(data.bin?' bytes':' characters'),},{html:'<span class="tracy-dump-lq">\'<\/span>'+s+'<span>\'<\/span>'}),]);}return createEl(null,null,[createEl('span',{'class':'tracy-dump-string','title':data.length+(data.bin?' bytes':' characters'),},{html:'<span>\'<\/span>'+s+'<span>\'<\/span>'}),]);}else if(data.number){return createEl(null,null,[createEl('span',{'class':'tracy-dump-number'},[data.number])]);}else if(data.text!==undefined){return createEl(null,null,[createEl('span',{class:'tracy-dump-virtual'},[data.text])]);}else{let span=data.array!==undefined?[createEl('span',{'class':'tracy-dump-array'},['array']),' ('+(data.length||data.items.length)+')']:[createEl('span',{'class':data.object?'tracy-dump-object':'tracy-dump-resource',title:data.editor?'Declared in file '+data.editor.file+' on line '+data.editor.line+(data.editor.url?'\n'+HINT_CTRL:'')+'\n'+HINT_ALT:null,'data-tracy-href':data.editor?data.editor.url:null},[data.object||data.resource]),...(id?[' ',createEl('span',{'class':'tracy-dump-hash'},[data.resource?'@'+id.substr(1):'#'+id])]:[])];parentIds=parentIds?parentIds.slice():[];let recursive=id&&parentIds.indexOf(id)>-1;parentIds.push(id);if(recursive||!data.items||!data.items.length){span.push(recursive?' RECURSION':(!data.items||data.items.length?' …':''));return createEl(null,null,span);}collapsed=collapsed===true||data.collapsed||(data.items&&data.items.length>=collapseCount);let toggle=createEl('span',{'class':collapsed?'tracy-toggle tracy-collapsed':'tracy-toggle'},span);return createEl(null,null,[toggle,'\n',buildStruct(data,repository,toggle,collapsed,parentIds),]);}}function buildStruct(data,repository,toggle,collapsed,parentIds){if(Array.isArray(data)){data={items:data};}else if(data.ref){parentIds=parentIds.slice();parentIds.push(data.ref);data=repository[data.ref];}let cut=data.items&&data.length>data.items.length;let type=data.object?TYPE_OBJECT:data.resource?TYPE_RESOURCE:TYPE_ARRAY;let div=createEl('div',{'class':collapsed?'tracy-collapsed':null});if(collapsed){let handler;toggle.addEventListener('tracy-toggle',handler=function(){toggle.removeEventListener('tracy-toggle',handler);createItems(div,data.items,type,repository,parentIds,null);if(cut){createEl(div,null,['…\n']);}});}else{createItems(div,data.items,type,repository,parentIds,true);if(cut){createEl(div,null,['…\n']);}}return div;}function createEl(el,attrs,content){if(!(el instanceof Node)){el=el?document.createElement(el):document.createDocumentFragment();}for(let id in attrs||{}){if(attrs[id]!==null){el.setAttribute(id,attrs[id]);}}if(content&&content.html!==undefined){el.innerHTML=content.html;return el;}content=content||[];for(let id=0;id<content.length;id++){let child=content[id];if(child!==null){el.appendChild(child instanceof Node?child:document.createTextNode(child));}}return el;}function createItems(el,items,type,repository,parentIds,collapsed){let key,val,vis,ref,i,tmp;for(i=0;i<items.length;i++){if(type===TYPE_ARRAY){[key,val,ref]=items[i];}else{[key,val,vis=PROP_VIRTUAL,ref]=items[i];}createEl(el,null,[build(key,null,null,null,type===TYPE_ARRAY?TYPE_ARRAY:vis),type===TYPE_ARRAY?' => ':': ',...(ref?[createEl('span',{'class':'tracy-dump-hash'},['&'+ref]),' ']:[]),tmp=build(val,repository,collapsed,parentIds),tmp.lastElementChild.tagName==='DIV'?'':'\n',]);}}function toggleChildren(cont,usedIds){let hashEl,id;cont.querySelectorAll(':scope > .tracy-toggle').forEach((el)=>{hashEl=(el.querySelector('.tracy-dump-hash')||el.previousElementSibling);id=hashEl&&hashEl.matches('.tracy-dump-hash')?hashEl.textContent:null;if(!usedIds||(id&&usedIds[id])){Tracy.Toggle.toggle(el,false);}else{usedIds[id]=true;Tracy.Toggle.toggle(el,true,{usedIds:usedIds});}});}function UnknownEntityException(){}let Tracy=window.Tracy=window.Tracy||{};Tracy.Dumper=Tracy.Dumper||Dumper;function init(){Tracy.Dumper.init();}if(document.readyState==='loading'){document.addEventListener('DOMContentLoaded',init);}else{init();}})();(function(){class BlueScreen{static init(ajax){let blueScreen=document.getElementById('tracy-bs');let styles=[];for(let i=0;i<document.styleSheets.length;i++){let style=document.styleSheets[i];if(!style.ownerNode.classList.contains('tracy-debug')){style.oldDisabled=style.disabled;style.disabled=true;styles.push(style);}}if(navigator.platform.indexOf('Mac')>-1){blueScreen.classList.add('mac');}document.getElementById('tracy-bs-toggle').addEventListener('tracy-toggle',function(){let collapsed=this.classList.contains('tracy-collapsed');for(let i=0;i<styles.length;i++){styles[i].disabled=collapsed?styles[i].oldDisabled:true;}});if(!ajax){document.body.appendChild(blueScreen);let id=location.href+document.querySelector('.section--error').textContent;Tracy.Toggle.persist(blueScreen,sessionStorage.getItem('tracy-toggles-bskey')===id);sessionStorage.setItem('tracy-toggles-bskey',id);}if(inited){return;}inited=true;document.addEventListener('keyup',(e)=>{if(e.keyCode===27&&!e.shiftKey&&!e.altKey&&!e.ctrlKey&&!e.metaKey){Tracy.Toggle.toggle(document.getElementById('tracy-bs-toggle'));}});blueScreen.addEventListener('tracy-toggle',(e)=>{if(!e.target.matches('.tracy-dump *')&&e.detail.originalEvent){e.detail.relatedTarget.classList.toggle('panel-fadein',!e.detail.collapsed);}});Tracy.TableSort.init();Tracy.Tabs.init();}static loadAjax(content){let ajaxBs=document.getElementById('tracy-bs');if(ajaxBs){ajaxBs.remove();}document.body.insertAdjacentHTML('beforeend',content);ajaxBs=document.getElementById('tracy-bs');Tracy.Dumper.init(ajaxBs);BlueScreen.init(true);window.scrollTo(0,0);}}let inited;let Tracy=window.Tracy=window.Tracy||{};Tracy.BlueScreen=Tracy.BlueScreen||BlueScreen;})();Tracy.BlueScreen.init();
</script>
</body>
</html>
