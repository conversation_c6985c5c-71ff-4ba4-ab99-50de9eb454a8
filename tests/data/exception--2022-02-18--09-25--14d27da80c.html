<!DOCTYPE html><!-- "' --></textarea></script></style></pre></xmp></a></iframe></noembed></noframes></noscript></option></select></template></title></table>
<html>
<head>
	<meta charset="utf-8">
	<meta name="robots" content="noindex">
	<meta name="generator" content="Tracy by Nette Framework">

	<title>Warning: dir(/web/storage/Doctrine/log): Failed to open directory: No such file or directory</title>
	<!-- in /web/vendor/dek-apps/nette-doctrine/src/Loggers/FileSQLLogger.php:50 -->

	<style class="tracy-debug">
	:root{--tracy-space: 16px}#tracy-bs{font: 9pt/1.5 Verdana,sans-serif;background: white;color: #333;position: absolute;z-index: 20000;left: 0;top: 0;width: 100%;text-align: left}#tracy-bs a{text-decoration: none;color: #328ADC;padding: 0 4px;margin: 0 -4px}#tracy-bs a+a{margin-left: 0}#tracy-bs a:hover,#tracy-bs a:focus{color: #085AA3}#tracy-bs-toggle{position: absolute;right: .5em;top: .5em;text-decoration: none;background: #CD1818;color: white!important;padding: 3px}.tracy-bs-main{display: flex;flex-direction: column;min-height: 100vh}.tracy-bs-main.tracy-collapsed{display: none}#tracy-bs .section:last-of-type{flex: 1}#tracy-bs p,#tracy-bs table,#tracy-bs pre,#tracy-bs h1,#tracy-bs h2,#tracy-bs h3{margin: 0 0 var(--tracy-space)}#tracy-bs h1{font-size: 15pt;font-weight: normal;text-shadow: 1px 1px 2px rgba(0,0,0,.3)}#tracy-bs h1 span{white-space: pre-wrap}#tracy-bs h2{font-size: 14pt;font-weight: normal;margin-top: var(--tracy-space)}#tracy-bs h3{font-size: 10pt;font-weight: bold}#tracy-bs pre,#tracy-bs code,#tracy-bs table{font: 9pt/1.5 Consolas,monospace!important}#tracy-bs pre,#tracy-bs table{background: #FDF5CE;padding: .4em .7em;border: 2px solid #ffffffa6;box-shadow: 1px 2px 6px #00000005;overflow: auto}#tracy-bs table pre{padding: 0;margin: 0;border: none;box-shadow: none}#tracy-bs table{border-collapse: collapse;width: 100%}#tracy-bs td,#tracy-bs th{vertical-align: top;text-align: left;padding: 2px 6px;border: 1px solid #e6dfbf}#tracy-bs th{font-weight: bold}#tracy-bs tr>:first-child{width: 20%}#tracy-bs tr:nth-child(2n),#tracy-bs tr:nth-child(2n) pre{background-color: #F7F0CB}#tracy-bs footer ul{font-size: 7pt;padding: var(--tracy-space);margin: var(--tracy-space) 0 0;color: #777;background: #F6F5F3;border-top: 1px solid #DDD;list-style: none}#tracy-bs .footer-logo a{position: absolute;bottom: 0;right: 0;width: 100px;height: 50px;background: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAFoAAAAUBAMAAAD/1DctAAAAMFBMVEWupZzj39rEvbTy8O3X0sz9/PvGwLu8tavQysHq6OS0rKP5+Pbd2dT29fPMxbzPx8DKErMJAAAACXBIWXMAAAsTAAALEwEAmpwYAAACGUlEQVQoFX3TQWgTQRQA0MWLIJJDYehBTykhG5ERTx56K1u8eEhCYtomE7x5L4iLh0ViF7egewuFFqSIYE6hIHsIYQ6CQSg9CDKn4QsNCRlB59C74J/ZNHW1+An5+bOPyf6/s46oz2P+A0yIeZZ2ieEHi6TOnLKTxvWq+b52mxlVO3xnM1s7xLX1504XQH65OnW2dBqn7cCkYsFsfYsWpyY/2salmFTpEyzeR8zosYqMdiPDXdyU52K1wgEa/SjGpdEwUAxqvRfckQCDOyFearsEHe2grvkh/cFAHKvdtI3lcVceKQIOFpv+FOZaNPQBwJZLPp+hfrvT5JZXaUFsR8zqQc9qSgAharkfS5M/5F6nGJJAtXq/eLr3ucZpHccSxOOIPaQhtHohpCH2Xu6rLmQ0djnr4/+J3C6v+AW8/XWYxwYNdlhWj/P5fPSTQwVr0T9lGxdaBCqErNZaqYnEwbkjEB3NasGF3lPdrHa1nnxNOMgj0+neePUPjd2v/qVvUv29ifvc19huQ48qwXShy/9o8o3OSk0cs37mOFd0Ydgvsf/oZEnPVtggfd66lORn9mDyyzXU13SRtH2L6aR5T/snGAcZPfAXz5J1YlJWBEuxdMYqQecpBrlM49xAbmqyHA+xlA1FxBtqT2xmJoNXZlIt74ZBLeJ9ZGDqByNI7p543idzJ23vXEv7IgnsxiS+eNtwNbFdLq7+Bi4wQ0I4SVb9AAAAAElFTkSuQmCC') no-repeat;opacity: .6;padding: 0;margin: 0}#tracy-bs .footer-logo a:hover,#tracy-bs .footer-logo a:focus{opacity: 1;transition: opacity 0.1s}#tracy-bs .section{padding-left: calc(1.5 * var(--tracy-space));padding-right: calc(1.5 * var(--tracy-space))}#tracy-bs .section-panel{background: #F4F3F1;padding: var(--tracy-space) var(--tracy-space) 0;margin: 0 0 var(--tracy-space);border-radius: 8px;box-shadow: inset 1px 1px 0px 0 #00000005;overflow: hidden}#tracy-bs .outer,#tracy-bs .pane{overflow: auto}#tracy-bs.mac .pane{padding-bottom: 12px}#tracy-bs .section--error{background: #CD1818;color: white;font-size: 13pt;padding-top: var(--tracy-space)}#tracy-bs .section--error h1{color: white}#tracy-bs .section--error::selection,#tracy-bs .section--error ::selection{color: black!important;background: #FDF5CE!important}#tracy-bs .section--error a{color: #ffefa1!important}#tracy-bs .section--error span span{font-size: 80%;color: rgba(255,255,255,0.5);text-shadow: none}#tracy-bs .section--error a.action{color: white!important;opacity: 0;font-size: .7em;border-bottom: none!important}#tracy-bs .section--error:hover a.action{opacity: .6}#tracy-bs .section--error a.action:hover{opacity: 1}#tracy-bs .section--error i{color: #ffefa1;font-style: normal}#tracy-bs pre.code>div{min-width: 100%;float: left;white-space: pre}#tracy-bs .highlight{background: #CD1818;color: white;font-weight: bold;font-style: normal;display: block;padding: 0 .4em;margin: 0 -.4em}#tracy-bs .line{color: #9F9C7F;font-weight: normal;font-style: normal}#tracy-bs a.tracy-editor{color: inherit;border-bottom: 1px dotted rgba(0,0,0,.3);border-radius: 3px}#tracy-bs a.tracy-editor:hover{background: #0001}#tracy-bs span[data-tracy-href]{border-bottom: 1px dotted rgba(0,0,0,.3)}#tracy-bs .tracy-dump-whitespace{color: #0003}#tracy-bs .caused{float: right;padding: .3em .6em;background: #df8075;border-radius: 0 0 0 8px;white-space: nowrap}#tracy-bs .caused a{color: white}#tracy-bs .callstack{display: grid;grid-template-columns: max-content 1fr;margin-bottom: calc(.5 * var(--tracy-space))}#tracy-bs .callstack-file{text-align: right;padding-right: var(--tracy-space);white-space: nowrap;height: calc(1.5 * var(--tracy-space))}#tracy-bs .callstack-callee{white-space: nowrap;height: calc(1.5 * var(--tracy-space))}#tracy-bs .callstack-additional{grid-column-start: 1;grid-column-end: 3}#tracy-bs .args tr:first-child>*{position: relative}#tracy-bs .args tr:first-child td:before{position: absolute;right: .3em;content:'may not be true';opacity: .4}#tracy-bs .panel-fadein{animation: panel-fadein .12s ease}@keyframes panel-fadein{0%{opacity: 0}}#tracy-bs .section--causedby{flex-direction: column;padding: 0}#tracy-bs .section--causedby:not(.tracy-collapsed){display: flex}#tracy-bs .section--causedby .section--error{background: #cd1818a6}#tracy-bs .tabs-bar{display: flex;list-style: none;padding-left: 0;margin: 0;width: 100%;font-size: 110%}#tracy-bs .tabs-bar>*:not(:first-child){margin-left: var(--tracy-space)}#tracy-bs .tabs-bar a{display: block;padding: calc(.5 * var(--tracy-space)) var(--tracy-space);margin: 0;height: 100%;box-sizing: border-box;border-radius: 5px 5px 0 0;text-decoration: none;transition: all 0.1s}#tracy-bs .tabs-bar>.tracy-active a{background: white}#tracy-bs .tabs-panel{border-top: 2px solid white;padding-top: var(--tracy-space);overflow: auto}.tracy-collapsed{display: none}.tracy-toggle.tracy-collapsed{display: inline}.tracy-toggle{cursor: pointer;user-select: none;white-space: nowrap}.tracy-toggle:after{content:'';display: inline-block;vertical-align: middle;line-height: 0;border-top: .6ex solid;border-right: .6ex solid transparent;border-left: .6ex solid transparent;transform: scale(1,1.5);margin: 0 .2ex 0 .7ex;transition: .1s transform;opacity: .5}.tracy-toggle.tracy-collapsed:after{transform: rotate(-90deg) scale(1,1.5) translate(.1ex,0)}.tracy-sortable>:first-child>tr:first-child>*{position: relative}.tracy-sortable>:first-child>tr:first-child>*:hover:before{position: absolute;right: .3em;content:"\21C5";opacity: .4;font-weight: normal}.tracy-tab-label{user-select: none}.tracy-tab-panel:not(.tracy-active){display: none}.tracy-dump.tracy-light{text-align: left;color: #444;background: #fdf9e2;border-radius: 4px;padding: 1em;margin: 1em 0;word-break: break-all;white-space: pre-wrap}.tracy-dump.tracy-light div{padding-left: 2.5ex}.tracy-dump.tracy-light div div{border-left: 1px solid rgba(0,0,0,.1);margin-left: .5ex}.tracy-dump.tracy-light div div:hover{border-left-color: rgba(0,0,0,.25)}.tracy-light .tracy-dump-location{color: gray;font-size: 80%;text-decoration: none;background: none;opacity: .5;float: right;cursor: pointer}.tracy-light .tracy-dump-location:hover,.tracy-light .tracy-dump-location:focus{opacity: 1}.tracy-light .tracy-dump-array,.tracy-light .tracy-dump-object{color: #C22;user-select: text}.tracy-light .tracy-dump-string{color: #35D;white-space: break-spaces}.tracy-light div.tracy-dump-string{position: relative;padding-left: 3.5ex}.tracy-light .tracy-dump-lq{margin-left: calc(-1ex - 1px)}.tracy-light div.tracy-dump-string:before{content:'';position: absolute;left: calc(3ex - 1px);top: 1.5em;bottom: 0;border-left: 1px solid rgba(0,0,0,.1)}.tracy-light .tracy-dump-virtual span,.tracy-light .tracy-dump-dynamic span,.tracy-light .tracy-dump-string span{color: rgba(0,0,0,0.5)}.tracy-light .tracy-dump-virtual i,.tracy-light .tracy-dump-dynamic i,.tracy-light .tracy-dump-string i{font-size: 80%;font-style: normal;color: rgba(0,0,0,0.5);user-select: none}.tracy-light .tracy-dump-number{color: #090}.tracy-light .tracy-dump-null,.tracy-light .tracy-dump-bool{color: #850}.tracy-light .tracy-dump-virtual{font-style: italic}.tracy-light .tracy-dump-public::after{content:' pub'}.tracy-light .tracy-dump-protected::after{content:' pro'}.tracy-light .tracy-dump-private::after{content:' pri'}.tracy-light .tracy-dump-public::after,.tracy-light .tracy-dump-protected::after,.tracy-light .tracy-dump-private::after,.tracy-light .tracy-dump-hash{font-size: 85%;color: rgba(0,0,0,0.5)}.tracy-light .tracy-dump-indent{display: none}.tracy-light .tracy-dump-highlight{background: #C22;color: white;border-radius: 2px;padding: 0 2px;margin: 0 -2px}span[data-tracy-href]{border-bottom: 1px dotted rgba(0,0,0,.2)}.tracy-light .tracy-dump-flash{animation: tracy-dump-flash .2s ease}@keyframes tracy-dump-flash{0%{background: #c0c0c033}}	</style>
</head>


<body>
<div id="tracy-bs" itemscope>
	<a id="tracy-bs-toggle" href="#" class="tracy-toggle"></a>
	<div class="tracy-bs-main">
<section class="section section--error">
	<p>Warning</p>

	<h1><span>dir(/web/storage/Doctrine/log): Failed to open directory: No such file or directory</span>
	</h1>
</section>



<section class="section">
	<h2 class="section-label"><a href="#" data-tracy-ref="^+" class="tracy-toggle">Source file</a></h2>

	<div class="section-panel">
		<p><b>File:</b> <a href="editor://open/?file=%2Fweb%2Fvendor%2Fdek-apps%2Fnette-doctrine%2Fsrc%2FLoggers%2FFileSQLLogger.php&amp;line=50&amp;search=&amp;replace=" title="/web/vendor/dek-apps/nette-doctrine/src/Loggers/FileSQLLogger.php:50" class="tracy-editor">.../dek-apps/nette-doctrine/src/Loggers/<b>FileSQLLogger.php</b>:50</a></p>
		<pre title="Ctrl-Click to open in editor" data-tracy-href="editor://open/?file=%2Fweb%2Fvendor%2Fdek-apps%2Fnette-doctrine%2Fsrc%2FLoggers%2FFileSQLLogger.php&amp;line=50&amp;search=&amp;replace=" class='code'><div><code><span style="color: #06B"><span style="color: #D24; font-weight: bold"><span class='line'>40:</span>        </span><span style="color: #998; font-style: italic">/**
<span class='line'>41:</span>         * {@inheritdoc}
<span class='line'>42:</span>         */
<span class='line'>43:</span>        </span><span style="color: #D24; font-weight: bold">public function </span><span style="color: #000">stopQuery</span><span style="color: #D24; font-weight: bold">()
<span class='line'>44:</span>        {
<span class='line'>45:</span>        }
<span class='line'>46:</span>    
<span class='line'>47:</span>        private function </span><span style="color: #000">clean</span><span style="color: #D24; font-weight: bold">(</span><span style="color: #000">int $deadline</span><span style="color: #D24; font-weight: bold">): </span><span style="color: #000">void
<span class='line'>48:</span>        </span><span style="color: #D24; font-weight: bold">{
<span class='line'>49:</span>            </span><span style="color: #000">$dirname </span><span style="color: #D24; font-weight: bold">= </span><span style="color: #000">dirname</span><span style="color: #D24; font-weight: bold">(</span><span style="color: #000">$this</span><span style="color: #D24; font-weight: bold">-&gt;</span><span style="color: #000">filename</span><span style="color: #D24; font-weight: bold">);
<span class='highlight'>50:            $dir = dir($dirname);
</span></span><span style="color: #000"></span><span style="color: #D24; font-weight: bold"></span><span style="color: #000"></span><span style="color: #D24; font-weight: bold"></span><span style="color: #000"></span><span style="color: #D24; font-weight: bold"><span class='line'>51:</span>            while (</span><span style="color: #000">$file </span><span style="color: #D24; font-weight: bold">= </span><span style="color: #000">$dir</span><span style="color: #D24; font-weight: bold">-&gt;</span><span style="color: #000">read</span><span style="color: #D24; font-weight: bold">()) {
<span class='line'>52:</span>                if (</span><span style="color: #000">$file </span><span style="color: #D24; font-weight: bold">=== </span><span style="color: #080">'.' </span><span style="color: #D24; font-weight: bold">|| </span><span style="color: #000">$file </span><span style="color: #D24; font-weight: bold">=== </span><span style="color: #080">'..'</span><span style="color: #D24; font-weight: bold">) {
<span class='line'>53:</span>                    continue;
<span class='line'>54:</span>                }
</span></span></code></div></pre>
	</div>
</section>

<section class="section">
	<h2 class="section-label"><a href="#" data-tracy-ref="^+" class="tracy-toggle">Call stack</a></h2>

	<div class="section-panel">
	<div class="callstack">

		<div class="callstack-file">
			<a href="editor://open/?file=%2Fweb%2Fvendor%2Fdek-apps%2Fnette-doctrine%2Fsrc%2FLoggers%2FFileSQLLogger.php&amp;line=50&amp;search=&amp;replace=" title="/web/vendor/dek-apps/nette-doctrine/src/Loggers/FileSQLLogger.php:50" class="tracy-editor">.../dek-apps/nette-doctrine/src/Loggers/<b>FileSQLLogger.php</b>:50</a>
		</div>

		<div class="callstack-callee">
			<a href="#" data-tracy-ref="^div + div" class="tracy-toggle tracy-collapsed"><b>dir</b> (...)</a>
		</div>

		<div class="callstack-additional tracy-collapsed">
			<pre title="Ctrl-Click to open in editor" data-tracy-href="editor://open/?file=%2Fweb%2Fvendor%2Fdek-apps%2Fnette-doctrine%2Fsrc%2FLoggers%2FFileSQLLogger.php&amp;line=50&amp;search=&amp;replace=" class='code'><div><code><span style="color: #06B"><span style="color: #D24; font-weight: bold"><span class='line'>40:</span>        </span><span style="color: #998; font-style: italic">/**
<span class='line'>41:</span>         * {@inheritdoc}
<span class='line'>42:</span>         */
<span class='line'>43:</span>        </span><span style="color: #D24; font-weight: bold">public function </span><span style="color: #000">stopQuery</span><span style="color: #D24; font-weight: bold">()
<span class='line'>44:</span>        {
<span class='line'>45:</span>        }
<span class='line'>46:</span>    
<span class='line'>47:</span>        private function </span><span style="color: #000">clean</span><span style="color: #D24; font-weight: bold">(</span><span style="color: #000">int $deadline</span><span style="color: #D24; font-weight: bold">): </span><span style="color: #000">void
<span class='line'>48:</span>        </span><span style="color: #D24; font-weight: bold">{
<span class='line'>49:</span>            </span><span style="color: #000">$dirname </span><span style="color: #D24; font-weight: bold">= </span><span style="color: #000">dirname</span><span style="color: #D24; font-weight: bold">(</span><span style="color: #000">$this</span><span style="color: #D24; font-weight: bold">-&gt;</span><span style="color: #000">filename</span><span style="color: #D24; font-weight: bold">);
<span class='highlight'>50:            $dir = dir($dirname);
</span></span><span style="color: #000"></span><span style="color: #D24; font-weight: bold"></span><span style="color: #000"></span><span style="color: #D24; font-weight: bold"></span><span style="color: #000"></span><span style="color: #D24; font-weight: bold"><span class='line'>51:</span>            while (</span><span style="color: #000">$file </span><span style="color: #D24; font-weight: bold">= </span><span style="color: #000">$dir</span><span style="color: #D24; font-weight: bold">-&gt;</span><span style="color: #000">read</span><span style="color: #D24; font-weight: bold">()) {
<span class='line'>52:</span>                if (</span><span style="color: #000">$file </span><span style="color: #D24; font-weight: bold">=== </span><span style="color: #080">'.' </span><span style="color: #D24; font-weight: bold">|| </span><span style="color: #000">$file </span><span style="color: #D24; font-weight: bold">=== </span><span style="color: #080">'..'</span><span style="color: #D24; font-weight: bold">) {
<span class='line'>53:</span>                    continue;
<span class='line'>54:</span>                }
</span></span></code></div></pre>

			<table class="args">
<tr><th>$directory</th><td><pre class="tracy-dump tracy-light"
><span class="tracy-dump-string" title="25 characters"><span>'</span>/web/storage/Doctrine/log<span>'</span></span></pre>
</td></tr>
			</table>
		</div>

		<div class="callstack-file">
			<a href="editor://open/?file=%2Fweb%2Fvendor%2Fdek-apps%2Fnette-doctrine%2Fsrc%2FLoggers%2FFileSQLLogger.php&amp;line=19&amp;search=&amp;replace=" title="/web/vendor/dek-apps/nette-doctrine/src/Loggers/FileSQLLogger.php:19" class="tracy-editor">.../dek-apps/nette-doctrine/src/Loggers/<b>FileSQLLogger.php</b>:19</a>
		</div>

		<div class="callstack-callee">
			<a href="#" data-tracy-ref="^div + div" class="tracy-toggle tracy-collapsed">DekApps\NetteDoctrine\Loggers\FileSQLLogger::<b>clean</b> (...)</a>
		</div>

		<div class="callstack-additional tracy-collapsed">
			<pre title="Ctrl-Click to open in editor" data-tracy-href="editor://open/?file=%2Fweb%2Fvendor%2Fdek-apps%2Fnette-doctrine%2Fsrc%2FLoggers%2FFileSQLLogger.php&amp;line=19&amp;search=&amp;replace=" class='code'><div><code><span style="color: #06B"><span style="color: #D24; font-weight: bold"><span class='line'> 9:</span>    class </span><span style="color: #000">FileSQLLogger </span><span style="color: #D24; font-weight: bold">implements </span><span style="color: #000">SQLLogger
<span class='line'>10:</span>    </span><span style="color: #D24; font-weight: bold">{
<span class='line'>11:</span>        </span><span style="color: #998; font-style: italic">/** @var string */
<span class='line'>12:</span>        </span><span style="color: #D24; font-weight: bold">private </span><span style="color: #000">$filename</span><span style="color: #D24; font-weight: bold">;
<span class='line'>13:</span>    
<span class='line'>14:</span>        public function </span><span style="color: #000">__construct</span><span style="color: #D24; font-weight: bold">(</span><span style="color: #000">string $dirname</span><span style="color: #D24; font-weight: bold">, </span><span style="color: #000">$deadline </span><span style="color: #D24; font-weight: bold">= </span><span style="color: #000">600</span><span style="color: #D24; font-weight: bold">)
<span class='line'>15:</span>        {
<span class='line'>16:</span>            @</span><span style="color: #000">mkdir</span><span style="color: #D24; font-weight: bold">(</span><span style="color: #000">$dirname</span><span style="color: #D24; font-weight: bold">);
<span class='line'>17:</span>            </span><span style="color: #000">$this</span><span style="color: #D24; font-weight: bold">-&gt;</span><span style="color: #000">filename </span><span style="color: #D24; font-weight: bold">= </span><span style="color: #000">$dirname </span><span style="color: #D24; font-weight: bold">. </span><span style="color: #080">'/' </span><span style="color: #D24; font-weight: bold">. </span><span style="color: #000">date</span><span style="color: #D24; font-weight: bold">(</span><span style="color: #080">'Y-m-d-H:i-s'</span><span style="color: #D24; font-weight: bold">) . </span><span style="color: #080">'_' </span><span style="color: #D24; font-weight: bold">. </span><span style="color: #000">\Nette\Utils\Strings</span><span style="color: #D24; font-weight: bold">::</span><span style="color: #000">webalize</span><span style="color: #D24; font-weight: bold">(</span><span style="color: #000">$_SERVER</span><span style="color: #D24; font-weight: bold">[</span><span style="color: #080">'REQUEST_URI'</span><span style="color: #D24; font-weight: bold">] ?? </span><span style="color: #080">'undefined'</span><span style="color: #D24; font-weight: bold">) . </span><span style="color: #080">'.log'</span><span style="color: #D24; font-weight: bold">;
<span class='line'>18:</span>    
<span class='highlight'>19:            $this-&gt;clean($deadline);
</span></span><span style="color: #000"></span><span style="color: #D24; font-weight: bold"></span><span style="color: #000"></span><span style="color: #D24; font-weight: bold"></span><span style="color: #000"></span><span style="color: #D24; font-weight: bold"><span class='line'>20:</span>        }
<span class='line'>21:</span>    
<span class='line'>22:</span>        </span><span style="color: #998; font-style: italic">/**
<span class='line'>23:</span>         * {@inheritdoc}
</span></span></code></div></pre>

			<table class="args">
<tr><th>$deadline</th><td><pre class="tracy-dump tracy-light"><span class="tracy-dump-number">600</span></pre>
</td></tr>
			</table>
		</div>

		<div class="callstack-file">
			<a href="editor://open/?file=%2Fweb%2Fvendor%2Fdek-apps%2Fnette-doctrine%2Fsrc%2FEntityManagerFactory.php&amp;line=84&amp;search=&amp;replace=" title="/web/vendor/dek-apps/nette-doctrine/src/EntityManagerFactory.php:84" class="tracy-editor">.../dek-apps/nette-doctrine/src/<b>EntityManagerFactory.php</b>:84</a>
		</div>

		<div class="callstack-callee">
			<a href="#" data-tracy-ref="^div + div" class="tracy-toggle tracy-collapsed">DekApps\NetteDoctrine\Loggers\FileSQLLogger::<b>__construct</b> (...)</a>
		</div>

		<div class="callstack-additional tracy-collapsed">
			<pre title="Ctrl-Click to open in editor" data-tracy-href="editor://open/?file=%2Fweb%2Fvendor%2Fdek-apps%2Fnette-doctrine%2Fsrc%2FEntityManagerFactory.php&amp;line=84&amp;search=&amp;replace=" class='code'><div><code><span style="color: #06B"><span style="color: #D24; font-weight: bold"><span class='line'>74:</span>            </span><span style="color: #000">$this</span><span style="color: #D24; font-weight: bold">-&gt;</span><span style="color: #000">logger </span><span style="color: #D24; font-weight: bold">= </span><span style="color: #000">$logger </span><span style="color: #D24; font-weight: bold">?? </span><span style="color: #000">false</span><span style="color: #D24; font-weight: bold">;  </span><span style="color: #998; font-style: italic">// false = disabled, null = undefined
<span class='line'>75:</span>            </span><span style="color: #D24; font-weight: bold">return </span><span style="color: #000">$this</span><span style="color: #D24; font-weight: bold">;
<span class='line'>76:</span>        }
<span class='line'>77:</span>    
<span class='line'>78:</span>        </span><span style="color: #998; font-style: italic">/**
<span class='line'>79:</span>         * @return ?SQLLogger
<span class='line'>80:</span>         */
<span class='line'>81:</span>        </span><span style="color: #D24; font-weight: bold">private function </span><span style="color: #000">getDefaultLogger</span><span style="color: #D24; font-weight: bold">(): ?</span><span style="color: #000">SQLLogger
<span class='line'>82:</span>        </span><span style="color: #D24; font-weight: bold">{
<span class='line'>83:</span>            if (</span><span style="color: #000">$this</span><span style="color: #D24; font-weight: bold">-&gt;</span><span style="color: #000">params</span><span style="color: #D24; font-weight: bold">[</span><span style="color: #080">'dev_mode'</span><span style="color: #D24; font-weight: bold">]) {
<span class='highlight'>84:                return new Loggers\FileSQLLogger($this-&gt;params['temp_dir'] . '/log');
</span></span><span style="color: #000"></span><span style="color: #D24; font-weight: bold"></span><span style="color: #000"></span><span style="color: #D24; font-weight: bold"></span><span style="color: #000"></span><span style="color: #D24; font-weight: bold"></span><span style="color: #080"></span><span style="color: #D24; font-weight: bold"></span><span style="color: #080"></span><span style="color: #D24; font-weight: bold"><span class='line'>85:</span>            }
<span class='line'>86:</span>            return </span><span style="color: #000">null</span><span style="color: #D24; font-weight: bold">;
<span class='line'>87:</span>        }
<span class='line'>88:</span>    
</span></span></code></div></pre>

			<table class="args">
<tr><th>$dirname</th><td><pre class="tracy-dump tracy-light"
><span class="tracy-dump-string" title="25 characters"><span>'</span>/web/storage/Doctrine/log<span>'</span></span></pre>
</td></tr>
			</table>
		</div>

		<div class="callstack-file">
			<a href="editor://open/?file=%2Fweb%2Fvendor%2Fdek-apps%2Fnette-doctrine%2Fsrc%2FEntityManagerFactory.php&amp;line=134&amp;search=&amp;replace=" title="/web/vendor/dek-apps/nette-doctrine/src/EntityManagerFactory.php:134" class="tracy-editor">.../dek-apps/nette-doctrine/src/<b>EntityManagerFactory.php</b>:134</a>
		</div>

		<div class="callstack-callee">
			<a href="#" data-tracy-ref="^div + div" class="tracy-toggle tracy-collapsed">DekApps\NetteDoctrine\EntityManagerFactory::<b>getDefaultLogger</b> ()</a>
		</div>

		<div class="callstack-additional tracy-collapsed">
			<pre title="Ctrl-Click to open in editor" data-tracy-href="editor://open/?file=%2Fweb%2Fvendor%2Fdek-apps%2Fnette-doctrine%2Fsrc%2FEntityManagerFactory.php&amp;line=134&amp;search=&amp;replace=" class='code'><div><code><span style="color: #06B"><span style="color: #D24; font-weight: bold"><span class='line'>124:</span>            </span><span style="color: #000">$devMode </span><span style="color: #D24; font-weight: bold">= </span><span style="color: #000">$this</span><span style="color: #D24; font-weight: bold">-&gt;</span><span style="color: #000">params</span><span style="color: #D24; font-weight: bold">[</span><span style="color: #080">'dev_mode'</span><span style="color: #D24; font-weight: bold">];
<span class='line'>125:</span>            </span><span style="color: #000">$paths </span><span style="color: #D24; font-weight: bold">= </span><span style="color: #000">$this</span><span style="color: #D24; font-weight: bold">-&gt;</span><span style="color: #000">params</span><span style="color: #D24; font-weight: bold">[</span><span style="color: #080">'paths'</span><span style="color: #D24; font-weight: bold">];
<span class='line'>126:</span>            </span><span style="color: #000">$tempDir </span><span style="color: #D24; font-weight: bold">= </span><span style="color: #000">$this</span><span style="color: #D24; font-weight: bold">-&gt;</span><span style="color: #000">params</span><span style="color: #D24; font-weight: bold">[</span><span style="color: #080">'temp_dir'</span><span style="color: #D24; font-weight: bold">];
<span class='line'>127:</span>            </span><span style="color: #000">$namespace </span><span style="color: #D24; font-weight: bold">= </span><span style="color: #080">'DoctrineProxies'</span><span style="color: #D24; font-weight: bold">;
<span class='line'>128:</span>            </span><span style="color: #000">$useSimpleAnnotationReader </span><span style="color: #D24; font-weight: bold">= </span><span style="color: #000">false</span><span style="color: #D24; font-weight: bold">;
<span class='line'>129:</span>    
<span class='line'>130:</span>            if (</span><span style="color: #000">$this</span><span style="color: #D24; font-weight: bold">-&gt;</span><span style="color: #000">cache </span><span style="color: #D24; font-weight: bold">=== </span><span style="color: #000">null</span><span style="color: #D24; font-weight: bold">) {
<span class='line'>131:</span>                </span><span style="color: #000">$this</span><span style="color: #D24; font-weight: bold">-&gt;</span><span style="color: #000">cache </span><span style="color: #D24; font-weight: bold">= </span><span style="color: #000">$this</span><span style="color: #D24; font-weight: bold">-&gt;</span><span style="color: #000">getDefaultCache</span><span style="color: #D24; font-weight: bold">();
<span class='line'>132:</span>            }
<span class='line'>133:</span>            if (</span><span style="color: #000">$this</span><span style="color: #D24; font-weight: bold">-&gt;</span><span style="color: #000">logger </span><span style="color: #D24; font-weight: bold">=== </span><span style="color: #000">null</span><span style="color: #D24; font-weight: bold">) {
<span class='highlight'>134:                $this-&gt;logger = $this-&gt;getDefaultLogger();
</span></span><span style="color: #000"></span><span style="color: #D24; font-weight: bold"></span><span style="color: #000"></span><span style="color: #D24; font-weight: bold"></span><span style="color: #000"></span><span style="color: #D24; font-weight: bold"></span><span style="color: #000"></span><span style="color: #D24; font-weight: bold"><span class='line'>135:</span>            }
<span class='line'>136:</span>    
<span class='line'>137:</span>            </span><span style="color: #998; font-style: italic">// Proxy Directory (REQUIRED)
<span class='line'>138:</span>            </span><span style="color: #000">$config</span><span style="color: #D24; font-weight: bold">-&gt;</span><span style="color: #000">setProxyDir</span><span style="color: #D24; font-weight: bold">(</span><span style="color: #000">$tempDir </span><span style="color: #D24; font-weight: bold">. </span><span style="color: #080">'/proxy'</span><span style="color: #D24; font-weight: bold">);
</span></span></code></div></pre>

		</div>

		<div class="callstack-file">
			<a href="editor://open/?file=%2Fweb%2Fvendor%2Fdek-apps%2Fnette-doctrine%2Fsrc%2FEntityManagerFactory.php&amp;line=95&amp;search=&amp;replace=" title="/web/vendor/dek-apps/nette-doctrine/src/EntityManagerFactory.php:95" class="tracy-editor">.../dek-apps/nette-doctrine/src/<b>EntityManagerFactory.php</b>:95</a>
		</div>

		<div class="callstack-callee">
			<a href="#" data-tracy-ref="^div + div" class="tracy-toggle tracy-collapsed">DekApps\NetteDoctrine\EntityManagerFactory::<b>createConfiguration</b> ()</a>
		</div>

		<div class="callstack-additional tracy-collapsed">
			<pre title="Ctrl-Click to open in editor" data-tracy-href="editor://open/?file=%2Fweb%2Fvendor%2Fdek-apps%2Fnette-doctrine%2Fsrc%2FEntityManagerFactory.php&amp;line=95&amp;search=&amp;replace=" class='code'><div><code><span style="color: #06B"><span style="color: #D24; font-weight: bold"><span class='line'>85:</span>            }
<span class='line'>86:</span>            return </span><span style="color: #000">null</span><span style="color: #D24; font-weight: bold">;
<span class='line'>87:</span>        }
<span class='line'>88:</span>    
<span class='line'>89:</span>    
<span class='line'>90:</span>        </span><span style="color: #998; font-style: italic">/**
<span class='line'>91:</span>         * @return EntityManager
<span class='line'>92:</span>         */
<span class='line'>93:</span>        </span><span style="color: #D24; font-weight: bold">public function </span><span style="color: #000">create</span><span style="color: #D24; font-weight: bold">(): </span><span style="color: #000">EntityManager
<span class='line'>94:</span>        </span><span style="color: #D24; font-weight: bold">{
<span class='highlight'>95:            $configuration = $this-&gt;createConfiguration();
</span></span><span style="color: #000"></span><span style="color: #D24; font-weight: bold"></span><span style="color: #000"></span><span style="color: #D24; font-weight: bold"></span><span style="color: #000"></span><span style="color: #D24; font-weight: bold"><span class='line'>96:</span>            </span><span style="color: #998; font-style: italic">// $annotationDriver = $configuration-&gt;getMetadataDriverImpl();
<span class='line'>97:</span>            // $annotationReader = $annotationDriver-&gt;getReader();
<span class='line'>98:</span>    
<span class='line'>99:</span>            </span><span style="color: #000">$em </span><span style="color: #D24; font-weight: bold">= </span><span style="color: #000">EntityManager</span><span style="color: #D24; font-weight: bold">::</span><span style="color: #000">create</span><span style="color: #D24; font-weight: bold">(</span><span style="color: #000">$this</span><span style="color: #D24; font-weight: bold">-&gt;</span><span style="color: #000">params</span><span style="color: #D24; font-weight: bold">[</span><span style="color: #080">'db_params'</span><span style="color: #D24; font-weight: bold">], </span><span style="color: #000">$configuration</span><span style="color: #D24; font-weight: bold">);
</span></span></code></div></pre>

		</div>

		<div class="callstack-file">
			<a href="editor://open/?file=%2Fweb%2Fapp%2FProviders%2FDoctrineServiceProvider.php&amp;line=20&amp;search=&amp;replace=" title="/web/app/Providers/DoctrineServiceProvider.php:20" class="tracy-editor">/web/app/Providers/<b>DoctrineServiceProvider.php</b>:20</a>
		</div>

		<div class="callstack-callee">
			<a href="#" data-tracy-ref="^div + div" class="tracy-toggle tracy-collapsed">DekApps\NetteDoctrine\EntityManagerFactory::<b>create</b> ()</a>
		</div>

		<div class="callstack-additional tracy-collapsed">
			<pre title="Ctrl-Click to open in editor" data-tracy-href="editor://open/?file=%2Fweb%2Fapp%2FProviders%2FDoctrineServiceProvider.php&amp;line=20&amp;search=&amp;replace=" class='code'><div><code><span style="color: #06B"><span style="color: #D24; font-weight: bold"><span class='line'>10:</span>    class </span><span style="color: #000">DoctrineServiceProvider </span><span style="color: #D24; font-weight: bold">extends </span><span style="color: #000">ServiceProvider
<span class='line'>11:</span>    </span><span style="color: #D24; font-weight: bold">{
<span class='line'>12:</span>        </span><span style="color: #998; font-style: italic">/**
<span class='line'>13:</span>         * Register services.
<span class='line'>14:</span>         *
<span class='line'>15:</span>         * @return void
<span class='line'>16:</span>         */
<span class='line'>17:</span>        </span><span style="color: #D24; font-weight: bold">public function </span><span style="color: #000">register</span><span style="color: #D24; font-weight: bold">()
<span class='line'>18:</span>        {
<span class='line'>19:</span>            </span><span style="color: #000">$this</span><span style="color: #D24; font-weight: bold">-&gt;</span><span style="color: #000">app</span><span style="color: #D24; font-weight: bold">-&gt;</span><span style="color: #000">singleton</span><span style="color: #D24; font-weight: bold">(</span><span style="color: #000">EntityManager</span><span style="color: #D24; font-weight: bold">::class, function (</span><span style="color: #000">$app</span><span style="color: #D24; font-weight: bold">) {
<span class='highlight'>20:                return (new EntityManagerFactory(config('doctrine.orm')))-&gt;create();
</span></span><span style="color: #000"></span><span style="color: #D24; font-weight: bold"></span><span style="color: #000"></span><span style="color: #D24; font-weight: bold"></span><span style="color: #080"></span><span style="color: #D24; font-weight: bold"></span><span style="color: #000"></span><span style="color: #D24; font-weight: bold"><span class='line'>21:</span>            });
<span class='line'>22:</span>        }
<span class='line'>23:</span>    
<span class='line'>24:</span>        </span><span style="color: #998; font-style: italic">/**
</span></span></code></div></pre>

		</div>

		<div class="callstack-file">
			<a href="editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FContainer%2FContainer.php&amp;line=873&amp;search=&amp;replace=" title="/web/vendor/laravel/framework/src/Illuminate/Container/Container.php:873" class="tracy-editor">.../laravel/framework/src/Illuminate/Container/<b>Container.php</b>:873</a>
		</div>

		<div class="callstack-callee">
			<a href="#" data-tracy-ref="^div + div" class="tracy-toggle tracy-collapsed">App\Providers\DoctrineServiceProvider::<b>App\Providers\{closure}</b> (...)</a>
		</div>

		<div class="callstack-additional tracy-collapsed">
			<pre title="Ctrl-Click to open in editor" data-tracy-href="editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FContainer%2FContainer.php&amp;line=873&amp;search=&amp;replace=" class='code'><div><code><span style="color: #06B"><span style="color: #998; font-style: italic"><span class='line'>863:</span>         *
<span class='line'>864:</span>         * @throws \Illuminate\Contracts\Container\BindingResolutionException
<span class='line'>865:</span>         * @throws \Illuminate\Contracts\Container\CircularDependencyException
<span class='line'>866:</span>         */
<span class='line'>867:</span>        </span><span style="color: #D24; font-weight: bold">public function </span><span style="color: #000">build</span><span style="color: #D24; font-weight: bold">(</span><span style="color: #000">$concrete</span><span style="color: #D24; font-weight: bold">)
<span class='line'>868:</span>        {
<span class='line'>869:</span>            </span><span style="color: #998; font-style: italic">// If the concrete type is actually a Closure, we will just execute it and
<span class='line'>870:</span>            // hand back the results of the functions, which allows functions to be
<span class='line'>871:</span>            // used as resolvers for more fine-tuned resolution of these objects.
<span class='line'>872:</span>            </span><span style="color: #D24; font-weight: bold">if (</span><span style="color: #000">$concrete </span><span style="color: #D24; font-weight: bold">instanceof </span><span style="color: #000">Closure</span><span style="color: #D24; font-weight: bold">) {
<span class='highlight'>873:                return $concrete($this, $this-&gt;getLastParameterOverride());
</span></span><span style="color: #000"></span><span style="color: #D24; font-weight: bold"></span><span style="color: #000"></span><span style="color: #D24; font-weight: bold"></span><span style="color: #000"></span><span style="color: #D24; font-weight: bold"></span><span style="color: #000"></span><span style="color: #D24; font-weight: bold"><span class='line'>874:</span>            }
<span class='line'>875:</span>    
<span class='line'>876:</span>            try {
<span class='line'>877:</span>                </span><span style="color: #000">$reflector </span><span style="color: #D24; font-weight: bold">= new </span><span style="color: #000">ReflectionClass</span><span style="color: #D24; font-weight: bold">(</span><span style="color: #000">$concrete</span><span style="color: #D24; font-weight: bold">);
</span></span></code></div></pre>

			<table class="args">
<tr><th>#0</th><td><pre class="tracy-dump tracy-light" data-tracy-dump='{"ref":2}'></pre>
</td></tr>
<tr><th>#1</th><td><pre class="tracy-dump tracy-light"><span class="tracy-dump-array">array</span> (0)</pre>
</td></tr>
			</table>
		</div>

		<div class="callstack-file">
			<a href="editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FContainer%2FContainer.php&amp;line=758&amp;search=&amp;replace=" title="/web/vendor/laravel/framework/src/Illuminate/Container/Container.php:758" class="tracy-editor">.../laravel/framework/src/Illuminate/Container/<b>Container.php</b>:758</a>
		</div>

		<div class="callstack-callee">
			<a href="#" data-tracy-ref="^div + div" class="tracy-toggle tracy-collapsed">Illuminate\Container\Container::<b>build</b> (...)</a>
		</div>

		<div class="callstack-additional tracy-collapsed">
			<pre title="Ctrl-Click to open in editor" data-tracy-href="editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FContainer%2FContainer.php&amp;line=758&amp;search=&amp;replace=" class='code'><div><code><span style="color: #06B"><span style="color: #D24; font-weight: bold"><span class='line'>748:</span>            </span><span style="color: #000">$this</span><span style="color: #D24; font-weight: bold">-&gt;</span><span style="color: #000">with</span><span style="color: #D24; font-weight: bold">[] = </span><span style="color: #000">$parameters</span><span style="color: #D24; font-weight: bold">;
<span class='line'>749:</span>    
<span class='line'>750:</span>            if (</span><span style="color: #000">is_null</span><span style="color: #D24; font-weight: bold">(</span><span style="color: #000">$concrete</span><span style="color: #D24; font-weight: bold">)) {
<span class='line'>751:</span>                </span><span style="color: #000">$concrete </span><span style="color: #D24; font-weight: bold">= </span><span style="color: #000">$this</span><span style="color: #D24; font-weight: bold">-&gt;</span><span style="color: #000">getConcrete</span><span style="color: #D24; font-weight: bold">(</span><span style="color: #000">$abstract</span><span style="color: #D24; font-weight: bold">);
<span class='line'>752:</span>            }
<span class='line'>753:</span>    
<span class='line'>754:</span>            </span><span style="color: #998; font-style: italic">// We're ready to instantiate an instance of the concrete type registered for
<span class='line'>755:</span>            // the binding. This will instantiate the types, as well as resolve any of
<span class='line'>756:</span>            // its "nested" dependencies recursively until all have gotten resolved.
<span class='line'>757:</span>            </span><span style="color: #D24; font-weight: bold">if (</span><span style="color: #000">$this</span><span style="color: #D24; font-weight: bold">-&gt;</span><span style="color: #000">isBuildable</span><span style="color: #D24; font-weight: bold">(</span><span style="color: #000">$concrete</span><span style="color: #D24; font-weight: bold">, </span><span style="color: #000">$abstract</span><span style="color: #D24; font-weight: bold">)) {
<span class='highlight'>758:                $object = $this-&gt;build($concrete);
</span></span><span style="color: #000"></span><span style="color: #D24; font-weight: bold"></span><span style="color: #000"></span><span style="color: #D24; font-weight: bold"></span><span style="color: #000"></span><span style="color: #D24; font-weight: bold"></span><span style="color: #000"></span><span style="color: #D24; font-weight: bold"><span class='line'>759:</span>            } else {
<span class='line'>760:</span>                </span><span style="color: #000">$object </span><span style="color: #D24; font-weight: bold">= </span><span style="color: #000">$this</span><span style="color: #D24; font-weight: bold">-&gt;</span><span style="color: #000">make</span><span style="color: #D24; font-weight: bold">(</span><span style="color: #000">$concrete</span><span style="color: #D24; font-weight: bold">);
<span class='line'>761:</span>            }
<span class='line'>762:</span>    
</span></span></code></div></pre>

			<table class="args">
<tr><th>$concrete</th><td><pre class="tracy-dump tracy-light" data-tracy-dump='{"ref":173}'></pre>
</td></tr>
			</table>
		</div>

		<div class="callstack-file">
			<a href="editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2FApplication.php&amp;line=855&amp;search=&amp;replace=" title="/web/vendor/laravel/framework/src/Illuminate/Foundation/Application.php:855" class="tracy-editor">.../framework/src/Illuminate/Foundation/<b>Application.php</b>:855</a>
		</div>

		<div class="callstack-callee">
			<a href="#" data-tracy-ref="^div + div" class="tracy-toggle tracy-collapsed">Illuminate\Container\Container::<b>resolve</b> (...)</a>
		</div>

		<div class="callstack-additional tracy-collapsed">
			<pre title="Ctrl-Click to open in editor" data-tracy-href="editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2FApplication.php&amp;line=855&amp;search=&amp;replace=" class='code'><div><code><span style="color: #06B"><span style="color: #998; font-style: italic"><span class='line'>845:</span>         *
<span class='line'>846:</span>         * @param  string  $abstract
<span class='line'>847:</span>         * @param  array  $parameters
<span class='line'>848:</span>         * @param  bool  $raiseEvents
<span class='line'>849:</span>         * @return mixed
<span class='line'>850:</span>         */
<span class='line'>851:</span>        </span><span style="color: #D24; font-weight: bold">protected function </span><span style="color: #000">resolve</span><span style="color: #D24; font-weight: bold">(</span><span style="color: #000">$abstract</span><span style="color: #D24; font-weight: bold">, </span><span style="color: #000">$parameters </span><span style="color: #D24; font-weight: bold">= [], </span><span style="color: #000">$raiseEvents </span><span style="color: #D24; font-weight: bold">= </span><span style="color: #000">true</span><span style="color: #D24; font-weight: bold">)
<span class='line'>852:</span>        {
<span class='line'>853:</span>            </span><span style="color: #000">$this</span><span style="color: #D24; font-weight: bold">-&gt;</span><span style="color: #000">loadDeferredProviderIfNeeded</span><span style="color: #D24; font-weight: bold">(</span><span style="color: #000">$abstract </span><span style="color: #D24; font-weight: bold">= </span><span style="color: #000">$this</span><span style="color: #D24; font-weight: bold">-&gt;</span><span style="color: #000">getAlias</span><span style="color: #D24; font-weight: bold">(</span><span style="color: #000">$abstract</span><span style="color: #D24; font-weight: bold">));
<span class='line'>854:</span>    
<span class='highlight'>855:            return parent::resolve($abstract, $parameters, $raiseEvents);
</span></span><span style="color: #000"></span><span style="color: #D24; font-weight: bold"></span><span style="color: #000"></span><span style="color: #D24; font-weight: bold"></span><span style="color: #000"></span><span style="color: #D24; font-weight: bold"></span><span style="color: #000"></span><span style="color: #D24; font-weight: bold"></span><span style="color: #000"></span><span style="color: #D24; font-weight: bold"><span class='line'>856:</span>        }
<span class='line'>857:</span>    
<span class='line'>858:</span>        </span><span style="color: #998; font-style: italic">/**
<span class='line'>859:</span>         * Load the deferred provider if the given type is a deferred service and the instance has not been loaded.
</span></span></code></div></pre>

			<table class="args">
<tr><th>$abstract</th><td><pre class="tracy-dump tracy-light"
><span class="tracy-dump-string" title="26 characters"><span>'</span>Doctrine\ORM\EntityManager<span>'</span></span></pre>
</td></tr>
<tr><th>$parameters</th><td><pre class="tracy-dump tracy-light"><span class="tracy-dump-array">array</span> (0)</pre>
</td></tr>
<tr><th>$raiseEvents</th><td><pre class="tracy-dump tracy-light"><span class="tracy-dump-bool">true</span></pre>
</td></tr>
			</table>
		</div>

		<div class="callstack-file">
			<a href="editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FContainer%2FContainer.php&amp;line=694&amp;search=&amp;replace=" title="/web/vendor/laravel/framework/src/Illuminate/Container/Container.php:694" class="tracy-editor">.../laravel/framework/src/Illuminate/Container/<b>Container.php</b>:694</a>
		</div>

		<div class="callstack-callee">
			<a href="#" data-tracy-ref="^div + div" class="tracy-toggle tracy-collapsed">Illuminate\Foundation\Application::<b>resolve</b> (...)</a>
		</div>

		<div class="callstack-additional tracy-collapsed">
			<pre title="Ctrl-Click to open in editor" data-tracy-href="editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FContainer%2FContainer.php&amp;line=694&amp;search=&amp;replace=" class='code'><div><code><span style="color: #06B"><span style="color: #998; font-style: italic"><span class='line'>684:</span>         * Resolve the given type from the container.
<span class='line'>685:</span>         *
<span class='line'>686:</span>         * @param  string|callable  $abstract
<span class='line'>687:</span>         * @param  array  $parameters
<span class='line'>688:</span>         * @return mixed
<span class='line'>689:</span>         *
<span class='line'>690:</span>         * @throws \Illuminate\Contracts\Container\BindingResolutionException
<span class='line'>691:</span>         */
<span class='line'>692:</span>        </span><span style="color: #D24; font-weight: bold">public function </span><span style="color: #000">make</span><span style="color: #D24; font-weight: bold">(</span><span style="color: #000">$abstract</span><span style="color: #D24; font-weight: bold">, array </span><span style="color: #000">$parameters </span><span style="color: #D24; font-weight: bold">= [])
<span class='line'>693:</span>        {
<span class='highlight'>694:            return $this-&gt;resolve($abstract, $parameters);
</span></span><span style="color: #000"></span><span style="color: #D24; font-weight: bold"></span><span style="color: #000"></span><span style="color: #D24; font-weight: bold"></span><span style="color: #000"></span><span style="color: #D24; font-weight: bold"></span><span style="color: #000"></span><span style="color: #D24; font-weight: bold"><span class='line'>695:</span>        }
<span class='line'>696:</span>    
<span class='line'>697:</span>        </span><span style="color: #998; font-style: italic">/**
<span class='line'>698:</span>         * {@inheritdoc}
</span></span></code></div></pre>

			<table class="args">
<tr><th>$abstract</th><td><pre class="tracy-dump tracy-light"
><span class="tracy-dump-string" title="26 characters"><span>'</span>Doctrine\ORM\EntityManager<span>'</span></span></pre>
</td></tr>
<tr><th>$parameters</th><td><pre class="tracy-dump tracy-light"><span class="tracy-dump-array">array</span> (0)</pre>
</td></tr>
			</table>
		</div>

		<div class="callstack-file">
			<a href="editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2FApplication.php&amp;line=840&amp;search=&amp;replace=" title="/web/vendor/laravel/framework/src/Illuminate/Foundation/Application.php:840" class="tracy-editor">.../framework/src/Illuminate/Foundation/<b>Application.php</b>:840</a>
		</div>

		<div class="callstack-callee">
			<a href="#" data-tracy-ref="^div + div" class="tracy-toggle tracy-collapsed">Illuminate\Container\Container::<b>make</b> (...)</a>
		</div>

		<div class="callstack-additional tracy-collapsed">
			<pre title="Ctrl-Click to open in editor" data-tracy-href="editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2FApplication.php&amp;line=840&amp;search=&amp;replace=" class='code'><div><code><span style="color: #06B"><span style="color: #998; font-style: italic"><span class='line'>830:</span>         * Resolve the given type from the container.
<span class='line'>831:</span>         *
<span class='line'>832:</span>         * @param  string  $abstract
<span class='line'>833:</span>         * @param  array  $parameters
<span class='line'>834:</span>         * @return mixed
<span class='line'>835:</span>         */
<span class='line'>836:</span>        </span><span style="color: #D24; font-weight: bold">public function </span><span style="color: #000">make</span><span style="color: #D24; font-weight: bold">(</span><span style="color: #000">$abstract</span><span style="color: #D24; font-weight: bold">, array </span><span style="color: #000">$parameters </span><span style="color: #D24; font-weight: bold">= [])
<span class='line'>837:</span>        {
<span class='line'>838:</span>            </span><span style="color: #000">$this</span><span style="color: #D24; font-weight: bold">-&gt;</span><span style="color: #000">loadDeferredProviderIfNeeded</span><span style="color: #D24; font-weight: bold">(</span><span style="color: #000">$abstract </span><span style="color: #D24; font-weight: bold">= </span><span style="color: #000">$this</span><span style="color: #D24; font-weight: bold">-&gt;</span><span style="color: #000">getAlias</span><span style="color: #D24; font-weight: bold">(</span><span style="color: #000">$abstract</span><span style="color: #D24; font-weight: bold">));
<span class='line'>839:</span>    
<span class='highlight'>840:            return parent::make($abstract, $parameters);
</span></span><span style="color: #000"></span><span style="color: #D24; font-weight: bold"></span><span style="color: #000"></span><span style="color: #D24; font-weight: bold"></span><span style="color: #000"></span><span style="color: #D24; font-weight: bold"></span><span style="color: #000"></span><span style="color: #D24; font-weight: bold"><span class='line'>841:</span>        }
<span class='line'>842:</span>    
<span class='line'>843:</span>        </span><span style="color: #998; font-style: italic">/**
<span class='line'>844:</span>         * Resolve the given type from the container.
</span></span></code></div></pre>

			<table class="args">
<tr><th>$abstract</th><td><pre class="tracy-dump tracy-light"
><span class="tracy-dump-string" title="26 characters"><span>'</span>Doctrine\ORM\EntityManager<span>'</span></span></pre>
</td></tr>
<tr><th>$parameters</th><td><pre class="tracy-dump tracy-light"><span class="tracy-dump-array">array</span> (0)</pre>
</td></tr>
			</table>
		</div>

		<div class="callstack-file">
			<a href="editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fhelpers.php&amp;line=119&amp;search=&amp;replace=" title="/web/vendor/laravel/framework/src/Illuminate/Foundation/helpers.php:119" class="tracy-editor">.../laravel/framework/src/Illuminate/Foundation/<b>helpers.php</b>:119</a>
		</div>

		<div class="callstack-callee">
			<a href="#" data-tracy-ref="^div + div" class="tracy-toggle tracy-collapsed">Illuminate\Foundation\Application::<b>make</b> (...)</a>
		</div>

		<div class="callstack-additional tracy-collapsed">
			<pre title="Ctrl-Click to open in editor" data-tracy-href="editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fhelpers.php&amp;line=119&amp;search=&amp;replace=" class='code'><div><code><span style="color: #06B"><span style="color: #998; font-style: italic"><span class='line'>109:</span>         * @param  string|null  $abstract
<span class='line'>110:</span>         * @param  array  $parameters
<span class='line'>111:</span>         * @return mixed|\Illuminate\Contracts\Foundation\Application
<span class='line'>112:</span>         */
<span class='line'>113:</span>        </span><span style="color: #D24; font-weight: bold">function </span><span style="color: #000">app</span><span style="color: #D24; font-weight: bold">(</span><span style="color: #000">$abstract </span><span style="color: #D24; font-weight: bold">= </span><span style="color: #000">null</span><span style="color: #D24; font-weight: bold">, array </span><span style="color: #000">$parameters </span><span style="color: #D24; font-weight: bold">= [])
<span class='line'>114:</span>        {
<span class='line'>115:</span>            if (</span><span style="color: #000">is_null</span><span style="color: #D24; font-weight: bold">(</span><span style="color: #000">$abstract</span><span style="color: #D24; font-weight: bold">)) {
<span class='line'>116:</span>                return </span><span style="color: #000">Container</span><span style="color: #D24; font-weight: bold">::</span><span style="color: #000">getInstance</span><span style="color: #D24; font-weight: bold">();
<span class='line'>117:</span>            }
<span class='line'>118:</span>    
<span class='highlight'>119:            return Container::getInstance()-&gt;make($abstract, $parameters);
</span></span><span style="color: #000"></span><span style="color: #D24; font-weight: bold"></span><span style="color: #000"></span><span style="color: #D24; font-weight: bold"></span><span style="color: #000"></span><span style="color: #D24; font-weight: bold"></span><span style="color: #000"></span><span style="color: #D24; font-weight: bold"></span><span style="color: #000"></span><span style="color: #D24; font-weight: bold"><span class='line'>120:</span>        }
<span class='line'>121:</span>    }
<span class='line'>122:</span>    
<span class='line'>123:</span>    if (! </span><span style="color: #000">function_exists</span><span style="color: #D24; font-weight: bold">(</span><span style="color: #080">'app_path'</span><span style="color: #D24; font-weight: bold">)) {
</span></span></code></div></pre>

			<table class="args">
<tr><th>$abstract</th><td><pre class="tracy-dump tracy-light"
><span class="tracy-dump-string" title="26 characters"><span>'</span>Doctrine\ORM\EntityManager<span>'</span></span></pre>
</td></tr>
<tr><th>$parameters</th><td><pre class="tracy-dump tracy-light"><span class="tracy-dump-array">array</span> (0)</pre>
</td></tr>
			</table>
		</div>

		<div class="callstack-file">
			<a href="editor://open/?file=%2Fweb%2Fvendor%2Fdek-apps%2Fnette-doctrine%2Fsrc%2FConsoleBootstrap%2FLaravelConsoleBootstrap.php&amp;line=24&amp;search=&amp;replace=" title="/web/vendor/dek-apps/nette-doctrine/src/ConsoleBootstrap/LaravelConsoleBootstrap.php:24" class="tracy-editor">.../nette-doctrine/src/ConsoleBootstrap/<b>LaravelConsoleBootstrap.php</b>:24</a>
		</div>

		<div class="callstack-callee">
			<a href="#" data-tracy-ref="^div + div" class="tracy-toggle tracy-collapsed"><b>app</b> (...)</a>
		</div>

		<div class="callstack-additional tracy-collapsed">
			<pre title="Ctrl-Click to open in editor" data-tracy-href="editor://open/?file=%2Fweb%2Fvendor%2Fdek-apps%2Fnette-doctrine%2Fsrc%2FConsoleBootstrap%2FLaravelConsoleBootstrap.php&amp;line=24&amp;search=&amp;replace=" class='code'><div><code><span style="color: #06B"><span style="color: #D24; font-weight: bold"><span class='line'>14:</span>    class </span><span style="color: #000">LaravelConsoleBootstrap </span><span style="color: #D24; font-weight: bold">implements </span><span style="color: #000">IConsoleBootstrap
<span class='line'>15:</span>    </span><span style="color: #D24; font-weight: bold">{
<span class='line'>16:</span>    
<span class='line'>17:</span>        public function </span><span style="color: #000">__construct</span><span style="color: #D24; font-weight: bold">(</span><span style="color: #000">string $root</span><span style="color: #D24; font-weight: bold">)
<span class='line'>18:</span>        {
<span class='line'>19:</span>            </span><span style="color: #000">$this</span><span style="color: #D24; font-weight: bold">-&gt;</span><span style="color: #000">boot</span><span style="color: #D24; font-weight: bold">(</span><span style="color: #000">$root</span><span style="color: #D24; font-weight: bold">);
<span class='line'>20:</span>        }
<span class='line'>21:</span>    
<span class='line'>22:</span>        public function </span><span style="color: #000">getEntityManager</span><span style="color: #D24; font-weight: bold">(): </span><span style="color: #000">EntityManager
<span class='line'>23:</span>        </span><span style="color: #D24; font-weight: bold">{
<span class='highlight'>24:            $entityManager = app(EntityManager::class);
</span></span><span style="color: #000"></span><span style="color: #D24; font-weight: bold"></span><span style="color: #000"></span><span style="color: #D24; font-weight: bold"></span><span style="color: #000"></span><span style="color: #D24; font-weight: bold"><span class='line'>25:</span>            if (!</span><span style="color: #000">$entityManager</span><span style="color: #D24; font-weight: bold">) {
<span class='line'>26:</span>                throw new </span><span style="color: #000">\Exception</span><span style="color: #D24; font-weight: bold">(</span><span style="color: #080">'Doctrine ORM is not correctly configured.'</span><span style="color: #D24; font-weight: bold">);
<span class='line'>27:</span>            }
<span class='line'>28:</span>            return </span><span style="color: #000">$entityManager</span><span style="color: #D24; font-weight: bold">;
</span></span></code></div></pre>

			<table class="args">
<tr><th>$abstract</th><td><pre class="tracy-dump tracy-light"
><span class="tracy-dump-string" title="26 characters"><span>'</span>Doctrine\ORM\EntityManager<span>'</span></span></pre>
</td></tr>
			</table>
		</div>

		<div class="callstack-file">
			<a href="editor://open/?file=%2Fweb%2Fvendor%2Fdek-apps%2Fnette-doctrine%2Fbin%2Fconsole.php&amp;line=88&amp;search=&amp;replace=" title="/web/vendor/dek-apps/nette-doctrine/bin/console.php:88" class="tracy-editor">.../web/vendor/dek-apps/nette-doctrine/bin/<b>console.php</b>:88</a>
		</div>

		<div class="callstack-callee">
			<a href="#" data-tracy-ref="^div + div" class="tracy-toggle tracy-collapsed">DekApps\NetteDoctrine\ConsoleBootstrap\LaravelConsoleBootstrap::<b>getEntityManager</b> ()</a>
		</div>

		<div class="callstack-additional tracy-collapsed">
			<pre title="Ctrl-Click to open in editor" data-tracy-href="editor://open/?file=%2Fweb%2Fvendor%2Fdek-apps%2Fnette-doctrine%2Fbin%2Fconsole.php&amp;line=88&amp;search=&amp;replace=" class='code'><div><code><span style="color: #06B"><span style="color: #998; font-style: italic"><span class='line'>78:</span>    if (!$orm || !$entityManager) {
<span class='line'>79:</span>        error('Doctrine ORM is not correctly configured.');
<span class='line'>80:</span>    }*/
<span class='line'>81:</span>    
<span class='line'>82:</span>    </span><span style="color: #000">$bootstrap </span><span style="color: #D24; font-weight: bold">= new </span><span style="color: #000">LaravelConsoleBootstrap</span><span style="color: #D24; font-weight: bold">(</span><span style="color: #000">$root</span><span style="color: #D24; font-weight: bold">);
<span class='line'>83:</span>    
<span class='line'>84:</span>    </span><span style="color: #000">$parameters </span><span style="color: #D24; font-weight: bold">= </span><span style="color: #000">$bootstrap</span><span style="color: #D24; font-weight: bold">-&gt;</span><span style="color: #000">getConfig</span><span style="color: #D24; font-weight: bold">();
<span class='line'>85:</span>    </span><span style="color: #000">$orm </span><span style="color: #D24; font-weight: bold">= </span><span style="color: #000">$parameters </span><span style="color: #D24; font-weight: bold">? </span><span style="color: #000">true </span><span style="color: #D24; font-weight: bold">: </span><span style="color: #000">false</span><span style="color: #D24; font-weight: bold">;
<span class='line'>86:</span>    </span><span style="color: #000">$migrations </span><span style="color: #D24; font-weight: bold">= </span><span style="color: #000">$parameters</span><span style="color: #D24; font-weight: bold">[</span><span style="color: #080">'migrations'</span><span style="color: #D24; font-weight: bold">] ?? </span><span style="color: #000">null</span><span style="color: #D24; font-weight: bold">;
<span class='line'>87:</span>    
<span class='highlight'>88:    $entityManager = $bootstrap-&gt;getEntityManager();
</span></span><span style="color: #000"></span><span style="color: #D24; font-weight: bold"></span><span style="color: #000"></span><span style="color: #D24; font-weight: bold"></span><span style="color: #000"></span><span style="color: #D24; font-weight: bold"><span class='line'>89:</span>    if (!</span><span style="color: #000">$orm </span><span style="color: #D24; font-weight: bold">|| !</span><span style="color: #000">$entityManager</span><span style="color: #D24; font-weight: bold">) {
<span class='line'>90:</span>        </span><span style="color: #000">error</span><span style="color: #D24; font-weight: bold">(</span><span style="color: #080">'Doctrine ORM is not correctly configured.'</span><span style="color: #D24; font-weight: bold">);
<span class='line'>91:</span>    }
<span class='line'>92:</span>    
</span></span></code></div></pre>

		</div>

		<div class="callstack-file">
			<a href="editor://open/?file=%2Fweb%2Fvendor%2Fdek-apps%2Fnette-doctrine%2Fbin%2Fconsole&amp;line=4&amp;search=&amp;replace=" title="/web/vendor/dek-apps/nette-doctrine/bin/console:4" class="tracy-editor">/web/vendor/dek-apps/nette-doctrine/bin/<b>console</b>:4</a>
		</div>

		<div class="callstack-callee">
			<a href="#" data-tracy-ref="^div + div" class="tracy-toggle tracy-collapsed"><b>include</b> (...)</a>
		</div>

		<div class="callstack-additional tracy-collapsed">
			<pre title="Ctrl-Click to open in editor" data-tracy-href="editor://open/?file=%2Fweb%2Fvendor%2Fdek-apps%2Fnette-doctrine%2Fbin%2Fconsole&amp;line=4&amp;search=&amp;replace=" class='code'><div><code><span style="color: #06B"><span class='line'>1:</span>    #!/usr/bin/env php
<span class='line'>2:</span>    <span style="color: #000">&lt;?php
<span class='line'>3:</span>    
<span class='highlight'>4:    include('console.php');
</span></span><span style="color: #D24; font-weight: bold"></span><span style="color: #080"></span><span style="color: #D24; font-weight: bold"><span class='line'>5:</span>    </span>
</span></code></div></pre>

			<table class="args">
<tr><th>#0</th><td><pre class="tracy-dump tracy-light"
><span class="tracy-dump-string" title="51 characters"><span>'</span>/web/vendor/dek-apps/nette-doctrine/bin/console.php<span>'</span></span></pre>
</td></tr>
			</table>
		</div>

		<div class="callstack-file">
			<a href="editor://open/?file=%2Fweb%2Fvendor%2Fbin%2Fconsole&amp;line=107&amp;search=&amp;replace=" title="/web/vendor/bin/console:107" class="tracy-editor">/web/vendor/bin/<b>console</b>:107</a>
		</div>

		<div class="callstack-callee">
			<a href="#" data-tracy-ref="^div + div" class="tracy-toggle tracy-collapsed"><b>include</b> (...)</a>
		</div>

		<div class="callstack-additional tracy-collapsed">
			<pre title="Ctrl-Click to open in editor" data-tracy-href="editor://open/?file=%2Fweb%2Fvendor%2Fbin%2Fconsole&amp;line=107&amp;search=&amp;replace=" class='code'><div><code><span style="color: #06B"><span style="color: #D24; font-weight: bold"><span class='line'> 97:</span>                }
<span class='line'> 98:</span>            }
<span class='line'> 99:</span>        }
<span class='line'>100:</span>    
<span class='line'>101:</span>        if (</span><span style="color: #000">function_exists</span><span style="color: #D24; font-weight: bold">(</span><span style="color: #080">'stream_wrapper_register'</span><span style="color: #D24; font-weight: bold">) &amp;&amp; </span><span style="color: #000">stream_wrapper_register</span><span style="color: #D24; font-weight: bold">(</span><span style="color: #080">'phpvfscomposer'</span><span style="color: #D24; font-weight: bold">, </span><span style="color: #080">'Composer\BinProxyWrapper'</span><span style="color: #D24; font-weight: bold">)) {
<span class='line'>102:</span>            include(</span><span style="color: #080">"phpvfscomposer://" </span><span style="color: #D24; font-weight: bold">. </span><span style="color: #000">__DIR__ </span><span style="color: #D24; font-weight: bold">. </span><span style="color: #080">'/..'</span><span style="color: #D24; font-weight: bold">.</span><span style="color: #080">'/dek-apps/nette-doctrine/bin/console'</span><span style="color: #D24; font-weight: bold">);
<span class='line'>103:</span>            exit(</span><span style="color: #000">0</span><span style="color: #D24; font-weight: bold">);
<span class='line'>104:</span>        }
<span class='line'>105:</span>    }
<span class='line'>106:</span>    
<span class='highlight'>107:    include __DIR__ . '/..'.'/dek-apps/nette-doctrine/bin/console';
</span></span><span style="color: #000"></span><span style="color: #D24; font-weight: bold"></span><span style="color: #080"></span><span style="color: #D24; font-weight: bold"></span><span style="color: #080"></span><span style="color: #D24; font-weight: bold"><span class='line'>108:</span>    </span>
</span></code></div></pre>

			<table class="args">
<tr><th>#0</th><td><pre class="tracy-dump tracy-light"
><span class="tracy-dump-string" title="47 characters"><span>'</span>/web/vendor/dek-apps/nette-doctrine/bin/console<span>'</span></span></pre>
</td></tr>
			</table>
		</div>
	</div>
	</div>
</section>


<section class="section">
	<h2 class="section-label"><a href="#" data-tracy-ref="^+" class="tracy-toggle tracy-collapsed">Exception</a></h2>
	<div class="section-panel tracy-collapsed">
		<pre class="tracy-dump tracy-light" data-tracy-dump='{"ref":389}'></pre>
	</div>
</section>




<section class="section">
	<h2 class="section-label"><a href="#" data-tracy-ref="^+" class="tracy-toggle tracy-collapsed">Environment</a></h2>

	<div class="section-panel tracy-collapsed">

	<div class="tracy-tabs">
		<ul class="tabs-bar">
			<li class="tracy-tab-label tracy-active"><a href="#">$_SERVER</a></li>
			<li class="tracy-tab-label"><a href="#">Constants</a></li>
			<li class="tracy-tab-label"><a href="#">Configuration</a></li>

		</ul>


		<div>
			<div class="tracy-tab-panel pane tracy-active">
				<table class="tracy-sortable">
				<tr><th>no_proxy</th><td><pre class="tracy-dump tracy-light"
><span class="tracy-dump-string" title="29 characters"><span>'</span>composer.dek.cz,debian.dek.cz<span>'</span></span></pre>
</td></tr>
				<tr><th>HOSTNAME</th><td><pre class="tracy-dump tracy-light"
><span class="tracy-dump-string" title="12 characters"><span>'</span>9246855c23c5<span>'</span></span></pre>
</td></tr>
				<tr><th>PHP_VERSION</th><td><pre class="tracy-dump tracy-light"><span class="tracy-dump-string" title="6 characters"><span>'</span>8.0.14<span>'</span></span></pre>
</td></tr>
				<tr><th>PHP_MD5</th><td><pre class="tracy-dump tracy-light"><span class="tracy-dump-string"><span>'</span><span>'</span></span></pre>
</td></tr>
				<tr><th>PHP_INI_DIR</th><td><pre class="tracy-dump tracy-light"
><span class="tracy-dump-string" title="18 characters"><span>'</span>/usr/local/etc/php<span>'</span></span></pre>
</td></tr>
				<tr><th>GPG_KEYS</th><td><pre class="tracy-dump tracy-light"
><span class="tracy-dump-string" title="81 characters"><span>'</span>1729F83938DA44E27BA0F4D3DBDB397470D12172 BFDDD28642824F8118EF77909B67A5C12229118F<span>'</span></span></pre>
</td></tr>
				<tr><th>PHP_LDFLAGS</th><td><pre class="tracy-dump tracy-light"
><span class="tracy-dump-string" title="12 characters"><span>'</span>-Wl,-O1 -pie<span>'</span></span></pre>
</td></tr>
				<tr><th>PWD</th><td><pre class="tracy-dump tracy-light" data-tracy-dump='{"text":"***** (string)"}'></pre>
</td></tr>
				<tr><th>HTTPD_VERSION</th><td><pre class="tracy-dump tracy-light"><span class="tracy-dump-string" title="6 characters"><span>'</span>2.4.52<span>'</span></span></pre>
</td></tr>
				<tr><th>TZ</th><td><pre class="tracy-dump tracy-light"
><span class="tracy-dump-string" title="13 characters"><span>'</span>Europe/Prague<span>'</span></span></pre>
</td></tr>
				<tr><th>HOME</th><td><pre class="tracy-dump tracy-light"><span class="tracy-dump-string" title="9 characters"><span>'</span>/home/<USER>'</span></span></pre>
</td></tr>
				<tr><th>https_proxy</th><td><pre class="tracy-dump tracy-light"
><span class="tracy-dump-string" title="24 characters"><span>'</span>http://proxy.dek.cz:3128<span>'</span></span></pre>
</td></tr>
				<tr><th>PHP_SHA256</th><td><pre class="tracy-dump tracy-light"
><span class="tracy-dump-string" title="64 characters"><span>'</span>fbde8247ac200e4de73449d9fefc8b495d323b5be9c10cdb645fb431c91156e3<span>'</span></span></pre>
</td></tr>
				<tr><th>APACHE_PID_FILE</th><td><pre class="tracy-dump tracy-light"
><span class="tracy-dump-string" title="16 characters"><span>'</span>/tmp/apache2.pid<span>'</span></span></pre>
</td></tr>
				<tr><th>PHPIZE_DEPS</th><td><pre class="tracy-dump tracy-light" data-tracy-dump='{"string":"autoconf <i>\\t</i>    <i>\\t</i>    dpkg-dev <i>\\t</i>    <i>\\t</i>    file <i>\\t</i>    <i>\\t</i>    g++ <i>\\t</i>    <i>\\t</i>    gcc <i>\\t</i>    <i>\\t</i>    libc-dev <i>\\t</i>    <i>\\t</i>    make <i>\\t</i>    <i>\\t</i>    pkg-config <i>\\t</i>    <i>\\t</i>    re2c","length":76}'></pre>
</td></tr>
				<tr><th>HTTPD_PATCHES</th><td><pre class="tracy-dump tracy-light"><span class="tracy-dump-string"><span>'</span><span>'</span></span></pre>
</td></tr>
				<tr><th>TERM</th><td><pre class="tracy-dump tracy-light"><span class="tracy-dump-string" title="5 characters"><span>'</span>xterm<span>'</span></span></pre>
</td></tr>
				<tr><th>HOST</th><td><pre class="tracy-dump tracy-light"
><span class="tracy-dump-string" title="24 characters"><span>'</span>zapisy-porady.mik.dek.cz<span>'</span></span></pre>
</td></tr>
				<tr><th>PHP_URL</th><td><pre class="tracy-dump tracy-light"
><span class="tracy-dump-string" title="51 characters"><span>'</span>https://www.php.net/distributions/php-8.0.14.tar.xz<span>'</span></span></pre>
</td></tr>
				<tr><th>HTTPD_SHA256</th><td><pre class="tracy-dump tracy-light"
><span class="tracy-dump-string" title="64 characters"><span>'</span>0127f7dc497e9983e9c51474bed75e45607f2f870a7675a86dc90af6d572f5c9<span>'</span></span></pre>
</td></tr>
				<tr><th>APACHE_RUN_GROUP</th><td><pre class="tracy-dump tracy-light"><span class="tracy-dump-string" title="3 characters"><span>'</span>mik<span>'</span></span></pre>
</td></tr>
				<tr><th>PHP_EXTRA_CONFIGURE_ARGS</th><td><pre class="tracy-dump tracy-light"
><span class="tracy-dump-string" title="26 characters"><span>'</span>--with-apxs2 --disable-cgi<span>'</span></span></pre>
</td></tr>
				<tr><th>SHLVL</th><td><pre class="tracy-dump tracy-light"><span class="tracy-dump-string"><span>'</span>1<span>'</span></span></pre>
</td></tr>
				<tr><th>COMPOSER_VERSION</th><td><pre class="tracy-dump tracy-light"><span class="tracy-dump-string" title="5 characters"><span>'</span>2.2.3<span>'</span></span></pre>
</td></tr>
				<tr><th>PHP_CFLAGS</th><td><pre class="tracy-dump tracy-light"
><span class="tracy-dump-string" title="83 characters"><span>'</span>-fstack-protector-strong -fpic -fpie -O2 -D_LARGEFILE_SOURCE -D_FILE_OFFSET_BITS=64<span>'</span></span></pre>
</td></tr>
				<tr><th>http_proxy</th><td><pre class="tracy-dump tracy-light"
><span class="tracy-dump-string" title="24 characters"><span>'</span>http://proxy.dek.cz:3128<span>'</span></span></pre>
</td></tr>
				<tr><th>APACHE_RUN_USER</th><td><pre class="tracy-dump tracy-light"><span class="tracy-dump-string" title="3 characters"><span>'</span>mik<span>'</span></span></pre>
</td></tr>
				<tr><th>PATH</th><td><pre class="tracy-dump tracy-light"
><span class="tracy-dump-string" title="83 characters"><span>'</span>/usr/local/apache2/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin<span>'</span></span></pre>
</td></tr>
				<tr><th>PHP_ASC_URL</th><td><pre class="tracy-dump tracy-light"
><span class="tracy-dump-string" title="55 characters"><span>'</span>https://www.php.net/distributions/php-8.0.14.tar.xz.asc<span>'</span></span></pre>
</td></tr>
				<tr><th>PHP_CPPFLAGS</th><td><pre class="tracy-dump tracy-light"
><span class="tracy-dump-string" title="83 characters"><span>'</span>-fstack-protector-strong -fpic -fpie -O2 -D_LARGEFILE_SOURCE -D_FILE_OFFSET_BITS=64<span>'</span></span></pre>
</td></tr>
				<tr><th>HTTPD_PREFIX</th><td><pre class="tracy-dump tracy-light"
><span class="tracy-dump-string" title="18 characters"><span>'</span>/usr/local/apache2<span>'</span></span></pre>
</td></tr>
				<tr><th>_</th><td><pre class="tracy-dump tracy-light"
><span class="tracy-dump-string" title="18 characters"><span>'</span>/usr/local/bin/php<span>'</span></span></pre>
</td></tr>
				<tr><th>PHP_SELF</th><td><pre class="tracy-dump tracy-light"
><span class="tracy-dump-string" title="18 characters"><span>'</span>vendor/bin/console<span>'</span></span></pre>
</td></tr>
				<tr><th>SCRIPT_NAME</th><td><pre class="tracy-dump tracy-light"
><span class="tracy-dump-string" title="18 characters"><span>'</span>vendor/bin/console<span>'</span></span></pre>
</td></tr>
				<tr><th>SCRIPT_FILENAME</th><td><pre class="tracy-dump tracy-light"
><span class="tracy-dump-string" title="18 characters"><span>'</span>vendor/bin/console<span>'</span></span></pre>
</td></tr>
				<tr><th>PATH_TRANSLATED</th><td><pre class="tracy-dump tracy-light"
><span class="tracy-dump-string" title="18 characters"><span>'</span>vendor/bin/console<span>'</span></span></pre>
</td></tr>
				<tr><th>DOCUMENT_ROOT</th><td><pre class="tracy-dump tracy-light"><span class="tracy-dump-string"><span>'</span><span>'</span></span></pre>
</td></tr>
				<tr><th>REQUEST_TIME_FLOAT</th><td><pre class="tracy-dump tracy-light"><span class="tracy-dump-number">1645176349.769275</span></pre>
</td></tr>
				<tr><th>REQUEST_TIME</th><td><pre class="tracy-dump tracy-light"><span class="tracy-dump-number">1645176349</span></pre>
</td></tr>
				<tr><th>argv</th><td><pre class="tracy-dump tracy-light" data-tracy-dump='[[0,"vendor/bin/console"]]'></pre>
</td></tr>
				<tr><th>argc</th><td><pre class="tracy-dump tracy-light"><span class="tracy-dump-number">1</span></pre>
</td></tr>
				<tr><th>APP_NAME</th><td><pre class="tracy-dump tracy-light"><span class="tracy-dump-string" title="7 characters"><span>'</span>Laravel<span>'</span></span></pre>
</td></tr>
				<tr><th>APP_ENV</th><td><pre class="tracy-dump tracy-light"><span class="tracy-dump-string" title="5 characters"><span>'</span>local<span>'</span></span></pre>
</td></tr>
				<tr><th>APP_KEY</th><td><pre class="tracy-dump tracy-light"
><span class="tracy-dump-string" title="51 characters"><span>'</span>base64:UtsxnZZlc++dy34C5pMIlyW8uMYAVIHrx6fB7IhB8Pc=<span>'</span></span></pre>
</td></tr>
				<tr><th>APP_DEBUG</th><td><pre class="tracy-dump tracy-light"><span class="tracy-dump-string" title="4 characters"><span>'</span>true<span>'</span></span></pre>
</td></tr>
				<tr><th>APP_URL</th><td><pre class="tracy-dump tracy-light"
><span class="tracy-dump-string" title="16 characters"><span>'</span>http://localhost<span>'</span></span></pre>
</td></tr>
				<tr><th>LOG_CHANNEL</th><td><pre class="tracy-dump tracy-light"><span class="tracy-dump-string" title="5 characters"><span>'</span>stack<span>'</span></span></pre>
</td></tr>
				<tr><th>LOG_DEPRECATIONS_CHANNEL</th><td><pre class="tracy-dump tracy-light"><span class="tracy-dump-string" title="4 characters"><span>'</span>null<span>'</span></span></pre>
</td></tr>
				<tr><th>LOG_LEVEL</th><td><pre class="tracy-dump tracy-light"><span class="tracy-dump-string" title="5 characters"><span>'</span>debug<span>'</span></span></pre>
</td></tr>
				<tr><th>DB_CONNECTION</th><td><pre class="tracy-dump tracy-light"><span class="tracy-dump-string" title="5 characters"><span>'</span>mysql<span>'</span></span></pre>
</td></tr>
				<tr><th>DB_HOST</th><td><pre class="tracy-dump tracy-light"
><span class="tracy-dump-string" title="18 characters"><span>'</span>mariadbtest.dek.cz<span>'</span></span></pre>
</td></tr>
				<tr><th>DB_PORT</th><td><pre class="tracy-dump tracy-light"><span class="tracy-dump-string" title="4 characters"><span>'</span>3306<span>'</span></span></pre>
</td></tr>
				<tr><th>DB_DATABASE</th><td><pre class="tracy-dump tracy-light"
><span class="tracy-dump-string" title="12 characters"><span>'</span>zapisyporady<span>'</span></span></pre>
</td></tr>
				<tr><th>DB_USERNAME</th><td><pre class="tracy-dump tracy-light"
><span class="tracy-dump-string" title="12 characters"><span>'</span>zapisyporady<span>'</span></span></pre>
</td></tr>
				<tr><th>DB_PASSWORD</th><td><pre class="tracy-dump tracy-light"
><span class="tracy-dump-string" title="25 characters"><span>'</span>rcphVbudHaEQfBjq4NWv7tnwC<span>'</span></span></pre>
</td></tr>
				<tr><th>BROADCAST_DRIVER</th><td><pre class="tracy-dump tracy-light"><span class="tracy-dump-string" title="3 characters"><span>'</span>log<span>'</span></span></pre>
</td></tr>
				<tr><th>CACHE_DRIVER</th><td><pre class="tracy-dump tracy-light"><span class="tracy-dump-string" title="4 characters"><span>'</span>file<span>'</span></span></pre>
</td></tr>
				<tr><th>FILESYSTEM_DISK</th><td><pre class="tracy-dump tracy-light"><span class="tracy-dump-string" title="5 characters"><span>'</span>local<span>'</span></span></pre>
</td></tr>
				<tr><th>QUEUE_CONNECTION</th><td><pre class="tracy-dump tracy-light"><span class="tracy-dump-string" title="4 characters"><span>'</span>sync<span>'</span></span></pre>
</td></tr>
				<tr><th>SESSION_DRIVER</th><td><pre class="tracy-dump tracy-light"><span class="tracy-dump-string" title="4 characters"><span>'</span>file<span>'</span></span></pre>
</td></tr>
				<tr><th>SESSION_LIFETIME</th><td><pre class="tracy-dump tracy-light"><span class="tracy-dump-string" title="3 characters"><span>'</span>120<span>'</span></span></pre>
</td></tr>
				<tr><th>MEMCACHED_HOST</th><td><pre class="tracy-dump tracy-light"><span class="tracy-dump-string" title="9 characters"><span>'</span>127.0.0.1<span>'</span></span></pre>
</td></tr>
				<tr><th>REDIS_HOST</th><td><pre class="tracy-dump tracy-light"><span class="tracy-dump-string" title="9 characters"><span>'</span>127.0.0.1<span>'</span></span></pre>
</td></tr>
				<tr><th>REDIS_PASSWORD</th><td><pre class="tracy-dump tracy-light"><span class="tracy-dump-string" title="4 characters"><span>'</span>null<span>'</span></span></pre>
</td></tr>
				<tr><th>REDIS_PORT</th><td><pre class="tracy-dump tracy-light"><span class="tracy-dump-string" title="4 characters"><span>'</span>6379<span>'</span></span></pre>
</td></tr>
				<tr><th>MAIL_MAILER</th><td><pre class="tracy-dump tracy-light"><span class="tracy-dump-string" title="4 characters"><span>'</span>smtp<span>'</span></span></pre>
</td></tr>
				<tr><th>MAIL_HOST</th><td><pre class="tracy-dump tracy-light"><span class="tracy-dump-string" title="7 characters"><span>'</span>mailhog<span>'</span></span></pre>
</td></tr>
				<tr><th>MAIL_PORT</th><td><pre class="tracy-dump tracy-light"><span class="tracy-dump-string" title="4 characters"><span>'</span>1025<span>'</span></span></pre>
</td></tr>
				<tr><th>MAIL_USERNAME</th><td><pre class="tracy-dump tracy-light"><span class="tracy-dump-string" title="4 characters"><span>'</span>null<span>'</span></span></pre>
</td></tr>
				<tr><th>MAIL_PASSWORD</th><td><pre class="tracy-dump tracy-light"><span class="tracy-dump-string" title="4 characters"><span>'</span>null<span>'</span></span></pre>
</td></tr>
				<tr><th>MAIL_ENCRYPTION</th><td><pre class="tracy-dump tracy-light"><span class="tracy-dump-string" title="4 characters"><span>'</span>null<span>'</span></span></pre>
</td></tr>
				<tr><th>MAIL_FROM_ADDRESS</th><td><pre class="tracy-dump tracy-light"><span class="tracy-dump-string" title="4 characters"><span>'</span>null<span>'</span></span></pre>
</td></tr>
				<tr><th>MAIL_FROM_NAME</th><td><pre class="tracy-dump tracy-light"><span class="tracy-dump-string" title="7 characters"><span>'</span>Laravel<span>'</span></span></pre>
</td></tr>
				<tr><th>AWS_ACCESS_KEY_ID</th><td><pre class="tracy-dump tracy-light"><span class="tracy-dump-string"><span>'</span><span>'</span></span></pre>
</td></tr>
				<tr><th>AWS_SECRET_ACCESS_KEY</th><td><pre class="tracy-dump tracy-light"><span class="tracy-dump-string"><span>'</span><span>'</span></span></pre>
</td></tr>
				<tr><th>AWS_DEFAULT_REGION</th><td><pre class="tracy-dump tracy-light"><span class="tracy-dump-string" title="9 characters"><span>'</span>us-east-1<span>'</span></span></pre>
</td></tr>
				<tr><th>AWS_BUCKET</th><td><pre class="tracy-dump tracy-light"><span class="tracy-dump-string"><span>'</span><span>'</span></span></pre>
</td></tr>
				<tr><th>AWS_USE_PATH_STYLE_ENDPOINT</th><td><pre class="tracy-dump tracy-light"><span class="tracy-dump-string" title="5 characters"><span>'</span>false<span>'</span></span></pre>
</td></tr>
				<tr><th>PUSHER_APP_ID</th><td><pre class="tracy-dump tracy-light"><span class="tracy-dump-string"><span>'</span><span>'</span></span></pre>
</td></tr>
				<tr><th>PUSHER_APP_KEY</th><td><pre class="tracy-dump tracy-light"><span class="tracy-dump-string"><span>'</span><span>'</span></span></pre>
</td></tr>
				<tr><th>PUSHER_APP_SECRET</th><td><pre class="tracy-dump tracy-light"><span class="tracy-dump-string"><span>'</span><span>'</span></span></pre>
</td></tr>
				<tr><th>PUSHER_APP_CLUSTER</th><td><pre class="tracy-dump tracy-light"><span class="tracy-dump-string" title="3 characters"><span>'</span>mt1<span>'</span></span></pre>
</td></tr>
				<tr><th>MIX_PUSHER_APP_KEY</th><td><pre class="tracy-dump tracy-light"><span class="tracy-dump-string"><span>'</span><span>'</span></span></pre>
</td></tr>
				<tr><th>MIX_PUSHER_APP_CLUSTER</th><td><pre class="tracy-dump tracy-light"><span class="tracy-dump-string" title="3 characters"><span>'</span>mt1<span>'</span></span></pre>
</td></tr>
				</table>
			</div>





			<div class="tracy-tab-panel pane">
				<table class="tracy-sortable">
					<tr><th>MYSQLI_REFRESH_REPLICA</th><td><pre class="tracy-dump tracy-light"><span class="tracy-dump-number">64</span></pre>
</td></tr>
					<tr><th>ARTISAN_BINARY</th><td><pre class="tracy-dump tracy-light"><span class="tracy-dump-string" title="7 characters"><span>'</span>artisan<span>'</span></span></pre>
</td></tr>
				</table>
			</div>


			<div class="tracy-tab-panel tabs-panel">
				<pre class="tracy-dump tracy-light">phpinfo()


 _______________________________________________________________________


Configuration

bcmath

BCMath support =&gt; enabled

Directive =&gt; Local Value =&gt; Master Value
bcmath.scale =&gt; 0 =&gt; 0

bz2

BZip2 Support =&gt; Enabled
Stream Wrapper support =&gt; compress.bzip2://
Stream Filter support =&gt; bzip2.decompress, bzip2.compress
BZip2 Version =&gt; 1.0.6, 6-Sept-2010

calendar

Calendar support =&gt; enabled

Core

PHP Version =&gt; 8.0.14

Directive =&gt; Local Value =&gt; Master Value
allow_url_fopen =&gt; On =&gt; On
allow_url_include =&gt; Off =&gt; Off
arg_separator.input =&gt; &amp; =&gt; &amp;
arg_separator.output =&gt; &amp; =&gt; &amp;
auto_append_file =&gt; no value =&gt; no value
auto_globals_jit =&gt; On =&gt; On
auto_prepend_file =&gt; no value =&gt; no value
browscap =&gt; no value =&gt; no value
default_charset =&gt; UTF-8 =&gt; UTF-8
default_mimetype =&gt; text/html =&gt; text/html
disable_classes =&gt; no value =&gt; no value
disable_functions =&gt; no value =&gt; no value
display_errors =&gt; STDOUT =&gt; STDOUT
display_startup_errors =&gt; On =&gt; On
doc_root =&gt; no value =&gt; no value
docref_ext =&gt; no value =&gt; no value
docref_root =&gt; no value =&gt; no value
enable_dl =&gt; On =&gt; On
enable_post_data_reading =&gt; On =&gt; On
error_append_string =&gt; no value =&gt; no value
error_log =&gt; no value =&gt; no value
error_prepend_string =&gt; no value =&gt; no value
error_reporting =&gt; 32767 =&gt; no value
expose_php =&gt; Off =&gt; Off
extension_dir =&gt; /usr/local/lib/php/extensions/no-debug-zts-20200930 =&gt; /usr/local/lib/php/extensions/no-debug-zts-20200930
file_uploads =&gt; On =&gt; On
hard_timeout =&gt; 2 =&gt; 2
highlight.comment =&gt; &lt;font style=&quot;color: #998; font-style: italic&quot;&gt;#998; font-style: italic&lt;/font&gt; =&gt; &lt;font style=&quot;color: #FF8000&quot;&gt;#FF8000&lt;/font&gt;
highlight.default =&gt; &lt;font style=&quot;color: #000&quot;&gt;#000&lt;/font&gt; =&gt; &lt;font style=&quot;color: #0000BB&quot;&gt;#0000BB&lt;/font&gt;
highlight.html =&gt; &lt;font style=&quot;color: #06B&quot;&gt;#06B&lt;/font&gt; =&gt; &lt;font style=&quot;color: #000000&quot;&gt;#000000&lt;/font&gt;
highlight.keyword =&gt; &lt;font style=&quot;color: #D24; font-weight: bold&quot;&gt;#D24; font-weight: bold&lt;/font&gt; =&gt; &lt;font style=&quot;color: #007700&quot;&gt;#007700&lt;/font&gt;
highlight.string =&gt; &lt;font style=&quot;color: #080&quot;&gt;#080&lt;/font&gt; =&gt; &lt;font style=&quot;color: #DD0000&quot;&gt;#DD0000&lt;/font&gt;
html_errors =&gt; Off =&gt; Off
ignore_repeated_errors =&gt; Off =&gt; Off
ignore_repeated_source =&gt; Off =&gt; Off
ignore_user_abort =&gt; Off =&gt; Off
implicit_flush =&gt; On =&gt; On
include_path =&gt; .:/usr/local/lib/php =&gt; .:/usr/local/lib/php
input_encoding =&gt; no value =&gt; no value
internal_encoding =&gt; no value =&gt; no value
log_errors =&gt; Off =&gt; On
log_errors_max_len =&gt; 1024 =&gt; 1024
mail.add_x_header =&gt; Off =&gt; Off
mail.force_extra_parameters =&gt; no value =&gt; no value
mail.log =&gt; no value =&gt; no value
max_execution_time =&gt; 0 =&gt; 0
max_file_uploads =&gt; 20 =&gt; 20
max_input_nesting_level =&gt; 64 =&gt; 64
max_input_time =&gt; -1 =&gt; -1
max_input_vars =&gt; 1000 =&gt; 1000
memory_limit =&gt; 128M =&gt; 128M
open_basedir =&gt; no value =&gt; no value
output_buffering =&gt; 0 =&gt; 0
output_encoding =&gt; no value =&gt; no value
output_handler =&gt; no value =&gt; no value
post_max_size =&gt; 8M =&gt; 8M
precision =&gt; 14 =&gt; 14
realpath_cache_size =&gt; 4096K =&gt; 4096K
realpath_cache_ttl =&gt; 120 =&gt; 120
register_argc_argv =&gt; On =&gt; On
report_memleaks =&gt; On =&gt; On
report_zend_debug =&gt; Off =&gt; Off
request_order =&gt; no value =&gt; no value
sendmail_from =&gt; no value =&gt; no value
sendmail_path =&gt; /usr/bin/msmtp -t =&gt; /usr/bin/msmtp -t
serialize_precision =&gt; -1 =&gt; -1
short_open_tag =&gt; On =&gt; On
SMTP =&gt; localhost =&gt; localhost
smtp_port =&gt; 25 =&gt; 25
sys_temp_dir =&gt; no value =&gt; no value
syslog.facility =&gt; LOG_USER =&gt; LOG_USER
syslog.filter =&gt; no-ctrl =&gt; no-ctrl
syslog.ident =&gt; php =&gt; php
unserialize_callback_func =&gt; no value =&gt; no value
upload_max_filesize =&gt; 2M =&gt; 2M
upload_tmp_dir =&gt; no value =&gt; no value
user_dir =&gt; no value =&gt; no value
user_ini.cache_ttl =&gt; 300 =&gt; 300
user_ini.filename =&gt; .user.ini =&gt; .user.ini
variables_order =&gt; EGPCS =&gt; EGPCS
xmlrpc_error_number =&gt; 0 =&gt; 0
xmlrpc_errors =&gt; Off =&gt; Off
zend.assertions =&gt; 1 =&gt; 1
zend.detect_unicode =&gt; On =&gt; On
zend.enable_gc =&gt; On =&gt; On
zend.exception_ignore_args =&gt; Off =&gt; Off
zend.exception_string_param_max_len =&gt; 15 =&gt; 15
zend.multibyte =&gt; Off =&gt; Off
zend.script_encoding =&gt; no value =&gt; no value
zend.signal_check =&gt; Off =&gt; Off

ctype

ctype functions =&gt; enabled

curl

cURL support =&gt; enabled
cURL Information =&gt; 7.64.0
Age =&gt; 4
Features
AsynchDNS =&gt; Yes
CharConv =&gt; No
Debug =&gt; No
GSS-Negotiate =&gt; No
IDN =&gt; Yes
IPv6 =&gt; Yes
krb4 =&gt; No
Largefile =&gt; Yes
libz =&gt; Yes
NTLM =&gt; Yes
NTLMWB =&gt; Yes
SPNEGO =&gt; Yes
SSL =&gt; Yes
SSPI =&gt; No
TLS-SRP =&gt; Yes
HTTP2 =&gt; Yes
GSSAPI =&gt; Yes
KERBEROS5 =&gt; Yes
UNIX_SOCKETS =&gt; Yes
PSL =&gt; Yes
HTTPS_PROXY =&gt; Yes
MULTI_SSL =&gt; No
BROTLI =&gt; No
Protocols =&gt; dict, file, ftp, ftps, gopher, http, https, imap, imaps, ldap, ldaps, pop3, pop3s, rtmp, rtsp, scp, sftp, smb, smbs, smtp, smtps, telnet, tftp
Host =&gt; x86_64-pc-linux-gnu
SSL Version =&gt; OpenSSL/1.1.1d
ZLib Version =&gt; 1.2.11
libSSH Version =&gt; libssh2/1.8.0

Directive =&gt; Local Value =&gt; Master Value
curl.cainfo =&gt; no value =&gt; no value

date

date/time support =&gt; enabled
timelib version =&gt; 2020.03
&quot;Olson&quot; Timezone Database Version =&gt; 2021.5
Timezone Database =&gt; internal
Default timezone =&gt; UTC

Directive =&gt; Local Value =&gt; Master Value
date.default_latitude =&gt; 31.7667 =&gt; 31.7667
date.default_longitude =&gt; 35.2333 =&gt; 35.2333
date.sunrise_zenith =&gt; 90.833333 =&gt; 90.833333
date.sunset_zenith =&gt; 90.833333 =&gt; 90.833333
date.timezone =&gt; Europe/Prague =&gt; Europe/Prague

dom

DOM/XML =&gt; enabled
DOM/XML API Version =&gt; 20031129
libxml Version =&gt; 2.9.4
HTML Support =&gt; enabled
XPath Support =&gt; enabled
XPointer Support =&gt; enabled
Schema Support =&gt; enabled
RelaxNG Support =&gt; enabled

exif

EXIF Support =&gt; enabled
Supported EXIF Version =&gt; 0220
Supported filetypes =&gt; JPEG, TIFF
Multibyte decoding support using mbstring =&gt; enabled
Extended EXIF tag formats =&gt; Canon, Casio, Fujifilm, Nikon, Olympus, Samsung, Panasonic, DJI, Sony, Pentax, Minolta, Sigma, Foveon, Kyocera, Ricoh, AGFA, Epson

Directive =&gt; Local Value =&gt; Master Value
exif.decode_jis_intel =&gt; JIS =&gt; JIS
exif.decode_jis_motorola =&gt; JIS =&gt; JIS
exif.decode_unicode_intel =&gt; UCS-2LE =&gt; UCS-2LE
exif.decode_unicode_motorola =&gt; UCS-2BE =&gt; UCS-2BE
exif.encode_jis =&gt; no value =&gt; no value
exif.encode_unicode =&gt; ISO-8859-15 =&gt; ISO-8859-15

fileinfo

fileinfo support =&gt; enabled
libmagic =&gt; 539

filter

Input Validation and Filtering =&gt; enabled

Directive =&gt; Local Value =&gt; Master Value
filter.default =&gt; unsafe_raw =&gt; unsafe_raw
filter.default_flags =&gt; no value =&gt; no value

ftp

FTP support =&gt; enabled
FTPS support =&gt; enabled

gd

GD Support =&gt; enabled
GD Version =&gt; bundled (2.1.0 compatible)
FreeType Support =&gt; enabled
FreeType Linkage =&gt; with freetype
FreeType Version =&gt; 2.9.1
GIF Read Support =&gt; enabled
GIF Create Support =&gt; enabled
JPEG Support =&gt; enabled
libJPEG Version =&gt; 6b
PNG Support =&gt; enabled
libPNG Version =&gt; 1.6.36
WBMP Support =&gt; enabled
XPM Support =&gt; enabled
libXpm Version =&gt; 30411
XBM Support =&gt; enabled
WebP Support =&gt; enabled
BMP Support =&gt; enabled
TGA Read Support =&gt; enabled

Directive =&gt; Local Value =&gt; Master Value
gd.jpeg_ignore_warning =&gt; 1 =&gt; 1

gettext

GetText Support =&gt; enabled

hash

hash support =&gt; enabled
Hashing Engines =&gt; md2 md4 md5 sha1 sha224 sha256 sha384 sha512/224 sha512/256 sha512 sha3-224 sha3-256 sha3-384 sha3-512 ripemd128 ripemd160 ripemd256 ripemd320 whirlpool tiger128,3 tiger160,3 tiger192,3 tiger128,4 tiger160,4 tiger192,4 snefru snefru256 gost gost-crypto adler32 crc32 crc32b crc32c fnv132 fnv1a32 fnv164 fnv1a64 joaat haval128,3 haval160,3 haval192,3 haval224,3 haval256,3 haval128,4 haval160,4 haval192,4 haval224,4 haval256,4 haval128,5 haval160,5 haval192,5 haval224,5 haval256,5 

MHASH support =&gt; Enabled
MHASH API Version =&gt; Emulated Support

iconv

iconv support =&gt; enabled
iconv implementation =&gt; glibc
iconv library version =&gt; 2.28

Directive =&gt; Local Value =&gt; Master Value
iconv.input_encoding =&gt; no value =&gt; no value
iconv.internal_encoding =&gt; no value =&gt; no value
iconv.output_encoding =&gt; no value =&gt; no value

imap

IMAP c-Client Version =&gt; 2007f
SSL Support =&gt; enabled
Kerberos Support =&gt; enabled

Directive =&gt; Local Value =&gt; Master Value
imap.enable_insecure_rsh =&gt; Off =&gt; Off

intl

Internationalization support =&gt; enabled
ICU version =&gt; 63.1
ICU Data version =&gt; 63.1
ICU Unicode version =&gt; 11.0

Directive =&gt; Local Value =&gt; Master Value
intl.default_locale =&gt; no value =&gt; no value
intl.error_level =&gt; 0 =&gt; 0
intl.use_exceptions =&gt; Off =&gt; Off

json

json support =&gt; enabled

ldap

LDAP Support =&gt; enabled
Total Links =&gt; 0/unlimited
API Version =&gt; 3001
Vendor Name =&gt; OpenLDAP
Vendor Version =&gt; 20447

Directive =&gt; Local Value =&gt; Master Value
ldap.max_links =&gt; Unlimited =&gt; Unlimited

libxml

libXML support =&gt; active
libXML Compiled Version =&gt; 2.9.4
libXML Loaded Version =&gt; 20904
libXML streams =&gt; enabled

mbstring

Multibyte Support =&gt; enabled
Multibyte string engine =&gt; libmbfl
HTTP input encoding translation =&gt; disabled
libmbfl version =&gt; 1.3.2

mbstring extension makes use of &quot;streamable kanji code filter and converter&quot;, which is distributed under the GNU Lesser General Public License version 2.1.

Multibyte (japanese) regex support =&gt; enabled
Multibyte regex (oniguruma) version =&gt; 6.9.1

Directive =&gt; Local Value =&gt; Master Value
mbstring.detect_order =&gt; no value =&gt; no value
mbstring.encoding_translation =&gt; Off =&gt; Off
mbstring.http_input =&gt; no value =&gt; no value
mbstring.http_output =&gt; no value =&gt; no value
mbstring.http_output_conv_mimetypes =&gt; ^(text/|application/xhtml\+xml) =&gt; ^(text/|application/xhtml\+xml)
mbstring.internal_encoding =&gt; no value =&gt; no value
mbstring.language =&gt; neutral =&gt; neutral
mbstring.regex_retry_limit =&gt; 1000000 =&gt; 1000000
mbstring.regex_stack_limit =&gt; 100000 =&gt; 100000
mbstring.strict_detection =&gt; Off =&gt; Off
mbstring.substitute_character =&gt; no value =&gt; no value

mysqli

MysqlI Support =&gt; enabled
Client API library version =&gt; mysqlnd 8.0.14
Active Persistent Links =&gt; 0
Inactive Persistent Links =&gt; 0
Active Links =&gt; 0

Directive =&gt; Local Value =&gt; Master Value
mysqli.allow_local_infile =&gt; Off =&gt; Off
mysqli.allow_persistent =&gt; On =&gt; On
mysqli.default_host =&gt; no value =&gt; no value
mysqli.default_port =&gt; 3306 =&gt; 3306
mysqli.default_pw =&gt; no value =&gt; no value
mysqli.default_socket =&gt; no value =&gt; no value
mysqli.default_user =&gt; no value =&gt; no value
mysqli.max_links =&gt; Unlimited =&gt; Unlimited
mysqli.max_persistent =&gt; Unlimited =&gt; Unlimited
mysqli.reconnect =&gt; Off =&gt; Off
mysqli.rollback_on_cached_plink =&gt; Off =&gt; Off

mysqlnd

mysqlnd =&gt; enabled
Version =&gt; mysqlnd 8.0.14
Compression =&gt; supported
core SSL =&gt; supported
extended SSL =&gt; supported
Command buffer size =&gt; 4096
Read buffer size =&gt; 32768
Read timeout =&gt; 86400
Collecting statistics =&gt; Yes
Collecting memory statistics =&gt; No
Tracing =&gt; n/a
Loaded plugins =&gt; mysqlnd,debug_trace,auth_plugin_mysql_native_password,auth_plugin_mysql_clear_password,auth_plugin_caching_sha2_password,auth_plugin_sha256_password
API Extensions =&gt; pdo_mysql,mysqli

odbc

ODBC Support =&gt; enabled
Active Persistent Links =&gt; 0
Active Links =&gt; 0
ODBC library =&gt; unixODBC
ODBCVER =&gt; 0x0380
ODBC_CFLAGS =&gt; -I/usr/include
ODBC_LFLAGS =&gt; -L/usr/lib/x86_64-linux-gnu
ODBC_LIBS =&gt; -lodbc

Directive =&gt; Local Value =&gt; Master Value
odbc.allow_persistent =&gt; On =&gt; On
odbc.check_persistent =&gt; On =&gt; On
odbc.default_cursortype =&gt; Static cursor =&gt; Static cursor
odbc.default_db =&gt; no value =&gt; no value
odbc.default_pw =&gt; no value =&gt; no value
odbc.default_user =&gt; no value =&gt; no value
odbc.defaultbinmode =&gt; return as is =&gt; return as is
odbc.defaultlrl =&gt; return up to 4096 bytes =&gt; return up to 4096 bytes
odbc.max_links =&gt; Unlimited =&gt; Unlimited
odbc.max_persistent =&gt; Unlimited =&gt; Unlimited

openssl

OpenSSL support =&gt; enabled
OpenSSL Library Version =&gt; OpenSSL 1.1.1d  10 Sep 2019
OpenSSL Header Version =&gt; OpenSSL 1.1.1d  10 Sep 2019
Openssl default config =&gt; /usr/lib/ssl/openssl.cnf

Directive =&gt; Local Value =&gt; Master Value
openssl.cafile =&gt; no value =&gt; no value
openssl.capath =&gt; no value =&gt; no value

pcntl

pcntl support =&gt; enabled

pcre

PCRE (Perl Compatible Regular Expressions) Support =&gt; enabled
PCRE Library Version =&gt; 10.35 2020-05-09
PCRE Unicode Version =&gt; 13.0.0
PCRE JIT Support =&gt; enabled
PCRE JIT Target =&gt; x86 64bit (little endian + unaligned)

Directive =&gt; Local Value =&gt; Master Value
pcre.backtrack_limit =&gt; 1000000 =&gt; 1000000
pcre.jit =&gt; 1 =&gt; 1
pcre.recursion_limit =&gt; 100000 =&gt; 100000

PDO

PDO support =&gt; enabled
PDO drivers =&gt; odbc, pgsql, sqlite, mysql, sqlsrv

pdo_mysql

PDO Driver for MySQL =&gt; enabled
Client API version =&gt; mysqlnd 8.0.14

Directive =&gt; Local Value =&gt; Master Value
pdo_mysql.default_socket =&gt; /tmp/mysql.sock =&gt; /tmp/mysql.sock

PDO_ODBC

PDO Driver for ODBC (unixODBC) =&gt; enabled
ODBC Connection Pooling =&gt; Enabled, strict matching

pdo_pgsql

PDO Driver for PostgreSQL =&gt; enabled
PostgreSQL(libpq) Version =&gt; 11.14

pdo_sqlite

PDO Driver for SQLite 3.x =&gt; enabled
SQLite Library =&gt; 3.27.2

pdo_sqlsrv

pdo_sqlsrv support =&gt; enabled
ExtensionVer =&gt; 5.9.0

Directive =&gt; Local Value =&gt; Master Value
pdo_sqlsrv.client_buffer_max_kb_size =&gt; 10240 =&gt; 10240
pdo_sqlsrv.log_severity =&gt; 0 =&gt; 0
pdo_sqlsrv.report_additional_errors =&gt; 1 =&gt; 1
pdo_sqlsrv.set_locale_info =&gt; 2 =&gt; 2

pgsql

PostgreSQL Support =&gt; enabled
PostgreSQL (libpq) Version =&gt; 11.14
Multibyte character support =&gt; enabled
Active Persistent Links =&gt; 0
Active Links =&gt; 0

Directive =&gt; Local Value =&gt; Master Value
pgsql.allow_persistent =&gt; On =&gt; On
pgsql.auto_reset_persistent =&gt; Off =&gt; Off
pgsql.ignore_notice =&gt; Off =&gt; Off
pgsql.log_notice =&gt; Off =&gt; Off
pgsql.max_links =&gt; Unlimited =&gt; Unlimited
pgsql.max_persistent =&gt; Unlimited =&gt; Unlimited

Phar

Phar: PHP Archive support =&gt; enabled
Phar API version =&gt; 1.1.1
Phar-based phar archives =&gt; enabled
Tar-based phar archives =&gt; enabled
ZIP-based phar archives =&gt; enabled
gzip compression =&gt; enabled
bzip2 compression =&gt; enabled
Native OpenSSL support =&gt; enabled


Phar based on pear/PHP_Archive, original concept by Davey Shafik.
Phar fully realized by Gregory Beaver and Marcus Boerger.
Portions of tar implementation Copyright (c) 2003-2009 Tim Kientzle.
Directive =&gt; Local Value =&gt; Master Value
phar.cache_list =&gt; no value =&gt; no value
phar.readonly =&gt; On =&gt; On
phar.require_hash =&gt; On =&gt; On

posix

POSIX support =&gt; enabled

readline

Readline Support =&gt; enabled
Readline library =&gt; EditLine wrapper

Directive =&gt; Local Value =&gt; Master Value
cli.pager =&gt; no value =&gt; no value
cli.prompt =&gt; \b \&gt;  =&gt; \b \&gt; 

redis

Redis Support =&gt; enabled
Redis Version =&gt; 5.3.5
Redis Sentinel Version =&gt; 0.1
Available serializers =&gt; php, json

Directive =&gt; Local Value =&gt; Master Value
redis.arrays.algorithm =&gt; no value =&gt; no value
redis.arrays.auth =&gt; no value =&gt; no value
redis.arrays.autorehash =&gt; 0 =&gt; 0
redis.arrays.connecttimeout =&gt; 0 =&gt; 0
redis.arrays.consistent =&gt; 0 =&gt; 0
redis.arrays.distributor =&gt; no value =&gt; no value
redis.arrays.functions =&gt; no value =&gt; no value
redis.arrays.hosts =&gt; no value =&gt; no value
redis.arrays.index =&gt; 0 =&gt; 0
redis.arrays.lazyconnect =&gt; 0 =&gt; 0
redis.arrays.names =&gt; no value =&gt; no value
redis.arrays.pconnect =&gt; 0 =&gt; 0
redis.arrays.previous =&gt; no value =&gt; no value
redis.arrays.readtimeout =&gt; 0 =&gt; 0
redis.arrays.retryinterval =&gt; 0 =&gt; 0
redis.clusters.auth =&gt; no value =&gt; no value
redis.clusters.cache_slots =&gt; 0 =&gt; 0
redis.clusters.persistent =&gt; 0 =&gt; 0
redis.clusters.read_timeout =&gt; 0 =&gt; 0
redis.clusters.seeds =&gt; no value =&gt; no value
redis.clusters.timeout =&gt; 0 =&gt; 0
redis.pconnect.connection_limit =&gt; 0 =&gt; 0
redis.pconnect.echo_check_liveness =&gt; 1 =&gt; 1
redis.pconnect.pool_detect_dirty =&gt; 0 =&gt; 0
redis.pconnect.pool_pattern =&gt; no value =&gt; no value
redis.pconnect.pool_poll_timeout =&gt; 0 =&gt; 0
redis.pconnect.pooling_enabled =&gt; 1 =&gt; 1
redis.session.lock_expire =&gt; 0 =&gt; 0
redis.session.lock_retries =&gt; 10 =&gt; 10
redis.session.lock_wait_time =&gt; 2000 =&gt; 2000
redis.session.locking_enabled =&gt; 0 =&gt; 0

Reflection

Reflection =&gt; enabled

session

Session Support =&gt; enabled
Registered save handlers =&gt; files user redis rediscluster 
Registered serializer handlers =&gt; php_serialize php php_binary 

Directive =&gt; Local Value =&gt; Master Value
session.auto_start =&gt; Off =&gt; Off
session.cache_expire =&gt; 180 =&gt; 180
session.cache_limiter =&gt; nocache =&gt; nocache
session.cookie_domain =&gt; no value =&gt; no value
session.cookie_httponly =&gt; 0 =&gt; 0
session.cookie_lifetime =&gt; 0 =&gt; 0
session.cookie_path =&gt; / =&gt; /
session.cookie_samesite =&gt; no value =&gt; no value
session.cookie_secure =&gt; 0 =&gt; 0
session.gc_divisor =&gt; 100 =&gt; 100
session.gc_maxlifetime =&gt; 1440 =&gt; 1440
session.gc_probability =&gt; 1 =&gt; 1
session.lazy_write =&gt; On =&gt; On
session.name =&gt; PHPSESSID =&gt; PHPSESSID
session.referer_check =&gt; no value =&gt; no value
session.save_handler =&gt; files =&gt; files
session.save_path =&gt; no value =&gt; no value
session.serialize_handler =&gt; php =&gt; php
session.sid_bits_per_character =&gt; 4 =&gt; 4
session.sid_length =&gt; 32 =&gt; 32
session.upload_progress.cleanup =&gt; On =&gt; On
session.upload_progress.enabled =&gt; On =&gt; On
session.upload_progress.freq =&gt; 1% =&gt; 1%
session.upload_progress.min_freq =&gt; 1 =&gt; 1
session.upload_progress.name =&gt; PHP_SESSION_UPLOAD_PROGRESS =&gt; PHP_SESSION_UPLOAD_PROGRESS
session.upload_progress.prefix =&gt; upload_progress_ =&gt; upload_progress_
session.use_cookies =&gt; 1 =&gt; 1
session.use_only_cookies =&gt; 1 =&gt; 1
session.use_strict_mode =&gt; 0 =&gt; 0
session.use_trans_sid =&gt; 0 =&gt; 0

shmop

shmop support =&gt; enabled

SimpleXML

SimpleXML support =&gt; enabled
Schema support =&gt; enabled

soap

Soap Client =&gt; enabled
Soap Server =&gt; enabled

Directive =&gt; Local Value =&gt; Master Value
soap.wsdl_cache =&gt; 1 =&gt; 1
soap.wsdl_cache_dir =&gt; /tmp =&gt; /tmp
soap.wsdl_cache_enabled =&gt; On =&gt; On
soap.wsdl_cache_limit =&gt; 5 =&gt; 5
soap.wsdl_cache_ttl =&gt; 86400 =&gt; 86400

sockets

Sockets Support =&gt; enabled

SPL

SPL support =&gt; enabled
Interfaces =&gt; OuterIterator, RecursiveIterator, SeekableIterator, SplObserver, SplSubject
Classes =&gt; AppendIterator, ArrayIterator, ArrayObject, BadFunctionCallException, BadMethodCallException, CachingIterator, CallbackFilterIterator, DirectoryIterator, DomainException, EmptyIterator, FilesystemIterator, FilterIterator, GlobIterator, InfiniteIterator, InvalidArgumentException, IteratorIterator, LengthException, LimitIterator, LogicException, MultipleIterator, NoRewindIterator, OutOfBoundsException, OutOfRangeException, OverflowException, ParentIterator, RangeException, RecursiveArrayIterator, RecursiveCachingIterator, RecursiveCallbackFilterIterator, RecursiveDirectoryIterator, RecursiveFilterIterator, RecursiveIteratorIterator, RecursiveRegexIterator, RecursiveTreeIterator, RegexIterator, RuntimeException, SplDoublyLinkedList, SplFileInfo, SplFileObject, SplFixedArray, SplHeap, SplMinHeap, SplMaxHeap, SplObjectStorage, SplPriorityQueue, SplQueue, SplStack, SplTempFileObject, UnderflowException, UnexpectedValueException

sqlite3

SQLite3 support =&gt; enabled
SQLite Library =&gt; 3.27.2

Directive =&gt; Local Value =&gt; Master Value
sqlite3.defensive =&gt; On =&gt; On
sqlite3.extension_dir =&gt; no value =&gt; no value

sqlsrv

sqlsrv support =&gt; enabled
ExtensionVer =&gt; 5.9.0

Directive =&gt; Local Value =&gt; Master Value
sqlsrv.ClientBufferMaxKBSize =&gt; 10240 =&gt; 10240
sqlsrv.LogSeverity =&gt; 0 =&gt; 0
sqlsrv.LogSubsystems =&gt; 0 =&gt; 0
sqlsrv.SetLocaleInfo =&gt; 2 =&gt; 2
sqlsrv.WarningsReturnAsErrors =&gt; On =&gt; On

standard

Dynamic Library Support =&gt; enabled
Path to sendmail =&gt; /usr/bin/msmtp -t

Directive =&gt; Local Value =&gt; Master Value
assert.active =&gt; On =&gt; On
assert.bail =&gt; Off =&gt; Off
assert.callback =&gt; no value =&gt; no value
assert.exception =&gt; On =&gt; On
assert.warning =&gt; On =&gt; On
auto_detect_line_endings =&gt; Off =&gt; Off
default_socket_timeout =&gt; 60 =&gt; 60
from =&gt; no value =&gt; no value
session.trans_sid_hosts =&gt; no value =&gt; no value
session.trans_sid_tags =&gt; a=href,area=href,frame=src,form= =&gt; a=href,area=href,frame=src,form=
unserialize_max_depth =&gt; 4096 =&gt; 4096
url_rewriter.hosts =&gt; no value =&gt; no value
url_rewriter.tags =&gt; form= =&gt; form=
user_agent =&gt; no value =&gt; no value

sysvmsg

sysvmsg support =&gt; enabled

sysvsem

sysvsem support =&gt; enabled

sysvshm

sysvshm support =&gt; enabled

tokenizer

Tokenizer Support =&gt; enabled

xml

XML Support =&gt; active
XML Namespace Support =&gt; active
libxml2 Version =&gt; 2.9.4

xmlreader

XMLReader =&gt; enabled

xmlwriter

XMLWriter =&gt; enabled

xsl

XSL =&gt; enabled
libxslt Version =&gt; 1.1.32
libxslt compiled against libxml Version =&gt; 2.9.4
EXSLT =&gt; enabled
libexslt Version =&gt; 1.1.32

zip

Zip =&gt; enabled
Zip version =&gt; 1.19.5
Libzip version =&gt; 1.5.1

zlib

ZLib Support =&gt; enabled
Stream Wrapper =&gt; compress.zlib://
Stream Filter =&gt; zlib.inflate, zlib.deflate
Compiled Version =&gt; 1.2.11
Linked Version =&gt; 1.2.11

Directive =&gt; Local Value =&gt; Master Value
zlib.output_compression =&gt; Off =&gt; Off
zlib.output_compression_level =&gt; -1 =&gt; -1
zlib.output_handler =&gt; no value =&gt; no value

Additional Modules

Module Name
</pre>
			</div>


		</div>
	</div>
	</div>
</section>

<section class="section">
	<h2 class="section-label"><a href="#" data-tracy-ref="^+" class="tracy-toggle tracy-collapsed">CLI request</a></h2>

	<div class="section-panel tracy-collapsed">
		<h3>Process ID 738</h3>
		<pre>php vendor/bin/console</pre>

		<h3>Arguments</h3>
		<div class="pane">
			<table>
				<tr><th>0</th><td><pre class="tracy-dump tracy-light"
><span class="tracy-dump-string" title="18 characters"><span>'</span>vendor/bin/console<span>'</span></span></pre>
</td></tr>
			</table>
		</div>
	</div>
</section>



		<footer>
			<ul>
				<li><b><a href="https://github.com/sponsors/dg" target="_blank" rel="noreferrer noopener">Please support Tracy via a donation 💙️</a></b></li>
				<li>Report generated at 2022/02/18 09:25:49</li>
				<li>PHP 8.0.14</li><li>Tracy 2.9.0</li>			</ul>
			<div class="footer-logo"><a href="https://tracy.nette.org" rel="noreferrer"></a></div>
		</footer>
	</div>
	<meta itemprop=tracy-snapshot content='{"2":{"object":"Illuminate\\Foundation\\Application","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Foundation/Application.php","line":31,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2FApplication.php\u0026line=31\u0026search=\u0026replace="},"items":[["basePath","/web",1],["hasBeenBootstrapped",true,1],["booted",true,1],["bootingCallbacks",[[0,{"ref":222}],[1,{"ref":239}]],1],["bootedCallbacks",[[0,{"ref":30}],[1,{"ref":245}]],1],["terminatingCallbacks",[],1],["serviceProviders",[[0,{"ref":6}],[1,{"ref":8}],[2,{"ref":10}],[3,{"ref":119}],[4,{"ref":118}],[5,{"ref":116}],[6,{"ref":108}],[7,{"ref":105}],[8,{"ref":99}],[9,{"ref":98}],[10,{"ref":100}],[11,{"ref":89}],[12,{"ref":87}],[13,{"ref":81}],[14,{"ref":77}],[15,{"ref":72}],[16,{"ref":70}],[17,{"ref":67}],[18,{"ref":69}],[19,{"ref":65}],[20,{"ref":49}],[21,{"ref":147}],[22,{"ref":166}],[23,{"ref":167}],[24,{"ref":168}],[25,{"ref":170}],[26,{"ref":172}],[27,{"ref":214}],[28,{"ref":233}],[29,{"ref":129}],[30,{"ref":253}],[31,{"ref":258}],[32,{"ref":338}],[33,{"ref":351}],[34,{"ref":257}],[35,{"ref":353}],[36,{"ref":356}],[37,{"ref":360}],[38,{"ref":362}],[39,{"ref":365}],[40,{"ref":368}],[41,{"ref":371}],[42,{"ref":375}],[43,{"ref":376}]],1],["loadedProviders",[["Illuminate\\Events\\EventServiceProvider",true],["Illuminate\\Log\\LogServiceProvider",true],["Illuminate\\Routing\\RoutingServiceProvider",true],["Illuminate\\Auth\\AuthServiceProvider",true],["Illuminate\\Cookie\\CookieServiceProvider",true],["Illuminate\\Database\\DatabaseServiceProvider",true],["Illuminate\\Encryption\\EncryptionServiceProvider",true],["Illuminate\\Filesystem\\FilesystemServiceProvider",true],["Illuminate\\Foundation\\Providers\\FormRequestServiceProvider",true],["Illuminate\\Testing\\ParallelTestingServiceProvider",true],["Illuminate\\Foundation\\Providers\\FoundationServiceProvider",true],["Illuminate\\Notifications\\NotificationServiceProvider",true],["Illuminate\\Pagination\\PaginationServiceProvider",true],["Illuminate\\Session\\SessionServiceProvider",true],["Illuminate\\View\\ViewServiceProvider",true],["Fruitcake\\Cors\\CorsServiceProvider",true],["Laravel\\Sanctum\\SanctumServiceProvider",true],["Carbon\\Laravel\\ServiceProvider",true],["NunoMaduro\\Collision\\Adapters\\Laravel\\CollisionServiceProvider",true],["Spatie\\LaravelIgnition\\IgnitionServiceProvider",true],["DekApps\\LaravelLatte\\ServiceProvider",true],["DekApps\\LaravelTracy\\ServiceProvider",true],["App\\Providers\\AppServiceProvider",true],["App\\Providers\\AuthServiceProvider",true],["App\\Providers\\EventServiceProvider",true],["App\\Providers\\RouteServiceProvider",true],["App\\Providers\\DoctrineServiceProvider",true],["Illuminate\\Queue\\QueueServiceProvider",true],["Illuminate\\Cache\\CacheServiceProvider",true],["Illuminate\\Broadcasting\\BroadcastServiceProvider",true],["Illuminate\\Bus\\BusServiceProvider",true],["Illuminate\\Foundation\\Providers\\ArtisanServiceProvider",true],["Illuminate\\Database\\MigrationServiceProvider",true],["Illuminate\\Foundation\\Providers\\ComposerServiceProvider",true],["Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider",true],["Illuminate\\Hashing\\HashServiceProvider",true],["Illuminate\\Mail\\MailServiceProvider",true],["Illuminate\\Pipeline\\PipelineServiceProvider",true],["Illuminate\\Redis\\RedisServiceProvider",true],["Illuminate\\Auth\\Passwords\\PasswordResetServiceProvider",true],["Illuminate\\Translation\\TranslationServiceProvider",true],["Illuminate\\Validation\\ValidationServiceProvider",true],["Laravel\\Sail\\SailServiceProvider",true],["Laravel\\Tinker\\TinkerServiceProvider",true]],1],["deferredServices",[],1],["appPath",null,1],["databasePath",null,1],["langPath","/web/lang",1],["storagePath",null,1],["environmentPath",null,1],["environmentFile",".env",1],["isRunningInConsole",true,1],["namespace",null,1],["absoluteCachePathPrefixes",[[0,"/"],[1,"\\"]],1],["resolved",[["events",true],["App\\Console\\Kernel",true],["Illuminate\\Contracts\\Console\\Kernel",true],["Illuminate\\Foundation\\Bootstrap\\LoadEnvironmentVariables",true],["Illuminate\\Foundation\\Bootstrap\\LoadConfiguration",true],["DekApps\\LaravelTracy\\ResetHandleExceptions",true],["Illuminate\\Foundation\\Bootstrap\\HandleExceptions",true],["Illuminate\\Foundation\\Bootstrap\\RegisterFacades",true],["Illuminate\\Foundation\\PackageManifest",true],["Illuminate\\Foundation\\Bootstrap\\SetRequestForConsole",true],["Illuminate\\Foundation\\Bootstrap\\RegisterProviders",true],["env",true],["App\\Exceptions\\Handler",true],["Illuminate\\Contracts\\Debug\\ExceptionHandler",true],["log",true],["view.engine.resolver",true],["files",true],["view.finder",true],["view",true],["Illuminate\\Foundation\\Bootstrap\\BootProviders",true],["db.factory",true],["db",true],["Illuminate\\Testing\\ParallelTesting",true],["router",true],["App\\Http\\Kernel",true],["Illuminate\\Contracts\\Http\\Kernel",true],["Spatie\\LaravelIgnition\\Recorders\\DumpRecorder\\DumpRecorder",true],["Spatie\\LaravelIgnition\\Recorders\\LogRecorder\\LogRecorder",true],["Spatie\\LaravelIgnition\\Recorders\\QueryRecorder\\QueryRecorder",true],["Spatie\\LaravelIgnition\\Recorders\\JobRecorder\\JobRecorder",true],["queue",true],["cache",true],["Illuminate\\Cache\\RateLimiter",true]],1],["bindings",{"array":null,"length":184,"items":[["Illuminate\\Foundation\\Mix",[["concrete",{"ref":4}],["shared",true]]],["Illuminate\\Foundation\\PackageManifest",[["concrete",{"ref":5}],["shared",true]]],["events",[["concrete",{"ref":7}],["shared",true]]],["log",[["concrete",{"ref":9}],["shared",true]]],["router",[["concrete",{"ref":11}],["shared",true]]],["url",[["concrete",{"ref":12}],["shared",true]]],["redirect",[["concrete",{"ref":14}],["shared",true]]],["Psr\\Http\\Message\\ServerRequestInterface",[["concrete",{"ref":15}],["shared",false]]],["Psr\\Http\\Message\\ResponseInterface",[["concrete",{"ref":16}],["shared",false]]],["Illuminate\\Contracts\\Routing\\ResponseFactory",[["concrete",{"ref":17}],["shared",true]]],["Illuminate\\Routing\\Contracts\\ControllerDispatcher",[["concrete",{"ref":18}],["shared",true]]],["Illuminate\\Contracts\\Http\\Kernel",[["concrete",{"ref":19}],["shared",true]]],["Illuminate\\Contracts\\Console\\Kernel",[["concrete",{"ref":20}],["shared",true]]],["Illuminate\\Contracts\\Debug\\ExceptionHandler",[["concrete",{"ref":64}],["shared",true]]],["Illuminate\\Foundation\\Bootstrap\\HandleExceptions",[["concrete",{"ref":22}],["shared",true]]],["env",[["concrete",{"ref":122}],["shared",false]]],["auth",[["concrete",{"ref":124}],["shared",true]]],["auth.driver",[["concrete",{"ref":54}],["shared",true]]],["Illuminate\\Contracts\\Auth\\Authenticatable",[["concrete",{"ref":121}],["shared",false]]],["Illuminate\\Contracts\\Auth\\Access\\Gate",[["concrete",{"ref":47}],["shared",true]]],["Illuminate\\Auth\\Middleware\\RequirePassword",[["concrete",{"ref":57}],["shared",false]]],["cookie",[["concrete",{"ref":117}],["shared",true]]],["db.factory",[["concrete",{"ref":115}],["shared",true]]],["db",[["concrete",{"ref":114}],["shared",true]]],["db.connection",[["concrete",{"ref":113}],["shared",false]]],["db.schema",[["concrete",{"ref":112}],["shared",false]]],["db.transactions",[["concrete",{"ref":111}],["shared",true]]],["Faker\\Generator",[["concrete",{"ref":110}],["shared",true]]],["Illuminate\\Contracts\\Queue\\EntityResolver",[["concrete",{"ref":109}],["shared",true]]],["encrypter",[["concrete",{"ref":107}],["shared",true]]],["files",[["concrete",{"ref":104}],["shared",true]]],["filesystem",[["concrete",{"ref":103}],["shared",true]]],["filesystem.disk",[["concrete",{"ref":102}],["shared",true]]],["filesystem.cloud",[["concrete",{"ref":101}],["shared",true]]],["Illuminate\\Testing\\ParallelTesting",[["concrete",{"ref":97}],["shared",true]]],["Illuminate\\Foundation\\MaintenanceModeManager",[["concrete",{"ref":91}],["shared",true]]],["Illuminate\\Contracts\\Foundation\\MaintenanceMode",[["concrete",{"ref":90}],["shared",false]]],["Illuminate\\Notifications\\ChannelManager",[["concrete",{"ref":88}],["shared",true]]],["session",[["concrete",{"ref":80}],["shared",true]]],["session.store",[["concrete",{"ref":79}],["shared",true]]],["Illuminate\\Session\\Middleware\\StartSession",[["concrete",{"ref":78}],["shared",true]]],["view",[["concrete",{"ref":76}],["shared",true]]],["view.finder",[["concrete",{"ref":75}],["shared",false]]],["blade.compiler",[["concrete",{"ref":74}],["shared",true]]],["view.engine.resolver",[["concrete",{"ref":73}],["shared",true]]],["Asm89\\Stack\\CorsService",[["concrete",{"ref":71}],["shared",true]]],["NunoMaduro\\Collision\\Contracts\\Provider",[["concrete",{"ref":68}],["shared",false]]],["Spatie\\FlareClient\\Flare",[["concrete",{"ref":60}],["shared",true]]],["Spatie\\LaravelIgnition\\Support\\SentReports",[["concrete",{"ref":59}],["shared",true]]],["Spatie\\Ignition\\Config\\IgnitionConfig",[["concrete",{"ref":58}],["shared",true]]],["Spatie\\Ignition\\Contracts\\SolutionProviderRepository",[["concrete",{"ref":55}],["shared",true]]],["Spatie\\Ignition\\Ignition",[["concrete",{"ref":44}],["shared",true]]],["Whoops\\Handler\\HandlerInterface",[["concrete",{"ref":37}],["shared",false]]],["Illuminate\\Contracts\\Foundation\\ExceptionRenderer",[["concrete",{"ref":41}],["shared",false]]],["Spatie\\LaravelIgnition\\Recorders\\DumpRecorder\\DumpRecorder",[["concrete",{"ref":42}],["shared",true]]],["Spatie\\LaravelIgnition\\Recorders\\LogRecorder\\LogRecorder",[["concrete",{"ref":43}],["shared",true]]],["Spatie\\LaravelIgnition\\Recorders\\QueryRecorder\\QueryRecorder",[["concrete",{"ref":132}],["shared",true]]],["Spatie\\LaravelIgnition\\Recorders\\JobRecorder\\JobRecorder",[["concrete",{"ref":46}],["shared",true]]],["flare.logger",[["concrete",{"ref":130}],["shared",true]]],["Latte\\Engine",[["concrete",{"ref":52}],["shared",true]]],["Doctrine\\ORM\\EntityManager",[["concrete",{"ref":173}],["shared",true]]],["Spatie\\LaravelIgnition\\Recorders\\DumpRecorder\\MultiDumpHandler",[["concrete",{"ref":202}],["shared",true]]],["queue",[["concrete",{"ref":217}],["shared",true]]],["queue.connection",[["concrete",{"ref":218}],["shared",true]]],["queue.worker",[["concrete",{"ref":219}],["shared",true]]],["queue.listener",[["concrete",{"ref":220}],["shared",true]]],["queue.failer",[["concrete",{"ref":221}],["shared",true]]],["cache",[["concrete",{"ref":234}],["shared",true]]],["cache.store",[["concrete",{"ref":235}],["shared",true]]],["cache.psr6",[["concrete",{"ref":236}],["shared",true]]],["memcached.connector",[["concrete",{"ref":237}],["shared",true]]],["Illuminate\\Cache\\RateLimiter",[["concrete",{"ref":238}],["shared",true]]],["Illuminate\\Console\\Scheduling\\Schedule",[["concrete",{"ref":123}],["shared",true]]],["Illuminate\\Broadcasting\\BroadcastManager",[["concrete",{"ref":128}],["shared",true]]],["Illuminate\\Contracts\\Broadcasting\\Broadcaster",[["concrete",{"ref":246}],["shared",true]]],["Illuminate\\Bus\\Dispatcher",[["concrete",{"ref":254}],["shared",true]]],["Illuminate\\Bus\\BatchRepository",[["concrete",{"ref":255}],["shared",true]]],["Illuminate\\Bus\\DatabaseBatchRepository",[["concrete",{"ref":256}],["shared",true]]],["Illuminate\\Cache\\Console\\ClearCommand",[["concrete",{"ref":259}],["shared",true]]],["Illuminate\\Cache\\Console\\ForgetCommand",[["concrete",{"ref":260}],["shared",true]]],["Illuminate\\Foundation\\Console\\ClearCompiledCommand",[["concrete",{"ref":261}],["shared",true]]],["Illuminate\\Auth\\Console\\ClearResetsCommand",[["concrete",{"ref":262}],["shared",true]]],["Illuminate\\Foundation\\Console\\ConfigCacheCommand",[["concrete",{"ref":263}],["shared",true]]],["Illuminate\\Foundation\\Console\\ConfigClearCommand",[["concrete",{"ref":264}],["shared",true]]],["Illuminate\\Database\\Console\\DbCommand",[["concrete",{"ref":265}],["shared",true]]],["Illuminate\\Database\\Console\\PruneCommand",[["concrete",{"ref":266}],["shared",true]]],["Illuminate\\Database\\Console\\WipeCommand",[["concrete",{"ref":267}],["shared",true]]],["Illuminate\\Foundation\\Console\\DownCommand",[["concrete",{"ref":268}],["shared",true]]],["Illuminate\\Foundation\\Console\\EnvironmentCommand",[["concrete",{"ref":269}],["shared",true]]],["Illuminate\\Foundation\\Console\\EventCacheCommand",[["concrete",{"ref":270}],["shared",true]]],["Illuminate\\Foundation\\Console\\EventClearCommand",[["concrete",{"ref":271}],["shared",true]]],["Illuminate\\Foundation\\Console\\EventListCommand",[["concrete",{"ref":272}],["shared",true]]],["Illuminate\\Foundation\\Console\\KeyGenerateCommand",[["concrete",{"ref":273}],["shared",true]]],["Illuminate\\Foundation\\Console\\OptimizeCommand",[["concrete",{"ref":274}],["shared",true]]],["Illuminate\\Foundation\\Console\\OptimizeClearCommand",[["concrete",{"ref":275}],["shared",true]]],["Illuminate\\Foundation\\Console\\PackageDiscoverCommand",[["concrete",{"ref":276}],["shared",true]]],["Illuminate\\Queue\\Console\\ClearCommand",[["concrete",{"ref":277}],["shared",true]]],["Illuminate\\Queue\\Console\\ListFailedCommand",[["concrete",{"ref":278}],["shared",true]]],["Illuminate\\Queue\\Console\\FlushFailedCommand",[["concrete",{"ref":279}],["shared",true]]],["Illuminate\\Queue\\Console\\ForgetFailedCommand",[["concrete",{"ref":280}],["shared",true]]]]},1],["methodBindings",[],1],["instances",[["path","/web/app"],["path.base","/web"],["path.config","/web/config"],["path.public","/web/public"],["path.storage","/web/storage"],["path.database","/web/database"],["path.resources","/web/resources"],["path.bootstrap","/web/bootstrap"],["path.lang","/web/lang"],["app",{"ref":2}],["Illuminate\\Container\\Container",{"ref":2}],["events",{"ref":27}],["Illuminate\\Contracts\\Console\\Kernel",{"ref":29}],["config",{"ref":25}],["Illuminate\\Foundation\\Bootstrap\\HandleExceptions",{"ref":40}],["Illuminate\\Foundation\\PackageManifest",{"ref":26}],["request",{"ref":120}],["Illuminate\\Contracts\\Debug\\ExceptionHandler",{"ref":21}],["log",{"ref":126}],["view.engine.resolver",{"ref":141}],["files",{"ref":144}],["view",{"ref":145}],["db.factory",{"ref":38}],["db",{"ref":51}],["Illuminate\\Testing\\ParallelTesting",{"ref":177}],["router",{"ref":184}],["Illuminate\\Contracts\\Http\\Kernel",{"ref":192}],["date",{"ref":195}],["Spatie\\LaravelIgnition\\Recorders\\DumpRecorder\\DumpRecorder",{"ref":204}],["Spatie\\LaravelIgnition\\Recorders\\LogRecorder\\LogRecorder",{"ref":211}],["Spatie\\LaravelIgnition\\Recorders\\QueryRecorder\\QueryRecorder",{"ref":212}],["Spatie\\LaravelIgnition\\Recorders\\JobRecorder\\JobRecorder",{"ref":213}],["queue",{"ref":223}],["cache",{"ref":241}],["Illuminate\\Cache\\RateLimiter",{"ref":240}]],1],["scopedInstances",[],1],["aliases",[["Illuminate\\Foundation\\Application","app"],["Illuminate\\Contracts\\Container\\Container","app"],["Illuminate\\Contracts\\Foundation\\Application","app"],["Psr\\Container\\ContainerInterface","app"],["Illuminate\\Auth\\AuthManager","auth"],["Illuminate\\Contracts\\Auth\\Factory","auth"],["Illuminate\\Contracts\\Auth\\Guard","auth.driver"],["Illuminate\\View\\Compilers\\BladeCompiler","blade.compiler"],["Illuminate\\Cache\\CacheManager","cache"],["Illuminate\\Contracts\\Cache\\Factory","cache"],["Illuminate\\Cache\\Repository","cache.store"],["Illuminate\\Contracts\\Cache\\Repository","cache.store"],["Psr\\SimpleCache\\CacheInterface","cache.store"],["Symfony\\Component\\Cache\\Adapter\\Psr16Adapter","cache.psr6"],["Symfony\\Component\\Cache\\Adapter\\AdapterInterface","cache.psr6"],["Psr\\Cache\\CacheItemPoolInterface","cache.psr6"],["Illuminate\\Config\\Repository","config"],["Illuminate\\Contracts\\Config\\Repository","config"],["Illuminate\\Cookie\\CookieJar","cookie"],["Illuminate\\Contracts\\Cookie\\Factory","cookie"],["Illuminate\\Contracts\\Cookie\\QueueingFactory","cookie"],["Illuminate\\Database\\DatabaseManager","db"],["Illuminate\\Database\\ConnectionResolverInterface","db"],["Illuminate\\Database\\Connection","db.connection"],["Illuminate\\Database\\ConnectionInterface","db.connection"],["Illuminate\\Database\\Schema\\Builder","db.schema"],["Illuminate\\Encryption\\Encrypter","encrypter"],["Illuminate\\Contracts\\Encryption\\Encrypter","encrypter"],["Illuminate\\Contracts\\Encryption\\StringEncrypter","encrypter"],["Illuminate\\Events\\Dispatcher","events"],["Illuminate\\Contracts\\Events\\Dispatcher","events"],["Illuminate\\Filesystem\\Filesystem","files"],["Illuminate\\Filesystem\\FilesystemManager","filesystem"],["Illuminate\\Contracts\\Filesystem\\Factory","filesystem"],["Illuminate\\Contracts\\Filesystem\\Filesystem","filesystem.disk"],["Illuminate\\Contracts\\Filesystem\\Cloud","filesystem.cloud"],["Illuminate\\Hashing\\HashManager","hash"],["Illuminate\\Contracts\\Hashing\\Hasher","hash.driver"],["Illuminate\\Translation\\Translator","translator"],["Illuminate\\Contracts\\Translation\\Translator","translator"],["Illuminate\\Log\\LogManager","log"],["Psr\\Log\\LoggerInterface","log"],["Illuminate\\Mail\\MailManager","mail.manager"],["Illuminate\\Contracts\\Mail\\Factory","mail.manager"],["Illuminate\\Mail\\Mailer","mailer"],["Illuminate\\Contracts\\Mail\\Mailer","mailer"],["Illuminate\\Contracts\\Mail\\MailQueue","mailer"],["Illuminate\\Auth\\Passwords\\PasswordBrokerManager","auth.password"],["Illuminate\\Contracts\\Auth\\PasswordBrokerFactory","auth.password"],["Illuminate\\Auth\\Passwords\\PasswordBroker","auth.password.broker"],["Illuminate\\Contracts\\Auth\\PasswordBroker","auth.password.broker"],["Illuminate\\Queue\\QueueManager","queue"],["Illuminate\\Contracts\\Queue\\Factory","queue"],["Illuminate\\Contracts\\Queue\\Monitor","queue"],["Illuminate\\Contracts\\Queue\\Queue","queue.connection"],["Illuminate\\Queue\\Failed\\FailedJobProviderInterface","queue.failer"],["Illuminate\\Routing\\Redirector","redirect"],["Illuminate\\Redis\\RedisManager","redis"],["Illuminate\\Contracts\\Redis\\Factory","redis"],["Illuminate\\Redis\\Connections\\Connection","redis.connection"],["Illuminate\\Contracts\\Redis\\Connection","redis.connection"],["Illuminate\\Http\\Request","request"],["Symfony\\Component\\HttpFoundation\\Request","request"],["Illuminate\\Routing\\Router","router"],["Illuminate\\Contracts\\Routing\\Registrar","router"],["Illuminate\\Contracts\\Routing\\BindingRegistrar","router"],["Illuminate\\Session\\SessionManager","session"],["Illuminate\\Session\\Store","session.store"],["Illuminate\\Contracts\\Session\\Session","session.store"],["Illuminate\\Routing\\UrlGenerator","url"],["Illuminate\\Contracts\\Routing\\UrlGenerator","url"],["Illuminate\\Validation\\Factory","validator"],["Illuminate\\Contracts\\Validation\\Factory","validator"],["Illuminate\\View\\Factory","view"],["Illuminate\\Contracts\\View\\Factory","view"],["Illuminate\\Contracts\\Notifications\\Dispatcher","Illuminate\\Notifications\\ChannelManager"],["Illuminate\\Contracts\\Notifications\\Factory","Illuminate\\Notifications\\ChannelManager"],["Illuminate\\Contracts\\Broadcasting\\Factory","Illuminate\\Broadcasting\\BroadcastManager"],["Illuminate\\Contracts\\Bus\\Dispatcher","Illuminate\\Bus\\Dispatcher"],["Illuminate\\Contracts\\Bus\\QueueingDispatcher","Illuminate\\Bus\\Dispatcher"]],1],["abstractAliases",[["app",[[0,"Illuminate\\Foundation\\Application"],[1,"Illuminate\\Contracts\\Container\\Container"],[2,"Illuminate\\Contracts\\Foundation\\Application"],[3,"Psr\\Container\\ContainerInterface"]]],["auth",[[0,"Illuminate\\Auth\\AuthManager"],[1,"Illuminate\\Contracts\\Auth\\Factory"]]],["auth.driver",[[0,"Illuminate\\Contracts\\Auth\\Guard"]]],["blade.compiler",[[0,"Illuminate\\View\\Compilers\\BladeCompiler"]]],["cache",[[0,"Illuminate\\Cache\\CacheManager"],[1,"Illuminate\\Contracts\\Cache\\Factory"]]],["cache.store",[[0,"Illuminate\\Cache\\Repository"],[1,"Illuminate\\Contracts\\Cache\\Repository"],[2,"Psr\\SimpleCache\\CacheInterface"]]],["cache.psr6",[[0,"Symfony\\Component\\Cache\\Adapter\\Psr16Adapter"],[1,"Symfony\\Component\\Cache\\Adapter\\AdapterInterface"],[2,"Psr\\Cache\\CacheItemPoolInterface"]]],["config",[[0,"Illuminate\\Config\\Repository"],[1,"Illuminate\\Contracts\\Config\\Repository"]]],["cookie",[[0,"Illuminate\\Cookie\\CookieJar"],[1,"Illuminate\\Contracts\\Cookie\\Factory"],[2,"Illuminate\\Contracts\\Cookie\\QueueingFactory"]]],["db",[[0,"Illuminate\\Database\\DatabaseManager"],[1,"Illuminate\\Database\\ConnectionResolverInterface"]]],["db.connection",[[0,"Illuminate\\Database\\Connection"],[1,"Illuminate\\Database\\ConnectionInterface"]]],["db.schema",[[0,"Illuminate\\Database\\Schema\\Builder"]]],["encrypter",[[0,"Illuminate\\Encryption\\Encrypter"],[1,"Illuminate\\Contracts\\Encryption\\Encrypter"],[2,"Illuminate\\Contracts\\Encryption\\StringEncrypter"]]],["events",[[0,"Illuminate\\Events\\Dispatcher"],[1,"Illuminate\\Contracts\\Events\\Dispatcher"]]],["files",[[0,"Illuminate\\Filesystem\\Filesystem"]]],["filesystem",[[0,"Illuminate\\Filesystem\\FilesystemManager"],[1,"Illuminate\\Contracts\\Filesystem\\Factory"]]],["filesystem.disk",[[0,"Illuminate\\Contracts\\Filesystem\\Filesystem"]]],["filesystem.cloud",[[0,"Illuminate\\Contracts\\Filesystem\\Cloud"]]],["hash",[[0,"Illuminate\\Hashing\\HashManager"]]],["hash.driver",[[0,"Illuminate\\Contracts\\Hashing\\Hasher"]]],["translator",[[0,"Illuminate\\Translation\\Translator"],[1,"Illuminate\\Contracts\\Translation\\Translator"]]],["log",[[0,"Illuminate\\Log\\LogManager"],[1,"Psr\\Log\\LoggerInterface"]]],["mail.manager",[[0,"Illuminate\\Mail\\MailManager"],[1,"Illuminate\\Contracts\\Mail\\Factory"]]],["mailer",[[0,"Illuminate\\Mail\\Mailer"],[1,"Illuminate\\Contracts\\Mail\\Mailer"],[2,"Illuminate\\Contracts\\Mail\\MailQueue"]]],["auth.password",[[0,"Illuminate\\Auth\\Passwords\\PasswordBrokerManager"],[1,"Illuminate\\Contracts\\Auth\\PasswordBrokerFactory"]]],["auth.password.broker",[[0,"Illuminate\\Auth\\Passwords\\PasswordBroker"],[1,"Illuminate\\Contracts\\Auth\\PasswordBroker"]]],["queue",[[0,"Illuminate\\Queue\\QueueManager"],[1,"Illuminate\\Contracts\\Queue\\Factory"],[2,"Illuminate\\Contracts\\Queue\\Monitor"]]],["queue.connection",[[0,"Illuminate\\Contracts\\Queue\\Queue"]]],["queue.failer",[[0,"Illuminate\\Queue\\Failed\\FailedJobProviderInterface"]]],["redirect",[[0,"Illuminate\\Routing\\Redirector"]]],["redis",[[0,"Illuminate\\Redis\\RedisManager"],[1,"Illuminate\\Contracts\\Redis\\Factory"]]],["redis.connection",[[0,"Illuminate\\Redis\\Connections\\Connection"],[1,"Illuminate\\Contracts\\Redis\\Connection"]]],["request",[[0,"Illuminate\\Http\\Request"],[1,"Symfony\\Component\\HttpFoundation\\Request"]]],["router",[[0,"Illuminate\\Routing\\Router"],[1,"Illuminate\\Contracts\\Routing\\Registrar"],[2,"Illuminate\\Contracts\\Routing\\BindingRegistrar"]]],["session",[[0,"Illuminate\\Session\\SessionManager"]]],["session.store",[[0,"Illuminate\\Session\\Store"],[1,"Illuminate\\Contracts\\Session\\Session"]]],["url",[[0,"Illuminate\\Routing\\UrlGenerator"],[1,"Illuminate\\Contracts\\Routing\\UrlGenerator"]]],["validator",[[0,"Illuminate\\Validation\\Factory"],[1,"Illuminate\\Contracts\\Validation\\Factory"]]],["view",[[0,"Illuminate\\View\\Factory"],[1,"Illuminate\\Contracts\\View\\Factory"]]],["Illuminate\\Notifications\\ChannelManager",[[0,"Illuminate\\Contracts\\Notifications\\Dispatcher"],[1,"Illuminate\\Contracts\\Notifications\\Factory"]]],["Illuminate\\Broadcasting\\BroadcastManager",[[0,"Illuminate\\Contracts\\Broadcasting\\Factory"]]],["Illuminate\\Bus\\Dispatcher",[[0,"Illuminate\\Contracts\\Bus\\Dispatcher"],[1,"Illuminate\\Contracts\\Bus\\QueueingDispatcher"]]]],1],["extenders",[["url",[[0,{"ref":13}]]]],1],["tags",[],1],["buildStack",[],1],["with",[[0,[]]],1],["contextual",[],0],["reboundCallbacks",[["request",[[0,{"ref":48}]]],["events",[[0,{"ref":53}]]]],1],["globalBeforeResolvingCallbacks",[],1],["globalResolvingCallbacks",[],1],["globalAfterResolvingCallbacks",[],1],["beforeResolvingCallbacks",[],1],["resolvingCallbacks",[["Illuminate\\Foundation\\Http\\FormRequest",[[0,{"ref":175}]]]],1],["afterResolvingCallbacks",[["Illuminate\\Contracts\\Validation\\ValidatesWhenResolved",[[0,{"ref":174}]]],["view",[[0,{"ref":179}],[1,{"ref":180}]]],["migrator",[[0,{"ref":182}]]],["auth",[[0,{"ref":187}]]]],1]]},"222":{"object":"Closure()","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Foundation/Application.php","line":823,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2FApplication.php\u0026line=823\u0026search=\u0026replace="},"items":[["file","/web/vendor/laravel/framework/src/Illuminate/Foundation/Application.php:823",4],["use",{"object":"$instance","items":[["$instance",{"ref":214},4]],"collapsed":true}]]},"214":{"object":"Illuminate\\Queue\\QueueServiceProvider","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Queue/QueueServiceProvider.php","line":22,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FQueue%2FQueueServiceProvider.php\u0026line=22\u0026search=\u0026replace="},"items":[["app",{"ref":2},1],["bootingCallbacks",[],1],["bootedCallbacks",[],1]]},"239":{"object":"Closure()","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Foundation/Application.php","line":823,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2FApplication.php\u0026line=823\u0026search=\u0026replace="},"items":[["file","/web/vendor/laravel/framework/src/Illuminate/Foundation/Application.php:823",4],["use",{"object":"$instance","items":[["$instance",{"ref":233},4]],"collapsed":true}]]},"233":{"object":"Illuminate\\Cache\\CacheServiceProvider","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Cache/CacheServiceProvider.php","line":9,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FCacheServiceProvider.php\u0026line=9\u0026search=\u0026replace="},"items":[["app",{"ref":2},1],["bootingCallbacks",[],1],["bootedCallbacks",[],1]]},"30":{"object":"Closure()","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php","line":88,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2FConsole%2FKernel.php\u0026line=88\u0026search=\u0026replace="},"items":[["file","/web/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php:88",4]]},"245":{"object":"Closure()","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Foundation/Support/Providers/RouteServiceProvider.php","line":47,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2FSupport%2FProviders%2FRouteServiceProvider.php\u0026line=47\u0026search=\u0026replace="},"items":[["file","/web/vendor/laravel/framework/src/Illuminate/Foundation/Support/Providers/RouteServiceProvider.php:47",4]]},"6":{"object":"Illuminate\\Events\\EventServiceProvider","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Events/EventServiceProvider.php","line":8,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FEvents%2FEventServiceProvider.php\u0026line=8\u0026search=\u0026replace="},"items":[["app",{"ref":2},1],["bootingCallbacks",[],1],["bootedCallbacks",[],1]]},"8":{"object":"Illuminate\\Log\\LogServiceProvider","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Log/LogServiceProvider.php","line":7,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FLog%2FLogServiceProvider.php\u0026line=7\u0026search=\u0026replace="},"items":[["app",{"ref":2},1],["bootingCallbacks",[],1],["bootedCallbacks",[],1]]},"10":{"object":"Illuminate\\Routing\\RoutingServiceProvider","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Routing/RoutingServiceProvider.php","line":17,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FRouting%2FRoutingServiceProvider.php\u0026line=17\u0026search=\u0026replace="},"items":[["app",{"ref":2},1],["bootingCallbacks",[],1],["bootedCallbacks",[],1]]},"119":{"object":"Illuminate\\Auth\\AuthServiceProvider","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Auth/AuthServiceProvider.php","line":13,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FAuthServiceProvider.php\u0026line=13\u0026search=\u0026replace="},"items":[["app",{"ref":2},1],["bootingCallbacks",[],1],["bootedCallbacks",[],1]]},"118":{"object":"Illuminate\\Cookie\\CookieServiceProvider","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Cookie/CookieServiceProvider.php","line":7,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCookie%2FCookieServiceProvider.php\u0026line=7\u0026search=\u0026replace="},"items":[["app",{"ref":2},1],["bootingCallbacks",[],1],["bootedCallbacks",[],1]]},"116":{"object":"Illuminate\\Database\\DatabaseServiceProvider","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Database/DatabaseServiceProvider.php","line":13,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FDatabase%2FDatabaseServiceProvider.php\u0026line=13\u0026search=\u0026replace="},"items":[["app",{"ref":2},1],["bootingCallbacks",[],1],["bootedCallbacks",[],1]]},"108":{"object":"Illuminate\\Encryption\\EncryptionServiceProvider","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php","line":9,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FEncryption%2FEncryptionServiceProvider.php\u0026line=9\u0026search=\u0026replace="},"items":[["app",{"ref":2},1],["bootingCallbacks",[],1],["bootedCallbacks",[],1]]},"105":{"object":"Illuminate\\Filesystem\\FilesystemServiceProvider","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Filesystem/FilesystemServiceProvider.php","line":7,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFilesystem%2FFilesystemServiceProvider.php\u0026line=7\u0026search=\u0026replace="},"items":[["app",{"ref":2},1],["bootingCallbacks",[],1],["bootedCallbacks",[],1]]},"99":{"object":"Illuminate\\Foundation\\Providers\\FormRequestServiceProvider","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Foundation/Providers/FormRequestServiceProvider.php","line":10,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2FProviders%2FFormRequestServiceProvider.php\u0026line=10\u0026search=\u0026replace="},"items":[["app",{"ref":2},1],["bootingCallbacks",[],1],["bootedCallbacks",[],1]]},"98":{"object":"Illuminate\\Testing\\ParallelTestingServiceProvider","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Testing/ParallelTestingServiceProvider.php","line":9,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FTesting%2FParallelTestingServiceProvider.php\u0026line=9\u0026search=\u0026replace="},"items":[["app",{"ref":2},1],["bootingCallbacks",[],1],["bootedCallbacks",[],1]]},"100":{"object":"Illuminate\\Foundation\\Providers\\FoundationServiceProvider","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Foundation/Providers/FoundationServiceProvider.php","line":15,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2FProviders%2FFoundationServiceProvider.php\u0026line=15\u0026search=\u0026replace="},"items":[["providers",[[0,"Illuminate\\Foundation\\Providers\\FormRequestServiceProvider"],[1,"Illuminate\\Testing\\ParallelTestingServiceProvider"]],1],["instances",[[0,{"ref":99}],[1,{"ref":98}]],1],["app",{"ref":2},1],["bootingCallbacks",[],1],["bootedCallbacks",[],1]]},"89":{"object":"Illuminate\\Notifications\\NotificationServiceProvider","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Notifications/NotificationServiceProvider.php","line":9,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FNotifications%2FNotificationServiceProvider.php\u0026line=9\u0026search=\u0026replace="},"items":[["app",{"ref":2},1],["bootingCallbacks",[],1],["bootedCallbacks",[],1]]},"87":{"object":"Illuminate\\Pagination\\PaginationServiceProvider","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Pagination/PaginationServiceProvider.php","line":7,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FPagination%2FPaginationServiceProvider.php\u0026line=7\u0026search=\u0026replace="},"items":[["app",{"ref":2},1],["bootingCallbacks",[],1],["bootedCallbacks",[],1]]},"81":{"object":"Illuminate\\Session\\SessionServiceProvider","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Session/SessionServiceProvider.php","line":9,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FSessionServiceProvider.php\u0026line=9\u0026search=\u0026replace="},"items":[["app",{"ref":2},1],["bootingCallbacks",[],1],["bootedCallbacks",[],1]]},"77":{"object":"Illuminate\\View\\ViewServiceProvider","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/View/ViewServiceProvider.php","line":12,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FView%2FViewServiceProvider.php\u0026line=12\u0026search=\u0026replace="},"items":[["app",{"ref":2},1],["bootingCallbacks",[],1],["bootedCallbacks",[],1]]},"72":{"object":"Fruitcake\\Cors\\CorsServiceProvider","editor":{"file":"/web/vendor/fruitcake/laravel-cors/src/CorsServiceProvider.php","line":11,"url":"editor://open/?file=%2Fweb%2Fvendor%2Ffruitcake%2Flaravel-cors%2Fsrc%2FCorsServiceProvider.php\u0026line=11\u0026search=\u0026replace="},"items":[["app",{"ref":2},1],["bootingCallbacks",[],1],["bootedCallbacks",[],1]]},"70":{"object":"Laravel\\Sanctum\\SanctumServiceProvider","editor":{"file":"/web/vendor/laravel/sanctum/src/SanctumServiceProvider.php","line":13,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fsanctum%2Fsrc%2FSanctumServiceProvider.php\u0026line=13\u0026search=\u0026replace="},"items":[["app",{"ref":2},1],["bootingCallbacks",[],1],["bootedCallbacks",[],1]]},"67":{"object":"Carbon\\Laravel\\ServiceProvider","editor":{"file":"/web/vendor/nesbot/carbon/src/Carbon/Laravel/ServiceProvider.php","line":25,"url":"editor://open/?file=%2Fweb%2Fvendor%2Fnesbot%2Fcarbon%2Fsrc%2FCarbon%2FLaravel%2FServiceProvider.php\u0026line=25\u0026search=\u0026replace="},"items":[["app",{"ref":2},1],["bootingCallbacks",[],1],["bootedCallbacks",[],1]]},"69":{"object":"NunoMaduro\\Collision\\Adapters\\Laravel\\CollisionServiceProvider","editor":{"file":"/web/vendor/nunomaduro/collision/src/Adapters/Laravel/CollisionServiceProvider.php","line":21,"url":"editor://open/?file=%2Fweb%2Fvendor%2Fnunomaduro%2Fcollision%2Fsrc%2FAdapters%2FLaravel%2FCollisionServiceProvider.php\u0026line=21\u0026search=\u0026replace="},"items":[["defer",true,1],["app",{"ref":2},1],["bootingCallbacks",[],1],["bootedCallbacks",[],1]]},"65":{"object":"Spatie\\LaravelIgnition\\IgnitionServiceProvider","editor":{"file":"/web/vendor/spatie/laravel-ignition/src/IgnitionServiceProvider.php","line":42,"url":"editor://open/?file=%2Fweb%2Fvendor%2Fspatie%2Flaravel-ignition%2Fsrc%2FIgnitionServiceProvider.php\u0026line=42\u0026search=\u0026replace="},"items":[["app",{"ref":2},1],["bootingCallbacks",[],1],["bootedCallbacks",[],1]]},"49":{"object":"DekApps\\LaravelLatte\\ServiceProvider","editor":{"file":"/web/vendor/dek-apps/laravel-latte/src/LaravelLatte/ServiceProvider.php","line":11,"url":"editor://open/?file=%2Fweb%2Fvendor%2Fdek-apps%2Flaravel-latte%2Fsrc%2FLaravelLatte%2FServiceProvider.php\u0026line=11\u0026search=\u0026replace="},"items":[["app",{"ref":2},1],["bootingCallbacks",[],1],["bootedCallbacks",[],1]]},"147":{"object":"DekApps\\LaravelTracy\\ServiceProvider","editor":{"file":"/web/vendor/dek-apps/laravel-tracy/src/ServiceProvider.php","line":9,"url":"editor://open/?file=%2Fweb%2Fvendor%2Fdek-apps%2Flaravel-tracy%2Fsrc%2FServiceProvider.php\u0026line=9\u0026search=\u0026replace="},"items":[["app",{"ref":2},1],["bootingCallbacks",[],1],["bootedCallbacks",[],1]]},"166":{"object":"App\\Providers\\AppServiceProvider","editor":{"file":"/web/app/Providers/AppServiceProvider.php","line":7,"url":"editor://open/?file=%2Fweb%2Fapp%2FProviders%2FAppServiceProvider.php\u0026line=7\u0026search=\u0026replace="},"items":[["app",{"ref":2},1],["bootingCallbacks",[],1],["bootedCallbacks",[],1]]},"167":{"object":"App\\Providers\\AuthServiceProvider","editor":{"file":"/web/app/Providers/AuthServiceProvider.php","line":8,"url":"editor://open/?file=%2Fweb%2Fapp%2FProviders%2FAuthServiceProvider.php\u0026line=8\u0026search=\u0026replace="},"items":[["policies",[],1],["app",{"ref":2},1],["bootingCallbacks",[],1],["bootedCallbacks",[],1]]},"168":{"object":"App\\Providers\\EventServiceProvider","editor":{"file":"/web/app/Providers/EventServiceProvider.php","line":10,"url":"editor://open/?file=%2Fweb%2Fapp%2FProviders%2FEventServiceProvider.php\u0026line=10\u0026search=\u0026replace="},"items":[["listen",[["Illuminate\\Auth\\Events\\Registered",{"array":null,"length":1}]],1],["subscribe",[],1],["observers",[],1],["app",{"ref":2},1],["bootingCallbacks",[[0,{"ref":169}]],1],["bootedCallbacks",[],1]]},"169":{"object":"Closure","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Foundation/Support/Providers/EventServiceProvider.php","line":39,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2FSupport%2FProviders%2FEventServiceProvider.php\u0026line=39\u0026search=\u0026replace="}},"170":{"object":"App\\Providers\\RouteServiceProvider","editor":{"file":"/web/app/Providers/RouteServiceProvider.php","line":11,"url":"editor://open/?file=%2Fweb%2Fapp%2FProviders%2FRouteServiceProvider.php\u0026line=11\u0026search=\u0026replace="},"items":[["namespace",null,1],["loadRoutesUsing",{"ref":244},1],["app",{"ref":2},1],["bootingCallbacks",[],1],["bootedCallbacks",[[0,{"ref":171}]],1]]},"244":{"object":"Closure()","editor":{"file":"/web/app/Providers/RouteServiceProvider.php","line":40,"url":"editor://open/?file=%2Fweb%2Fapp%2FProviders%2FRouteServiceProvider.php\u0026line=40\u0026search=\u0026replace="},"items":[["file","/web/app/Providers/RouteServiceProvider.php:40",4]]},"171":{"object":"Closure","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Foundation/Support/Providers/RouteServiceProvider.php","line":39,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2FSupport%2FProviders%2FRouteServiceProvider.php\u0026line=39\u0026search=\u0026replace="}},"172":{"object":"App\\Providers\\DoctrineServiceProvider","editor":{"file":"/web/app/Providers/DoctrineServiceProvider.php","line":10,"url":"editor://open/?file=%2Fweb%2Fapp%2FProviders%2FDoctrineServiceProvider.php\u0026line=10\u0026search=\u0026replace="},"items":[["app",{"ref":2},1],["bootingCallbacks",[],1],["bootedCallbacks",[],1]]},"129":{"object":"Illuminate\\Broadcasting\\BroadcastServiceProvider","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Broadcasting/BroadcastServiceProvider.php","line":10,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FBroadcasting%2FBroadcastServiceProvider.php\u0026line=10\u0026search=\u0026replace="},"items":[["app",{"ref":2},1],["bootingCallbacks",[],1],["bootedCallbacks",[],1]]},"253":{"object":"Illuminate\\Bus\\BusServiceProvider","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Bus/BusServiceProvider.php","line":11,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FBus%2FBusServiceProvider.php\u0026line=11\u0026search=\u0026replace="},"items":[["app",{"ref":2},1],["bootingCallbacks",[],1],["bootedCallbacks",[],1]]},"258":{"object":"Illuminate\\Foundation\\Providers\\ArtisanServiceProvider","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Foundation/Providers/ArtisanServiceProvider.php","line":86,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2FProviders%2FArtisanServiceProvider.php\u0026line=86\u0026search=\u0026replace="},"items":[["commands",[["CacheClear","Illuminate\\Cache\\Console\\ClearCommand"],["CacheForget","Illuminate\\Cache\\Console\\ForgetCommand"],["ClearCompiled","Illuminate\\Foundation\\Console\\ClearCompiledCommand"],["ClearResets","Illuminate\\Auth\\Console\\ClearResetsCommand"],["ConfigCache","Illuminate\\Foundation\\Console\\ConfigCacheCommand"],["ConfigClear","Illuminate\\Foundation\\Console\\ConfigClearCommand"],["Db","Illuminate\\Database\\Console\\DbCommand"],["DbPrune","Illuminate\\Database\\Console\\PruneCommand"],["DbWipe","Illuminate\\Database\\Console\\WipeCommand"],["Down","Illuminate\\Foundation\\Console\\DownCommand"],["Environment","Illuminate\\Foundation\\Console\\EnvironmentCommand"],["EventCache","Illuminate\\Foundation\\Console\\EventCacheCommand"],["EventClear","Illuminate\\Foundation\\Console\\EventClearCommand"],["EventList","Illuminate\\Foundation\\Console\\EventListCommand"],["KeyGenerate","Illuminate\\Foundation\\Console\\KeyGenerateCommand"],["Optimize","Illuminate\\Foundation\\Console\\OptimizeCommand"],["OptimizeClear","Illuminate\\Foundation\\Console\\OptimizeClearCommand"],["PackageDiscover","Illuminate\\Foundation\\Console\\PackageDiscoverCommand"],["QueueClear","Illuminate\\Queue\\Console\\ClearCommand"],["QueueFailed","Illuminate\\Queue\\Console\\ListFailedCommand"],["QueueFlush","Illuminate\\Queue\\Console\\FlushFailedCommand"],["QueueForget","Illuminate\\Queue\\Console\\ForgetFailedCommand"],["QueueListen","Illuminate\\Queue\\Console\\ListenCommand"],["QueueMonitor","Illuminate\\Queue\\Console\\MonitorCommand"],["QueuePruneBatches","Illuminate\\Queue\\Console\\PruneBatchesCommand"],["QueuePruneFailedJobs","Illuminate\\Queue\\Console\\PruneFailedJobsCommand"],["QueueRestart","Illuminate\\Queue\\Console\\RestartCommand"],["QueueRetry","Illuminate\\Queue\\Console\\RetryCommand"],["QueueRetryBatch","Illuminate\\Queue\\Console\\RetryBatchCommand"],["QueueWork","Illuminate\\Queue\\Console\\WorkCommand"],["RouteCache","Illuminate\\Foundation\\Console\\RouteCacheCommand"],["RouteClear","Illuminate\\Foundation\\Console\\RouteClearCommand"],["RouteList","Illuminate\\Foundation\\Console\\RouteListCommand"],["SchemaDump","Illuminate\\Database\\Console\\DumpCommand"],["Seed","Illuminate\\Database\\Console\\Seeds\\SeedCommand"],["ScheduleFinish","Illuminate\\Console\\Scheduling\\ScheduleFinishCommand"],["ScheduleList","Illuminate\\Console\\Scheduling\\ScheduleListCommand"],["ScheduleRun","Illuminate\\Console\\Scheduling\\ScheduleRunCommand"],["ScheduleClearCache","Illuminate\\Console\\Scheduling\\ScheduleClearCacheCommand"],["ScheduleTest","Illuminate\\Console\\Scheduling\\ScheduleTestCommand"],["ScheduleWork","Illuminate\\Console\\Scheduling\\ScheduleWorkCommand"],["StorageLink","Illuminate\\Foundation\\Console\\StorageLinkCommand"],["Up","Illuminate\\Foundation\\Console\\UpCommand"],["ViewCache","Illuminate\\Foundation\\Console\\ViewCacheCommand"],["ViewClear","Illuminate\\Foundation\\Console\\ViewClearCommand"]],1],["devCommands",[["CacheTable","Illuminate\\Cache\\Console\\CacheTableCommand"],["CastMake","Illuminate\\Foundation\\Console\\CastMakeCommand"],["ChannelMake","Illuminate\\Foundation\\Console\\ChannelMakeCommand"],["ComponentMake","Illuminate\\Foundation\\Console\\ComponentMakeCommand"],["ConsoleMake","Illuminate\\Foundation\\Console\\ConsoleMakeCommand"],["ControllerMake","Illuminate\\Routing\\Console\\ControllerMakeCommand"],["EventGenerate","Illuminate\\Foundation\\Console\\EventGenerateCommand"],["EventMake","Illuminate\\Foundation\\Console\\EventMakeCommand"],["ExceptionMake","Illuminate\\Foundation\\Console\\ExceptionMakeCommand"],["FactoryMake","Illuminate\\Database\\Console\\Factories\\FactoryMakeCommand"],["JobMake","Illuminate\\Foundation\\Console\\JobMakeCommand"],["ListenerMake","Illuminate\\Foundation\\Console\\ListenerMakeCommand"],["MailMake","Illuminate\\Foundation\\Console\\MailMakeCommand"],["MiddlewareMake","Illuminate\\Routing\\Console\\MiddlewareMakeCommand"],["ModelMake","Illuminate\\Foundation\\Console\\ModelMakeCommand"],["NotificationMake","Illuminate\\Foundation\\Console\\NotificationMakeCommand"],["NotificationTable","Illuminate\\Notifications\\Console\\NotificationTableCommand"],["ObserverMake","Illuminate\\Foundation\\Console\\ObserverMakeCommand"],["PolicyMake","Illuminate\\Foundation\\Console\\PolicyMakeCommand"],["ProviderMake","Illuminate\\Foundation\\Console\\ProviderMakeCommand"],["QueueFailedTable","Illuminate\\Queue\\Console\\FailedTableCommand"],["QueueTable","Illuminate\\Queue\\Console\\TableCommand"],["QueueBatchesTable","Illuminate\\Queue\\Console\\BatchesTableCommand"],["RequestMake","Illuminate\\Foundation\\Console\\RequestMakeCommand"],["ResourceMake","Illuminate\\Foundation\\Console\\ResourceMakeCommand"],["RuleMake","Illuminate\\Foundation\\Console\\RuleMakeCommand"],["ScopeMake","Illuminate\\Foundation\\Console\\ScopeMakeCommand"],["SeederMake","Illuminate\\Database\\Console\\Seeds\\SeederMakeCommand"],["SessionTable","Illuminate\\Session\\Console\\SessionTableCommand"],["Serve","Illuminate\\Foundation\\Console\\ServeCommand"],["StubPublish","Illuminate\\Foundation\\Console\\StubPublishCommand"],["TestMake","Illuminate\\Foundation\\Console\\TestMakeCommand"],["VendorPublish","Illuminate\\Foundation\\Console\\VendorPublishCommand"]],1],["app",{"ref":2},1],["bootingCallbacks",[],1],["bootedCallbacks",[],1]]},"338":{"object":"Illuminate\\Database\\MigrationServiceProvider","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Database/MigrationServiceProvider.php","line":20,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FDatabase%2FMigrationServiceProvider.php\u0026line=20\u0026search=\u0026replace="},"items":[["commands",[["Migrate","Illuminate\\Database\\Console\\Migrations\\MigrateCommand"],["MigrateFresh","Illuminate\\Database\\Console\\Migrations\\FreshCommand"],["MigrateInstall","Illuminate\\Database\\Console\\Migrations\\InstallCommand"],["MigrateRefresh","Illuminate\\Database\\Console\\Migrations\\RefreshCommand"],["MigrateReset","Illuminate\\Database\\Console\\Migrations\\ResetCommand"],["MigrateRollback","Illuminate\\Database\\Console\\Migrations\\RollbackCommand"],["MigrateStatus","Illuminate\\Database\\Console\\Migrations\\StatusCommand"],["MigrateMake","Illuminate\\Database\\Console\\Migrations\\MigrateMakeCommand"]],1],["app",{"ref":2},1],["bootingCallbacks",[],1],["bootedCallbacks",[],1]]},"351":{"object":"Illuminate\\Foundation\\Providers\\ComposerServiceProvider","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Foundation/Providers/ComposerServiceProvider.php","line":9,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2FProviders%2FComposerServiceProvider.php\u0026line=9\u0026search=\u0026replace="},"items":[["app",{"ref":2},1],["bootingCallbacks",[],1],["bootedCallbacks",[],1]]},"257":{"object":"Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Foundation/Providers/ConsoleSupportServiceProvider.php","line":9,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2FProviders%2FConsoleSupportServiceProvider.php\u0026line=9\u0026search=\u0026replace="},"items":[["providers",[[0,"Illuminate\\Foundation\\Providers\\ArtisanServiceProvider"],[1,"Illuminate\\Database\\MigrationServiceProvider"],[2,"Illuminate\\Foundation\\Providers\\ComposerServiceProvider"]],1],["instances",[[0,{"ref":258}],[1,{"ref":338}],[2,{"ref":351}]],1],["app",{"ref":2},1],["bootingCallbacks",[],1],["bootedCallbacks",[],1]]},"353":{"object":"Illuminate\\Hashing\\HashServiceProvider","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Hashing/HashServiceProvider.php","line":8,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FHashing%2FHashServiceProvider.php\u0026line=8\u0026search=\u0026replace="},"items":[["app",{"ref":2},1],["bootingCallbacks",[],1],["bootedCallbacks",[],1]]},"356":{"object":"Illuminate\\Mail\\MailServiceProvider","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Mail/MailServiceProvider.php","line":8,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FMail%2FMailServiceProvider.php\u0026line=8\u0026search=\u0026replace="},"items":[["app",{"ref":2},1],["bootingCallbacks",[],1],["bootedCallbacks",[],1]]},"360":{"object":"Illuminate\\Pipeline\\PipelineServiceProvider","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Pipeline/PipelineServiceProvider.php","line":9,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FPipeline%2FPipelineServiceProvider.php\u0026line=9\u0026search=\u0026replace="},"items":[["app",{"ref":2},1],["bootingCallbacks",[],1],["bootedCallbacks",[],1]]},"362":{"object":"Illuminate\\Redis\\RedisServiceProvider","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Redis/RedisServiceProvider.php","line":9,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FRedis%2FRedisServiceProvider.php\u0026line=9\u0026search=\u0026replace="},"items":[["app",{"ref":2},1],["bootingCallbacks",[],1],["bootedCallbacks",[],1]]},"365":{"object":"Illuminate\\Auth\\Passwords\\PasswordResetServiceProvider","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Auth/Passwords/PasswordResetServiceProvider.php","line":8,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FPasswords%2FPasswordResetServiceProvider.php\u0026line=8\u0026search=\u0026replace="},"items":[["app",{"ref":2},1],["bootingCallbacks",[],1],["bootedCallbacks",[],1]]},"368":{"object":"Illuminate\\Translation\\TranslationServiceProvider","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Translation/TranslationServiceProvider.php","line":8,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FTranslation%2FTranslationServiceProvider.php\u0026line=8\u0026search=\u0026replace="},"items":[["app",{"ref":2},1],["bootingCallbacks",[],1],["bootedCallbacks",[],1]]},"371":{"object":"Illuminate\\Validation\\ValidationServiceProvider","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Validation/ValidationServiceProvider.php","line":10,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FValidation%2FValidationServiceProvider.php\u0026line=10\u0026search=\u0026replace="},"items":[["app",{"ref":2},1],["bootingCallbacks",[],1],["bootedCallbacks",[],1]]},"375":{"object":"Laravel\\Sail\\SailServiceProvider","editor":{"file":"/web/vendor/laravel/sail/src/SailServiceProvider.php","line":10,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fsail%2Fsrc%2FSailServiceProvider.php\u0026line=10\u0026search=\u0026replace="},"items":[["app",{"ref":2},1],["bootingCallbacks",[],1],["bootedCallbacks",[],1]]},"376":{"object":"Laravel\\Tinker\\TinkerServiceProvider","editor":{"file":"/web/vendor/laravel/tinker/src/TinkerServiceProvider.php","line":11,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Ftinker%2Fsrc%2FTinkerServiceProvider.php\u0026line=11\u0026search=\u0026replace="},"items":[["app",{"ref":2},1],["bootingCallbacks",[],1],["bootedCallbacks",[],1]]},"4":{"object":"Closure($container, $parameters)","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Container/Container.php","line":290,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FContainer%2FContainer.php\u0026line=290\u0026search=\u0026replace="},"items":[["file","/web/vendor/laravel/framework/src/Illuminate/Container/Container.php:290",4],["use",{"object":"$abstract, $concrete","items":[["$abstract","Illuminate\\Foundation\\Mix",4],["$concrete","Illuminate\\Foundation\\Mix",4]],"collapsed":true}]]},"5":{"object":"Closure()","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Foundation/Application.php","line":207,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2FApplication.php\u0026line=207\u0026search=\u0026replace="},"items":[["file","/web/vendor/laravel/framework/src/Illuminate/Foundation/Application.php:207",4]]},"7":{"object":"Closure($app)","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Events/EventServiceProvider.php","line":17,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FEvents%2FEventServiceProvider.php\u0026line=17\u0026search=\u0026replace="},"items":[["file","/web/vendor/laravel/framework/src/Illuminate/Events/EventServiceProvider.php:17",4]]},"9":{"object":"Closure($app)","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Log/LogServiceProvider.php","line":16,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FLog%2FLogServiceProvider.php\u0026line=16\u0026search=\u0026replace="},"items":[["file","/web/vendor/laravel/framework/src/Illuminate/Log/LogServiceProvider.php:16",4]]},"11":{"object":"Closure($app)","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Routing/RoutingServiceProvider.php","line":42,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FRouting%2FRoutingServiceProvider.php\u0026line=42\u0026search=\u0026replace="},"items":[["file","/web/vendor/laravel/framework/src/Illuminate/Routing/RoutingServiceProvider.php:42",4]]},"12":{"object":"Closure($app)","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Routing/RoutingServiceProvider.php","line":54,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FRouting%2FRoutingServiceProvider.php\u0026line=54\u0026search=\u0026replace="},"items":[["file","/web/vendor/laravel/framework/src/Illuminate/Routing/RoutingServiceProvider.php:54",4]]},"14":{"object":"Closure($app)","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Routing/RoutingServiceProvider.php","line":111,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FRouting%2FRoutingServiceProvider.php\u0026line=111\u0026search=\u0026replace="},"items":[["file","/web/vendor/laravel/framework/src/Illuminate/Routing/RoutingServiceProvider.php:111",4]]},"15":{"object":"Closure($app)","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Routing/RoutingServiceProvider.php","line":134,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FRouting%2FRoutingServiceProvider.php\u0026line=134\u0026search=\u0026replace="},"items":[["file","/web/vendor/laravel/framework/src/Illuminate/Routing/RoutingServiceProvider.php:134",4]]},"16":{"object":"Closure()","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Routing/RoutingServiceProvider.php","line":155,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FRouting%2FRoutingServiceProvider.php\u0026line=155\u0026search=\u0026replace="},"items":[["file","/web/vendor/laravel/framework/src/Illuminate/Routing/RoutingServiceProvider.php:155",4]]},"17":{"object":"Closure($app)","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Routing/RoutingServiceProvider.php","line":171,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FRouting%2FRoutingServiceProvider.php\u0026line=171\u0026search=\u0026replace="},"items":[["file","/web/vendor/laravel/framework/src/Illuminate/Routing/RoutingServiceProvider.php:171",4]]},"18":{"object":"Closure($app)","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Routing/RoutingServiceProvider.php","line":183,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FRouting%2FRoutingServiceProvider.php\u0026line=183\u0026search=\u0026replace="},"items":[["file","/web/vendor/laravel/framework/src/Illuminate/Routing/RoutingServiceProvider.php:183",4]]},"19":{"object":"Closure($container, $parameters)","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Container/Container.php","line":290,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FContainer%2FContainer.php\u0026line=290\u0026search=\u0026replace="},"items":[["file","/web/vendor/laravel/framework/src/Illuminate/Container/Container.php:290",4],["use",{"object":"$abstract, $concrete","items":[["$abstract","Illuminate\\Contracts\\Http\\Kernel",4],["$concrete","App\\Http\\Kernel",4]],"collapsed":true}]]},"20":{"object":"Closure($container, $parameters)","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Container/Container.php","line":290,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FContainer%2FContainer.php\u0026line=290\u0026search=\u0026replace="},"items":[["file","/web/vendor/laravel/framework/src/Illuminate/Container/Container.php:290",4],["use",{"object":"$abstract, $concrete","items":[["$abstract","Illuminate\\Contracts\\Console\\Kernel",4],["$concrete","App\\Console\\Kernel",4]],"collapsed":true}]]},"64":{"object":"Closure($app)","editor":{"file":"/web/vendor/nunomaduro/collision/src/Adapters/Laravel/CollisionServiceProvider.php","line":69,"url":"editor://open/?file=%2Fweb%2Fvendor%2Fnunomaduro%2Fcollision%2Fsrc%2FAdapters%2FLaravel%2FCollisionServiceProvider.php\u0026line=69\u0026search=\u0026replace="},"items":[["file","/web/vendor/nunomaduro/collision/src/Adapters/Laravel/CollisionServiceProvider.php:69",4],["use",{"object":"$appExceptionHandler","items":[["$appExceptionHandler",{"ref":63},4]],"collapsed":true}]]},"63":{"object":"App\\Exceptions\\Handler","editor":{"file":"/web/app/Exceptions/Handler.php","line":9,"url":"editor://open/?file=%2Fweb%2Fapp%2FExceptions%2FHandler.php\u0026line=9\u0026search=\u0026replace="},"items":[["dontReport",{"array":null,"length":1},1],["dontFlash",{"array":null,"length":3},1],["container",{"ref":2},1],["reportCallbacks",{"array":null,"length":1},1],["renderCallbacks",[],1],["exceptionMap",[],1],["internalDontReport",{"array":null,"length":11},1]]},"22":{"object":"Closure($container, $parameters)","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Container/Container.php","line":290,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FContainer%2FContainer.php\u0026line=290\u0026search=\u0026replace="},"items":[["file","/web/vendor/laravel/framework/src/Illuminate/Container/Container.php:290",4],["use",{"object":"$abstract, $concrete","items":[["$abstract","Illuminate\\Foundation\\Bootstrap\\HandleExceptions",4],["$concrete","DekApps\\LaravelTracy\\ResetHandleExceptions",4]],"collapsed":true}]]},"122":{"object":"Closure()","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Container/Container.php","line":1429,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FContainer%2FContainer.php\u0026line=1429\u0026search=\u0026replace="},"items":[["file","/web/vendor/laravel/framework/src/Illuminate/Container/Container.php:1429",4],["use",{"object":"$value","items":[["$value","local",4]],"collapsed":true}]]},"124":{"object":"Closure($app)","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Auth/AuthServiceProvider.php","line":37,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FAuthServiceProvider.php\u0026line=37\u0026search=\u0026replace="},"items":[["file","/web/vendor/laravel/framework/src/Illuminate/Auth/AuthServiceProvider.php:37",4]]},"54":{"object":"Closure($app)","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Auth/AuthServiceProvider.php","line":41,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FAuthServiceProvider.php\u0026line=41\u0026search=\u0026replace="},"items":[["file","/web/vendor/laravel/framework/src/Illuminate/Auth/AuthServiceProvider.php:41",4]]},"121":{"object":"Closure($app)","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Auth/AuthServiceProvider.php","line":53,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FAuthServiceProvider.php\u0026line=53\u0026search=\u0026replace="},"items":[["file","/web/vendor/laravel/framework/src/Illuminate/Auth/AuthServiceProvider.php:53",4]]},"47":{"object":"Closure($app)","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Auth/AuthServiceProvider.php","line":65,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FAuthServiceProvider.php\u0026line=65\u0026search=\u0026replace="},"items":[["file","/web/vendor/laravel/framework/src/Illuminate/Auth/AuthServiceProvider.php:65",4]]},"57":{"object":"Closure($app)","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Auth/AuthServiceProvider.php","line":79,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FAuthServiceProvider.php\u0026line=79\u0026search=\u0026replace="},"items":[["file","/web/vendor/laravel/framework/src/Illuminate/Auth/AuthServiceProvider.php:79",4]]},"117":{"object":"Closure($app)","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Cookie/CookieServiceProvider.php","line":16,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCookie%2FCookieServiceProvider.php\u0026line=16\u0026search=\u0026replace="},"items":[["file","/web/vendor/laravel/framework/src/Illuminate/Cookie/CookieServiceProvider.php:16",4]]},"115":{"object":"Closure($app)","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Database/DatabaseServiceProvider.php","line":58,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FDatabase%2FDatabaseServiceProvider.php\u0026line=58\u0026search=\u0026replace="},"items":[["file","/web/vendor/laravel/framework/src/Illuminate/Database/DatabaseServiceProvider.php:58",4]]},"114":{"object":"Closure($app)","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Database/DatabaseServiceProvider.php","line":65,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FDatabase%2FDatabaseServiceProvider.php\u0026line=65\u0026search=\u0026replace="},"items":[["file","/web/vendor/laravel/framework/src/Illuminate/Database/DatabaseServiceProvider.php:65",4]]},"113":{"object":"Closure($app)","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Database/DatabaseServiceProvider.php","line":69,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FDatabase%2FDatabaseServiceProvider.php\u0026line=69\u0026search=\u0026replace="},"items":[["file","/web/vendor/laravel/framework/src/Illuminate/Database/DatabaseServiceProvider.php:69",4]]},"112":{"object":"Closure($app)","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Database/DatabaseServiceProvider.php","line":73,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FDatabase%2FDatabaseServiceProvider.php\u0026line=73\u0026search=\u0026replace="},"items":[["file","/web/vendor/laravel/framework/src/Illuminate/Database/DatabaseServiceProvider.php:73",4]]},"111":{"object":"Closure($app)","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Database/DatabaseServiceProvider.php","line":77,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FDatabase%2FDatabaseServiceProvider.php\u0026line=77\u0026search=\u0026replace="},"items":[["file","/web/vendor/laravel/framework/src/Illuminate/Database/DatabaseServiceProvider.php:77",4]]},"110":{"object":"Closure($app, $parameters)","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Database/DatabaseServiceProvider.php","line":89,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FDatabase%2FDatabaseServiceProvider.php\u0026line=89\u0026search=\u0026replace="},"items":[["file","/web/vendor/laravel/framework/src/Illuminate/Database/DatabaseServiceProvider.php:89",4]]},"109":{"object":"Closure()","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Database/DatabaseServiceProvider.php","line":109,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FDatabase%2FDatabaseServiceProvider.php\u0026line=109\u0026search=\u0026replace="},"items":[["file","/web/vendor/laravel/framework/src/Illuminate/Database/DatabaseServiceProvider.php:109",4]]},"107":{"object":"Closure($app)","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php","line":29,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FEncryption%2FEncryptionServiceProvider.php\u0026line=29\u0026search=\u0026replace="},"items":[["file","/web/vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php:29",4]]},"104":{"object":"Closure()","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Filesystem/FilesystemServiceProvider.php","line":28,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFilesystem%2FFilesystemServiceProvider.php\u0026line=28\u0026search=\u0026replace="},"items":[["file","/web/vendor/laravel/framework/src/Illuminate/Filesystem/FilesystemServiceProvider.php:28",4]]},"103":{"object":"Closure($app)","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Filesystem/FilesystemServiceProvider.php","line":58,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFilesystem%2FFilesystemServiceProvider.php\u0026line=58\u0026search=\u0026replace="},"items":[["file","/web/vendor/laravel/framework/src/Illuminate/Filesystem/FilesystemServiceProvider.php:58",4]]},"102":{"object":"Closure($app)","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Filesystem/FilesystemServiceProvider.php","line":42,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFilesystem%2FFilesystemServiceProvider.php\u0026line=42\u0026search=\u0026replace="},"items":[["file","/web/vendor/laravel/framework/src/Illuminate/Filesystem/FilesystemServiceProvider.php:42",4]]},"101":{"object":"Closure($app)","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Filesystem/FilesystemServiceProvider.php","line":46,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFilesystem%2FFilesystemServiceProvider.php\u0026line=46\u0026search=\u0026replace="},"items":[["file","/web/vendor/laravel/framework/src/Illuminate/Filesystem/FilesystemServiceProvider.php:46",4]]},"97":{"object":"Closure()","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Testing/ParallelTestingServiceProvider.php","line":33,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FTesting%2FParallelTestingServiceProvider.php\u0026line=33\u0026search=\u0026replace="},"items":[["file","/web/vendor/laravel/framework/src/Illuminate/Testing/ParallelTestingServiceProvider.php:33",4]]},"91":{"object":"Closure($container, $parameters)","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Container/Container.php","line":290,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FContainer%2FContainer.php\u0026line=290\u0026search=\u0026replace="},"items":[["file","/web/vendor/laravel/framework/src/Illuminate/Container/Container.php:290",4],["use",{"object":"$abstract, $concrete","items":[["$abstract","Illuminate\\Foundation\\MaintenanceModeManager",4],["$concrete","Illuminate\\Foundation\\MaintenanceModeManager",4]],"collapsed":true}]]},"90":{"object":"Closure()","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Foundation/Providers/FoundationServiceProvider.php","line":135,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2FProviders%2FFoundationServiceProvider.php\u0026line=135\u0026search=\u0026replace="},"items":[["file","/web/vendor/laravel/framework/src/Illuminate/Foundation/Providers/FoundationServiceProvider.php:135",4]]},"88":{"object":"Closure($app)","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Notifications/NotificationServiceProvider.php","line":34,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FNotifications%2FNotificationServiceProvider.php\u0026line=34\u0026search=\u0026replace="},"items":[["file","/web/vendor/laravel/framework/src/Illuminate/Notifications/NotificationServiceProvider.php:34",4]]},"80":{"object":"Closure($app)","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Session/SessionServiceProvider.php","line":36,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FSessionServiceProvider.php\u0026line=36\u0026search=\u0026replace="},"items":[["file","/web/vendor/laravel/framework/src/Illuminate/Session/SessionServiceProvider.php:36",4]]},"79":{"object":"Closure($app)","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Session/SessionServiceProvider.php","line":48,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FSessionServiceProvider.php\u0026line=48\u0026search=\u0026replace="},"items":[["file","/web/vendor/laravel/framework/src/Illuminate/Session/SessionServiceProvider.php:48",4]]},"78":{"object":"Closure($app)","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Session/SessionServiceProvider.php","line":22,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FSessionServiceProvider.php\u0026line=22\u0026search=\u0026replace="},"items":[["file","/web/vendor/laravel/framework/src/Illuminate/Session/SessionServiceProvider.php:22",4]]},"76":{"object":"Closure($app)","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/View/ViewServiceProvider.php","line":34,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FView%2FViewServiceProvider.php\u0026line=34\u0026search=\u0026replace="},"items":[["file","/web/vendor/laravel/framework/src/Illuminate/View/ViewServiceProvider.php:34",4]]},"75":{"object":"Closure($app)","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/View/ViewServiceProvider.php","line":75,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FView%2FViewServiceProvider.php\u0026line=75\u0026search=\u0026replace="},"items":[["file","/web/vendor/laravel/framework/src/Illuminate/View/ViewServiceProvider.php:75",4]]},"74":{"object":"Closure($app)","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/View/ViewServiceProvider.php","line":87,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FView%2FViewServiceProvider.php\u0026line=87\u0026search=\u0026replace="},"items":[["file","/web/vendor/laravel/framework/src/Illuminate/View/ViewServiceProvider.php:87",4]]},"73":{"object":"Closure()","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/View/ViewServiceProvider.php","line":105,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FView%2FViewServiceProvider.php\u0026line=105\u0026search=\u0026replace="},"items":[["file","/web/vendor/laravel/framework/src/Illuminate/View/ViewServiceProvider.php:105",4]]},"71":{"object":"Closure($app)","editor":{"file":"/web/vendor/fruitcake/laravel-cors/src/CorsServiceProvider.php","line":22,"url":"editor://open/?file=%2Fweb%2Fvendor%2Ffruitcake%2Flaravel-cors%2Fsrc%2FCorsServiceProvider.php\u0026line=22\u0026search=\u0026replace="},"items":[["file","/web/vendor/fruitcake/laravel-cors/src/CorsServiceProvider.php:22",4]]},"68":{"object":"Closure()","editor":{"file":"/web/vendor/nunomaduro/collision/src/Adapters/Laravel/CollisionServiceProvider.php","line":48,"url":"editor://open/?file=%2Fweb%2Fvendor%2Fnunomaduro%2Fcollision%2Fsrc%2FAdapters%2FLaravel%2FCollisionServiceProvider.php\u0026line=48\u0026search=\u0026replace="},"items":[["file","/web/vendor/nunomaduro/collision/src/Adapters/Laravel/CollisionServiceProvider.php:48",4]]},"60":{"object":"Closure()","editor":{"file":"/web/vendor/spatie/laravel-ignition/src/IgnitionServiceProvider.php","line":119,"url":"editor://open/?file=%2Fweb%2Fvendor%2Fspatie%2Flaravel-ignition%2Fsrc%2FIgnitionServiceProvider.php\u0026line=119\u0026search=\u0026replace="},"items":[["file","/web/vendor/spatie/laravel-ignition/src/IgnitionServiceProvider.php:119",4]]},"59":{"object":"Closure($container, $parameters)","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Container/Container.php","line":290,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FContainer%2FContainer.php\u0026line=290\u0026search=\u0026replace="},"items":[["file","/web/vendor/laravel/framework/src/Illuminate/Container/Container.php:290",4],["use",{"object":"$abstract, $concrete","items":[["$abstract","Spatie\\LaravelIgnition\\Support\\SentReports",4],["$concrete","Spatie\\LaravelIgnition\\Support\\SentReports",4]],"collapsed":true}]]},"58":{"object":"Closure()","editor":{"file":"/web/vendor/spatie/laravel-ignition/src/IgnitionServiceProvider.php","line":140,"url":"editor://open/?file=%2Fweb%2Fvendor%2Fspatie%2Flaravel-ignition%2Fsrc%2FIgnitionServiceProvider.php\u0026line=140\u0026search=\u0026replace="},"items":[["file","/web/vendor/spatie/laravel-ignition/src/IgnitionServiceProvider.php:140",4],["use",{"object":"$ignitionConfig","items":[["$ignitionConfig",{"ref":66},4]],"collapsed":true}]]},"66":{"object":"Spatie\\Ignition\\Config\\IgnitionConfig","editor":{"file":"/web/vendor/spatie/ignition/src/Config/IgnitionConfig.php","line":8,"url":"editor://open/?file=%2Fweb%2Fvendor%2Fspatie%2Fignition%2Fsrc%2FConfig%2FIgnitionConfig.php\u0026line=8\u0026search=\u0026replace="}},"55":{"object":"Closure()","editor":{"file":"/web/vendor/spatie/laravel-ignition/src/IgnitionServiceProvider.php","line":142,"url":"editor://open/?file=%2Fweb%2Fvendor%2Fspatie%2Flaravel-ignition%2Fsrc%2FIgnitionServiceProvider.php\u0026line=142\u0026search=\u0026replace="},"items":[["file","/web/vendor/spatie/laravel-ignition/src/IgnitionServiceProvider.php:142",4],["use",{"object":"$solutionProviderRepository","items":[["$solutionProviderRepository",{"ref":45},4]],"collapsed":true}]]},"45":{"object":"Spatie\\LaravelIgnition\\Solutions\\SolutionProviders\\SolutionProviderRepository","editor":{"file":"/web/vendor/spatie/laravel-ignition/src/Solutions/SolutionProviders/SolutionProviderRepository.php","line":12,"url":"editor://open/?file=%2Fweb%2Fvendor%2Fspatie%2Flaravel-ignition%2Fsrc%2FSolutions%2FSolutionProviders%2FSolutionProviderRepository.php\u0026line=12\u0026search=\u0026replace="}},"44":{"object":"Closure()","editor":{"file":"/web/vendor/spatie/laravel-ignition/src/IgnitionServiceProvider.php","line":144,"url":"editor://open/?file=%2Fweb%2Fvendor%2Fspatie%2Flaravel-ignition%2Fsrc%2FIgnitionServiceProvider.php\u0026line=144\u0026search=\u0026replace="},"items":[["file","/web/vendor/spatie/laravel-ignition/src/IgnitionServiceProvider.php:144",4]]},"37":{"object":"Closure($app)","editor":{"file":"/web/vendor/spatie/laravel-ignition/src/IgnitionServiceProvider.php","line":104,"url":"editor://open/?file=%2Fweb%2Fvendor%2Fspatie%2Flaravel-ignition%2Fsrc%2FIgnitionServiceProvider.php\u0026line=104\u0026search=\u0026replace="},"items":[["file","/web/vendor/spatie/laravel-ignition/src/IgnitionServiceProvider.php:104",4]]},"41":{"object":"Closure($app)","editor":{"file":"/web/vendor/spatie/laravel-ignition/src/IgnitionServiceProvider.php","line":112,"url":"editor://open/?file=%2Fweb%2Fvendor%2Fspatie%2Flaravel-ignition%2Fsrc%2FIgnitionServiceProvider.php\u0026line=112\u0026search=\u0026replace="},"items":[["file","/web/vendor/spatie/laravel-ignition/src/IgnitionServiceProvider.php:112",4]]},"42":{"object":"Closure($container, $parameters)","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Container/Container.php","line":290,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FContainer%2FContainer.php\u0026line=290\u0026search=\u0026replace="},"items":[["file","/web/vendor/laravel/framework/src/Illuminate/Container/Container.php:290",4],["use",{"object":"$abstract, $concrete","items":[["$abstract","Spatie\\LaravelIgnition\\Recorders\\DumpRecorder\\DumpRecorder",4],["$concrete","Spatie\\LaravelIgnition\\Recorders\\DumpRecorder\\DumpRecorder",4]],"collapsed":true}]]},"43":{"object":"Closure($app)","editor":{"file":"/web/vendor/spatie/laravel-ignition/src/IgnitionServiceProvider.php","line":151,"url":"editor://open/?file=%2Fweb%2Fvendor%2Fspatie%2Flaravel-ignition%2Fsrc%2FIgnitionServiceProvider.php\u0026line=151\u0026search=\u0026replace="},"items":[["file","/web/vendor/spatie/laravel-ignition/src/IgnitionServiceProvider.php:151",4]]},"132":{"object":"Closure($app)","editor":{"file":"/web/vendor/spatie/laravel-ignition/src/IgnitionServiceProvider.php","line":160,"url":"editor://open/?file=%2Fweb%2Fvendor%2Fspatie%2Flaravel-ignition%2Fsrc%2FIgnitionServiceProvider.php\u0026line=160\u0026search=\u0026replace="},"items":[["file","/web/vendor/spatie/laravel-ignition/src/IgnitionServiceProvider.php:160",4]]},"46":{"object":"Closure($app)","editor":{"file":"/web/vendor/spatie/laravel-ignition/src/IgnitionServiceProvider.php","line":169,"url":"editor://open/?file=%2Fweb%2Fvendor%2Fspatie%2Flaravel-ignition%2Fsrc%2FIgnitionServiceProvider.php\u0026line=169\u0026search=\u0026replace="},"items":[["file","/web/vendor/spatie/laravel-ignition/src/IgnitionServiceProvider.php:169",4]]},"130":{"object":"Closure($app)","editor":{"file":"/web/vendor/spatie/laravel-ignition/src/IgnitionServiceProvider.php","line":224,"url":"editor://open/?file=%2Fweb%2Fvendor%2Fspatie%2Flaravel-ignition%2Fsrc%2FIgnitionServiceProvider.php\u0026line=224\u0026search=\u0026replace="},"items":[["file","/web/vendor/spatie/laravel-ignition/src/IgnitionServiceProvider.php:224",4]]},"52":{"object":"Closure($app)","editor":{"file":"/web/vendor/dek-apps/laravel-latte/src/LaravelLatte/ServiceProvider.php","line":17,"url":"editor://open/?file=%2Fweb%2Fvendor%2Fdek-apps%2Flaravel-latte%2Fsrc%2FLaravelLatte%2FServiceProvider.php\u0026line=17\u0026search=\u0026replace="},"items":[["file","/web/vendor/dek-apps/laravel-latte/src/LaravelLatte/ServiceProvider.php:17",4]]},"173":{"object":"Closure($app)","editor":{"file":"/web/app/Providers/DoctrineServiceProvider.php","line":19,"url":"editor://open/?file=%2Fweb%2Fapp%2FProviders%2FDoctrineServiceProvider.php\u0026line=19\u0026search=\u0026replace="},"items":[["file","/web/app/Providers/DoctrineServiceProvider.php:19",4]]},"202":{"object":"Closure()","editor":{"file":"/web/vendor/spatie/laravel-ignition/src/Recorders/DumpRecorder/DumpRecorder.php","line":30,"url":"editor://open/?file=%2Fweb%2Fvendor%2Fspatie%2Flaravel-ignition%2Fsrc%2FRecorders%2FDumpRecorder%2FDumpRecorder.php\u0026line=30\u0026search=\u0026replace="},"items":[["file","/web/vendor/spatie/laravel-ignition/src/Recorders/DumpRecorder/DumpRecorder.php:30",4],["use",{"object":"$multiDumpHandler","items":[["$multiDumpHandler",{"ref":203},4]],"collapsed":true}]]},"203":{"object":"Spatie\\LaravelIgnition\\Recorders\\DumpRecorder\\MultiDumpHandler","editor":{"file":"/web/vendor/spatie/laravel-ignition/src/Recorders/DumpRecorder/MultiDumpHandler.php","line":5,"url":"editor://open/?file=%2Fweb%2Fvendor%2Fspatie%2Flaravel-ignition%2Fsrc%2FRecorders%2FDumpRecorder%2FMultiDumpHandler.php\u0026line=5\u0026search=\u0026replace="}},"217":{"object":"Closure($app)","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Queue/QueueServiceProvider.php","line":73,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FQueue%2FQueueServiceProvider.php\u0026line=73\u0026search=\u0026replace="},"items":[["file","/web/vendor/laravel/framework/src/Illuminate/Queue/QueueServiceProvider.php:73",4]]},"218":{"object":"Closure($app)","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Queue/QueueServiceProvider.php","line":90,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FQueue%2FQueueServiceProvider.php\u0026line=90\u0026search=\u0026replace="},"items":[["file","/web/vendor/laravel/framework/src/Illuminate/Queue/QueueServiceProvider.php:90",4]]},"219":{"object":"Closure($app)","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Queue/QueueServiceProvider.php","line":193,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FQueue%2FQueueServiceProvider.php\u0026line=193\u0026search=\u0026replace="},"items":[["file","/web/vendor/laravel/framework/src/Illuminate/Queue/QueueServiceProvider.php:193",4]]},"220":{"object":"Closure($app)","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Queue/QueueServiceProvider.php","line":223,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FQueue%2FQueueServiceProvider.php\u0026line=223\u0026search=\u0026replace="},"items":[["file","/web/vendor/laravel/framework/src/Illuminate/Queue/QueueServiceProvider.php:223",4]]},"221":{"object":"Closure($app)","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Queue/QueueServiceProvider.php","line":235,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FQueue%2FQueueServiceProvider.php\u0026line=235\u0026search=\u0026replace="},"items":[["file","/web/vendor/laravel/framework/src/Illuminate/Queue/QueueServiceProvider.php:235",4]]},"234":{"object":"Closure($app)","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Cache/CacheServiceProvider.php","line":18,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FCacheServiceProvider.php\u0026line=18\u0026search=\u0026replace="},"items":[["file","/web/vendor/laravel/framework/src/Illuminate/Cache/CacheServiceProvider.php:18",4]]},"235":{"object":"Closure($app)","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Cache/CacheServiceProvider.php","line":22,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FCacheServiceProvider.php\u0026line=22\u0026search=\u0026replace="},"items":[["file","/web/vendor/laravel/framework/src/Illuminate/Cache/CacheServiceProvider.php:22",4]]},"236":{"object":"Closure($app)","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Cache/CacheServiceProvider.php","line":26,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FCacheServiceProvider.php\u0026line=26\u0026search=\u0026replace="},"items":[["file","/web/vendor/laravel/framework/src/Illuminate/Cache/CacheServiceProvider.php:26",4]]},"237":{"object":"Closure()","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Cache/CacheServiceProvider.php","line":30,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FCacheServiceProvider.php\u0026line=30\u0026search=\u0026replace="},"items":[["file","/web/vendor/laravel/framework/src/Illuminate/Cache/CacheServiceProvider.php:30",4]]},"238":{"object":"Closure($app)","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Cache/CacheServiceProvider.php","line":34,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FCacheServiceProvider.php\u0026line=34\u0026search=\u0026replace="},"items":[["file","/web/vendor/laravel/framework/src/Illuminate/Cache/CacheServiceProvider.php:34",4]]},"123":{"object":"Closure($app)","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php","line":100,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2FConsole%2FKernel.php\u0026line=100\u0026search=\u0026replace="},"items":[["file","/web/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php:100",4]]},"128":{"object":"Closure($app)","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Broadcasting/BroadcastServiceProvider.php","line":19,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FBroadcasting%2FBroadcastServiceProvider.php\u0026line=19\u0026search=\u0026replace="},"items":[["file","/web/vendor/laravel/framework/src/Illuminate/Broadcasting/BroadcastServiceProvider.php:19",4]]},"246":{"object":"Closure($app)","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Broadcasting/BroadcastServiceProvider.php","line":23,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FBroadcasting%2FBroadcastServiceProvider.php\u0026line=23\u0026search=\u0026replace="},"items":[["file","/web/vendor/laravel/framework/src/Illuminate/Broadcasting/BroadcastServiceProvider.php:23",4]]},"254":{"object":"Closure($app)","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Bus/BusServiceProvider.php","line":20,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FBus%2FBusServiceProvider.php\u0026line=20\u0026search=\u0026replace="},"items":[["file","/web/vendor/laravel/framework/src/Illuminate/Bus/BusServiceProvider.php:20",4]]},"255":{"object":"Closure($container, $parameters)","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Container/Container.php","line":290,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FContainer%2FContainer.php\u0026line=290\u0026search=\u0026replace="},"items":[["file","/web/vendor/laravel/framework/src/Illuminate/Container/Container.php:290",4],["use",{"object":"$abstract, $concrete","items":[["$abstract","Illuminate\\Bus\\BatchRepository",4],["$concrete","Illuminate\\Bus\\DatabaseBatchRepository",4]],"collapsed":true}]]},"256":{"object":"Closure($app)","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Bus/BusServiceProvider.php","line":46,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FBus%2FBusServiceProvider.php\u0026line=46\u0026search=\u0026replace="},"items":[["file","/web/vendor/laravel/framework/src/Illuminate/Bus/BusServiceProvider.php:46",4]]},"259":{"object":"Closure($app)","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Foundation/Providers/ArtisanServiceProvider.php","line":216,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2FProviders%2FArtisanServiceProvider.php\u0026line=216\u0026search=\u0026replace="},"items":[["file","/web/vendor/laravel/framework/src/Illuminate/Foundation/Providers/ArtisanServiceProvider.php:216",4]]},"260":{"object":"Closure($app)","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Foundation/Providers/ArtisanServiceProvider.php","line":228,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2FProviders%2FArtisanServiceProvider.php\u0026line=228\u0026search=\u0026replace="},"items":[["file","/web/vendor/laravel/framework/src/Illuminate/Foundation/Providers/ArtisanServiceProvider.php:228",4]]},"261":{"object":"Closure($container, $parameters)","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Container/Container.php","line":290,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FContainer%2FContainer.php\u0026line=290\u0026search=\u0026replace="},"items":[["file","/web/vendor/laravel/framework/src/Illuminate/Container/Container.php:290",4],["use",{"object":"$abstract, $concrete","items":[["$abstract","Illuminate\\Foundation\\Console\\ClearCompiledCommand",4],["$concrete","Illuminate\\Foundation\\Console\\ClearCompiledCommand",4]],"collapsed":true}]]},"262":{"object":"Closure($container, $parameters)","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Container/Container.php","line":290,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FContainer%2FContainer.php\u0026line=290\u0026search=\u0026replace="},"items":[["file","/web/vendor/laravel/framework/src/Illuminate/Container/Container.php:290",4],["use",{"object":"$abstract, $concrete","items":[["$abstract","Illuminate\\Auth\\Console\\ClearResetsCommand",4],["$concrete","Illuminate\\Auth\\Console\\ClearResetsCommand",4]],"collapsed":true}]]},"263":{"object":"Closure($app)","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Foundation/Providers/ArtisanServiceProvider.php","line":308,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2FProviders%2FArtisanServiceProvider.php\u0026line=308\u0026search=\u0026replace="},"items":[["file","/web/vendor/laravel/framework/src/Illuminate/Foundation/Providers/ArtisanServiceProvider.php:308",4]]},"264":{"object":"Closure($app)","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Foundation/Providers/ArtisanServiceProvider.php","line":320,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2FProviders%2FArtisanServiceProvider.php\u0026line=320\u0026search=\u0026replace="},"items":[["file","/web/vendor/laravel/framework/src/Illuminate/Foundation/Providers/ArtisanServiceProvider.php:320",4]]},"265":{"object":"Closure($container, $parameters)","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Container/Container.php","line":290,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FContainer%2FContainer.php\u0026line=290\u0026search=\u0026replace="},"items":[["file","/web/vendor/laravel/framework/src/Illuminate/Container/Container.php:290",4],["use",{"object":"$abstract, $concrete","items":[["$abstract","Illuminate\\Database\\Console\\DbCommand",4],["$concrete","Illuminate\\Database\\Console\\DbCommand",4]],"collapsed":true}]]},"266":{"object":"Closure($container, $parameters)","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Container/Container.php","line":290,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FContainer%2FContainer.php\u0026line=290\u0026search=\u0026replace="},"items":[["file","/web/vendor/laravel/framework/src/Illuminate/Container/Container.php:290",4],["use",{"object":"$abstract, $concrete","items":[["$abstract","Illuminate\\Database\\Console\\PruneCommand",4],["$concrete","Illuminate\\Database\\Console\\PruneCommand",4]],"collapsed":true}]]},"267":{"object":"Closure($container, $parameters)","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Container/Container.php","line":290,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FContainer%2FContainer.php\u0026line=290\u0026search=\u0026replace="},"items":[["file","/web/vendor/laravel/framework/src/Illuminate/Container/Container.php:290",4],["use",{"object":"$abstract, $concrete","items":[["$abstract","Illuminate\\Database\\Console\\WipeCommand",4],["$concrete","Illuminate\\Database\\Console\\WipeCommand",4]],"collapsed":true}]]},"268":{"object":"Closure($container, $parameters)","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Container/Container.php","line":290,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FContainer%2FContainer.php\u0026line=290\u0026search=\u0026replace="},"items":[["file","/web/vendor/laravel/framework/src/Illuminate/Container/Container.php:290",4],["use",{"object":"$abstract, $concrete","items":[["$abstract","Illuminate\\Foundation\\Console\\DownCommand",4],["$concrete","Illuminate\\Foundation\\Console\\DownCommand",4]],"collapsed":true}]]},"269":{"object":"Closure($container, $parameters)","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Container/Container.php","line":290,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FContainer%2FContainer.php\u0026line=290\u0026search=\u0026replace="},"items":[["file","/web/vendor/laravel/framework/src/Illuminate/Container/Container.php:290",4],["use",{"object":"$abstract, $concrete","items":[["$abstract","Illuminate\\Foundation\\Console\\EnvironmentCommand",4],["$concrete","Illuminate\\Foundation\\Console\\EnvironmentCommand",4]],"collapsed":true}]]},"270":{"object":"Closure($container, $parameters)","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Container/Container.php","line":290,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FContainer%2FContainer.php\u0026line=290\u0026search=\u0026replace="},"items":[["file","/web/vendor/laravel/framework/src/Illuminate/Container/Container.php:290",4],["use",{"object":"$abstract, $concrete","items":[["$abstract","Illuminate\\Foundation\\Console\\EventCacheCommand",4],["$concrete","Illuminate\\Foundation\\Console\\EventCacheCommand",4]],"collapsed":true}]]},"271":{"object":"Closure($app)","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Foundation/Providers/ArtisanServiceProvider.php","line":462,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2FProviders%2FArtisanServiceProvider.php\u0026line=462\u0026search=\u0026replace="},"items":[["file","/web/vendor/laravel/framework/src/Illuminate/Foundation/Providers/ArtisanServiceProvider.php:462",4]]},"272":{"object":"Closure($container, $parameters)","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Container/Container.php","line":290,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FContainer%2FContainer.php\u0026line=290\u0026search=\u0026replace="},"items":[["file","/web/vendor/laravel/framework/src/Illuminate/Container/Container.php:290",4],["use",{"object":"$abstract, $concrete","items":[["$abstract","Illuminate\\Foundation\\Console\\EventListCommand",4],["$concrete","Illuminate\\Foundation\\Console\\EventListCommand",4]],"collapsed":true}]]},"273":{"object":"Closure($container, $parameters)","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Container/Container.php","line":290,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FContainer%2FContainer.php\u0026line=290\u0026search=\u0026replace="},"items":[["file","/web/vendor/laravel/framework/src/Illuminate/Container/Container.php:290",4],["use",{"object":"$abstract, $concrete","items":[["$abstract","Illuminate\\Foundation\\Console\\KeyGenerateCommand",4],["$concrete","Illuminate\\Foundation\\Console\\KeyGenerateCommand",4]],"collapsed":true}]]},"274":{"object":"Closure($container, $parameters)","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Container/Container.php","line":290,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FContainer%2FContainer.php\u0026line=290\u0026search=\u0026replace="},"items":[["file","/web/vendor/laravel/framework/src/Illuminate/Container/Container.php:290",4],["use",{"object":"$abstract, $concrete","items":[["$abstract","Illuminate\\Foundation\\Console\\OptimizeCommand",4],["$concrete","Illuminate\\Foundation\\Console\\OptimizeCommand",4]],"collapsed":true}]]},"275":{"object":"Closure($container, $parameters)","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Container/Container.php","line":290,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FContainer%2FContainer.php\u0026line=290\u0026search=\u0026replace="},"items":[["file","/web/vendor/laravel/framework/src/Illuminate/Container/Container.php:290",4],["use",{"object":"$abstract, $concrete","items":[["$abstract","Illuminate\\Foundation\\Console\\OptimizeClearCommand",4],["$concrete","Illuminate\\Foundation\\Console\\OptimizeClearCommand",4]],"collapsed":true}]]},"276":{"object":"Closure($container, $parameters)","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Container/Container.php","line":290,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FContainer%2FContainer.php\u0026line=290\u0026search=\u0026replace="},"items":[["file","/web/vendor/laravel/framework/src/Illuminate/Container/Container.php:290",4],["use",{"object":"$abstract, $concrete","items":[["$abstract","Illuminate\\Foundation\\Console\\PackageDiscoverCommand",4],["$concrete","Illuminate\\Foundation\\Console\\PackageDiscoverCommand",4]],"collapsed":true}]]},"277":{"object":"Closure($container, $parameters)","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Container/Container.php","line":290,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FContainer%2FContainer.php\u0026line=290\u0026search=\u0026replace="},"items":[["file","/web/vendor/laravel/framework/src/Illuminate/Container/Container.php:290",4],["use",{"object":"$abstract, $concrete","items":[["$abstract","Illuminate\\Queue\\Console\\ClearCommand",4],["$concrete","Illuminate\\Queue\\Console\\ClearCommand",4]],"collapsed":true}]]},"278":{"object":"Closure($container, $parameters)","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Container/Container.php","line":290,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FContainer%2FContainer.php\u0026line=290\u0026search=\u0026replace="},"items":[["file","/web/vendor/laravel/framework/src/Illuminate/Container/Container.php:290",4],["use",{"object":"$abstract, $concrete","items":[["$abstract","Illuminate\\Queue\\Console\\ListFailedCommand",4],["$concrete","Illuminate\\Queue\\Console\\ListFailedCommand",4]],"collapsed":true}]]},"279":{"object":"Closure($container, $parameters)","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Container/Container.php","line":290,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FContainer%2FContainer.php\u0026line=290\u0026search=\u0026replace="},"items":[["file","/web/vendor/laravel/framework/src/Illuminate/Container/Container.php:290",4],["use",{"object":"$abstract, $concrete","items":[["$abstract","Illuminate\\Queue\\Console\\FlushFailedCommand",4],["$concrete","Illuminate\\Queue\\Console\\FlushFailedCommand",4]],"collapsed":true}]]},"280":{"object":"Closure($container, $parameters)","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Container/Container.php","line":290,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FContainer%2FContainer.php\u0026line=290\u0026search=\u0026replace="},"items":[["file","/web/vendor/laravel/framework/src/Illuminate/Container/Container.php:290",4],["use",{"object":"$abstract, $concrete","items":[["$abstract","Illuminate\\Queue\\Console\\ForgetFailedCommand",4],["$concrete","Illuminate\\Queue\\Console\\ForgetFailedCommand",4]],"collapsed":true}]]},"27":{"object":"Illuminate\\Events\\Dispatcher","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Events/Dispatcher.php","line":20,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FEvents%2FDispatcher.php\u0026line=20\u0026search=\u0026replace="},"items":[["container",{"ref":2},1],["listeners",[["Illuminate\\Database\\Events\\QueryExecuted",{"array":null,"length":2}],["Illuminate\\Foundation\\Http\\Events\\RequestHandled",{"array":null,"length":1}],["Illuminate\\Foundation\\Events\\LocaleUpdated",{"array":null,"length":1}],["Illuminate\\Log\\Events\\MessageLogged",{"array":null,"length":1}],["Illuminate\\Queue\\Events\\JobExceptionOccurred",{"array":null,"length":1}],["Illuminate\\Queue\\Events\\JobProcessing",{"array":null,"length":1}],["Illuminate\\Queue\\Events\\JobProcessed",{"array":null,"length":1}],["Illuminate\\Auth\\Events\\Registered",{"array":null,"length":1}]],1],["wildcards",[["composing:*",{"array":null,"length":1}],["eloquent.*",{"array":null,"length":1}]],1],["wildcardsCache",[["bootstrapped: Illuminate\\Foundation\\Bootstrap\\RegisterProviders",[]],["bootstrapping: Illuminate\\Foundation\\Bootstrap\\BootProviders",[]],["bootstrapped: Illuminate\\Foundation\\Bootstrap\\BootProviders",[]]],1],["queueResolver",{"ref":28},1]]},"28":{"object":"Closure()","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Events/EventServiceProvider.php","line":18,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FEvents%2FEventServiceProvider.php\u0026line=18\u0026search=\u0026replace="},"items":[["file","/web/vendor/laravel/framework/src/Illuminate/Events/EventServiceProvider.php:18",4],["use",{"object":"$app","items":[["$app",{"ref":2},4]],"collapsed":true}]]},"29":{"object":"App\\Console\\Kernel","editor":{"file":"/web/app/Console/Kernel.php","line":8,"url":"editor://open/?file=%2Fweb%2Fapp%2FConsole%2FKernel.php\u0026line=8\u0026search=\u0026replace="},"items":[["app",{"ref":2},1],["events",{"ref":27},1],["artisan",null,1],["commands",[],1],["commandsLoaded",true,1],["bootstrappers",[[0,"Illuminate\\Foundation\\Bootstrap\\LoadEnvironmentVariables"],[1,"Illuminate\\Foundation\\Bootstrap\\LoadConfiguration"],[2,"Illuminate\\Foundation\\Bootstrap\\HandleExceptions"],[3,"Illuminate\\Foundation\\Bootstrap\\RegisterFacades"],[4,"Illuminate\\Foundation\\Bootstrap\\SetRequestForConsole"],[5,"Illuminate\\Foundation\\Bootstrap\\RegisterProviders"],[6,"Illuminate\\Foundation\\Bootstrap\\BootProviders"]],1]]},"25":{"object":"Illuminate\\Config\\Repository","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Config/Repository.php","line":9,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FConfig%2FRepository.php\u0026line=9\u0026search=\u0026replace="},"items":[["items",[["app",{"array":null,"length":13}],["auth",{"array":null,"length":5}],["broadcasting",{"array":null,"length":2}],["cache",{"array":null,"length":3}],["cors",{"array":null,"length":8}],["database",{"array":null,"length":4}],["doctrine",{"array":null,"length":2}],["filesystems",{"array":null,"length":3}],["hashing",{"array":null,"length":3}],["logging",{"array":null,"length":3}],["mail",{"array":null,"length":4}],["queue",{"array":null,"length":3}],["sanctum",{"array":null,"length":4}],["services",{"array":null,"length":3}],["session",{"array":null,"length":15}],["tracy",{"array":null,"length":4}],["view",{"array":null,"length":2}],["flare",{"array":null,"length":3}],["ignition",{"array":null,"length":10}],["tinker",{"array":null,"length":3}]],1]]},"40":{"object":"DekApps\\LaravelTracy\\ResetHandleExceptions","editor":{"file":"/web/vendor/dek-apps/laravel-tracy/src/ResetHandleExceptions.php","line":8,"url":"editor://open/?file=%2Fweb%2Fvendor%2Fdek-apps%2Flaravel-tracy%2Fsrc%2FResetHandleExceptions.php\u0026line=8\u0026search=\u0026replace="},"items":[]},"26":{"object":"Illuminate\\Foundation\\PackageManifest","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Foundation/PackageManifest.php","line":8,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2FPackageManifest.php\u0026line=8\u0026search=\u0026replace="},"items":[["files",{"ref":136},0],["basePath","/web",0],["vendorPath","/web/vendor",0],["manifestPath","/web/bootstrap/cache/packages.php",0],["manifest",[["fruitcake/laravel-cors",{"array":null,"length":1}],["laravel/sail",{"array":null,"length":1}],["laravel/sanctum",{"array":null,"length":1}],["laravel/tinker",{"array":null,"length":1}],["nesbot/carbon",{"array":null,"length":1}],["nunomaduro/collision",{"array":null,"length":1}],["spatie/laravel-ignition",{"array":null,"length":2}]],0]]},"136":{"object":"Illuminate\\Filesystem\\Filesystem","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php","line":16,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFilesystem%2FFilesystem.php\u0026line=16\u0026search=\u0026replace="},"items":[]},"120":{"object":"Illuminate\\Http\\Request","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Http/Request.php","line":23,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FHttp%2FRequest.php\u0026line=23\u0026search=\u0026replace="},"items":[["json",null,1],["convertedFiles",null,1],["userResolver",null,1],["routeResolver",null,1],["attributes",{"ref":135},0],["request",{"ref":140},0],["query",{"ref":39},0],["server",{"ref":131},0],["files",{"ref":137},0],["cookies",{"ref":133},0],["headers",{"ref":127},0],["content",null,1],["languages",null,1],["charsets",null,1],["encodings",null,1],["acceptableContentTypes",null,1],["pathInfo",null,1],["requestUri",null,1],["baseUrl",null,1],["basePath",null,1],["method",null,1],["format",null,1],["session",null,1],["locale",null,1],["defaultLocale","en",1],["preferredFormat",null,"Symfony\\Component\\HttpFoundation\\Request"],["isHostValid",true,"Symfony\\Component\\HttpFoundation\\Request"],["isForwardedValid",true,"Symfony\\Component\\HttpFoundation\\Request"],["isSafeContentPreferred",{"text":"unset"},"Symfony\\Component\\HttpFoundation\\Request"]]},"135":{"object":"Symfony\\Component\\HttpFoundation\\ParameterBag","editor":{"file":"/web/vendor/symfony/http-foundation/ParameterBag.php","line":23,"url":"editor://open/?file=%2Fweb%2Fvendor%2Fsymfony%2Fhttp-foundation%2FParameterBag.php\u0026line=23\u0026search=\u0026replace="},"items":[["parameters",[],1]]},"140":{"object":"Symfony\\Component\\HttpFoundation\\InputBag","editor":{"file":"/web/vendor/symfony/http-foundation/InputBag.php","line":21,"url":"editor://open/?file=%2Fweb%2Fvendor%2Fsymfony%2Fhttp-foundation%2FInputBag.php\u0026line=21\u0026search=\u0026replace="},"items":[["parameters",[],1]]},"39":{"object":"Symfony\\Component\\HttpFoundation\\InputBag","editor":{"file":"/web/vendor/symfony/http-foundation/InputBag.php","line":21,"url":"editor://open/?file=%2Fweb%2Fvendor%2Fsymfony%2Fhttp-foundation%2FInputBag.php\u0026line=21\u0026search=\u0026replace="},"items":[["parameters",[],1]]},"131":{"object":"Symfony\\Component\\HttpFoundation\\ServerBag","editor":{"file":"/web/vendor/symfony/http-foundation/ServerBag.php","line":21,"url":"editor://open/?file=%2Fweb%2Fvendor%2Fsymfony%2Fhttp-foundation%2FServerBag.php\u0026line=21\u0026search=\u0026replace="},"items":[["parameters",{"array":null,"length":97},1]]},"137":{"object":"Symfony\\Component\\HttpFoundation\\FileBag","editor":{"file":"/web/vendor/symfony/http-foundation/FileBag.php","line":22,"url":"editor://open/?file=%2Fweb%2Fvendor%2Fsymfony%2Fhttp-foundation%2FFileBag.php\u0026line=22\u0026search=\u0026replace="},"items":[["parameters",[],1]]},"133":{"object":"Symfony\\Component\\HttpFoundation\\InputBag","editor":{"file":"/web/vendor/symfony/http-foundation/InputBag.php","line":21,"url":"editor://open/?file=%2Fweb%2Fvendor%2Fsymfony%2Fhttp-foundation%2FInputBag.php\u0026line=21\u0026search=\u0026replace="},"items":[["parameters",[],1]]},"127":{"object":"Symfony\\Component\\HttpFoundation\\HeaderBag","editor":{"file":"/web/vendor/symfony/http-foundation/HeaderBag.php","line":21,"url":"editor://open/?file=%2Fweb%2Fvendor%2Fsymfony%2Fhttp-foundation%2FHeaderBag.php\u0026line=21\u0026search=\u0026replace="},"items":[["headers",{"array":null,"length":5},1],["cacheControl",[],1]]},"21":{"object":"NunoMaduro\\Collision\\Adapters\\Laravel\\ExceptionHandler","editor":{"file":"/web/vendor/nunomaduro/collision/src/Adapters/Laravel/ExceptionHandler.php","line":16,"url":"editor://open/?file=%2Fweb%2Fvendor%2Fnunomaduro%2Fcollision%2Fsrc%2FAdapters%2FLaravel%2FExceptionHandler.php\u0026line=16\u0026search=\u0026replace="},"items":[["appExceptionHandler",{"ref":63},1],["container",{"ref":2},1]]},"126":{"object":"Illuminate\\Log\\LogManager","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Log/LogManager.php","line":22,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FLog%2FLogManager.php\u0026line=22\u0026search=\u0026replace="},"items":[["app",{"ref":2},1],["channels",[],1],["customCreators",[["flare",{"ref":50}]],1],["dateFormat","Y-m-d H:i:s",1],["levels",[["debug",100],["info",200],["notice",250],["warning",300],["error",400],["critical",500],["alert",550],["emergency",600]],1]]},"50":{"object":"Closure","editor":{"file":"/web/vendor/spatie/laravel-ignition/src/IgnitionServiceProvider.php","line":242,"url":"editor://open/?file=%2Fweb%2Fvendor%2Fspatie%2Flaravel-ignition%2Fsrc%2FIgnitionServiceProvider.php\u0026line=242\u0026search=\u0026replace="}},"141":{"object":"Illuminate\\View\\Engines\\EngineResolver","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/View/Engines/EngineResolver.php","line":8,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FView%2FEngines%2FEngineResolver.php\u0026line=8\u0026search=\u0026replace="},"items":[["resolvers",[["file",{"ref":134}],["php",{"ref":143}],["blade",{"ref":142}],["latte",{"ref":146}]],1],["resolved",[],1]]},"134":{"object":"Closure","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/View/ViewServiceProvider.php","line":127,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FView%2FViewServiceProvider.php\u0026line=127\u0026search=\u0026replace="}},"143":{"object":"Closure","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/View/ViewServiceProvider.php","line":140,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FView%2FViewServiceProvider.php\u0026line=140\u0026search=\u0026replace="}},"142":{"object":"Closure","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/View/ViewServiceProvider.php","line":153,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FView%2FViewServiceProvider.php\u0026line=153\u0026search=\u0026replace="}},"146":{"object":"Closure","editor":{"file":"/web/vendor/dek-apps/laravel-latte/src/LaravelLatte/ServiceProvider.php","line":23,"url":"editor://open/?file=%2Fweb%2Fvendor%2Fdek-apps%2Flaravel-latte%2Fsrc%2FLaravelLatte%2FServiceProvider.php\u0026line=23\u0026search=\u0026replace="}},"144":{"object":"Illuminate\\Filesystem\\Filesystem","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php","line":16,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFilesystem%2FFilesystem.php\u0026line=16\u0026search=\u0026replace="},"items":[]},"145":{"object":"Illuminate\\View\\Factory","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/View/Factory.php","line":14,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FView%2FFactory.php\u0026line=14\u0026search=\u0026replace="},"items":[["engines",{"ref":141},1],["finder",{"ref":139},1],["events",{"ref":27},1],["container",{"ref":2},1],["shared",[["__env",{"ref":145}],["app",{"ref":2}]],1],["extensions",[["latte","latte"],["blade.php","blade"],["php","php"],["css","file"],["html","file"]],1],["composers",[],1],["renderCount",0,1],["renderedOnce",[],1],["componentStack",[],1],["componentData",[],1],["currentComponentData",[],1],["slots",[],1],["slotStack",[],1],["sections",[],1],["sectionStack",[],1],["loopsStack",[],1],["pushes",[],1],["prepends",[],1],["pushStack",[],1],["translationReplacements",[],1]]},"139":{"object":"Illuminate\\View\\FileViewFinder","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/View/FileViewFinder.php","line":8,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FView%2FFileViewFinder.php\u0026line=8\u0026search=\u0026replace="},"items":[["files",{"ref":144},1],["paths",{"array":null,"length":1},1],["views",[],1],["hints",{"array":null,"length":2},1],["extensions",{"array":null,"length":5},1]]},"38":{"object":"Illuminate\\Database\\Connectors\\ConnectionFactory","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Database/Connectors/ConnectionFactory.php","line":15,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FDatabase%2FConnectors%2FConnectionFactory.php\u0026line=15\u0026search=\u0026replace="},"items":[["container",{"ref":2},1]]},"51":{"object":"Illuminate\\Database\\DatabaseManager","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Database/DatabaseManager.php","line":17,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FDatabase%2FDatabaseManager.php\u0026line=17\u0026search=\u0026replace="},"items":[["app",{"ref":2},1],["factory",{"ref":38},1],["connections",[],1],["extensions",[],1],["reconnector",{"ref":125},1],["doctrineTypes",[],1]]},"125":{"object":"Closure($connection)","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Database/DatabaseManager.php","line":73,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FDatabase%2FDatabaseManager.php\u0026line=73\u0026search=\u0026replace="},"items":[["file","/web/vendor/laravel/framework/src/Illuminate/Database/DatabaseManager.php:73",4]]},"177":{"object":"Illuminate\\Testing\\ParallelTesting","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Testing/ParallelTesting.php","line":8,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FTesting%2FParallelTesting.php\u0026line=8\u0026search=\u0026replace="},"items":[["container",{"ref":2},1],["optionsResolver",null,1],["tokenResolver",null,1],["setUpProcessCallbacks",[[0,{"ref":176}]],1],["setUpTestCaseCallbacks",[[0,{"ref":178}]],1],["setUpTestDatabaseCallbacks",[],1],["tearDownProcessCallbacks",[],1],["tearDownTestCaseCallbacks",[],1]]},"176":{"object":"Closure","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Testing/Concerns/TestDatabases.php","line":29,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FTesting%2FConcerns%2FTestDatabases.php\u0026line=29\u0026search=\u0026replace="}},"178":{"object":"Closure","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Testing/Concerns/TestDatabases.php","line":39,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FTesting%2FConcerns%2FTestDatabases.php\u0026line=39\u0026search=\u0026replace="}},"184":{"object":"Illuminate\\Routing\\Router","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Routing/Router.php","line":32,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FRouting%2FRouter.php\u0026line=32\u0026search=\u0026replace="},"items":[["events",{"ref":27},1],["container",{"ref":2},1],["routes",{"ref":185},1],["current",null,1],["currentRequest",null,1],["middleware",[["auth","App\\Http\\Middleware\\Authenticate"],["auth.basic","Illuminate\\Auth\\Middleware\\AuthenticateWithBasicAuth"],["cache.headers","Illuminate\\Http\\Middleware\\SetCacheHeaders"],["can","Illuminate\\Auth\\Middleware\\Authorize"],["guest","App\\Http\\Middleware\\RedirectIfAuthenticated"],["password.confirm","Illuminate\\Auth\\Middleware\\RequirePassword"],["signed","Illuminate\\Routing\\Middleware\\ValidateSignature"],["throttle","Illuminate\\Routing\\Middleware\\ThrottleRequests"],["verified","Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified"]],1],["middlewareGroups",[["web",{"array":null,"length":6}],["api",{"array":null,"length":2}]],1],["middlewarePriority",[[0,"Laravel\\Sanctum\\Http\\Middleware\\EnsureFrontendRequestsAreStateful"],[1,"Illuminate\\Cookie\\Middleware\\EncryptCookies"],[2,"Illuminate\\Session\\Middleware\\StartSession"],[3,"Illuminate\\View\\Middleware\\ShareErrorsFromSession"],[4,"Illuminate\\Contracts\\Auth\\Middleware\\AuthenticatesRequests"],[5,"Illuminate\\Routing\\Middleware\\ThrottleRequests"],[6,"Illuminate\\Routing\\Middleware\\ThrottleRequestsWithRedis"],[7,"Illuminate\\Session\\Middleware\\AuthenticateSession"],[8,"Illuminate\\Routing\\Middleware\\SubstituteBindings"],[9,"Illuminate\\Auth\\Middleware\\Authorize"]],0],["binders",[],1],["patterns",[],1],["groupStack",[],1]]},"185":{"object":"Illuminate\\Routing\\RouteCollection","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Routing/RouteCollection.php","line":9,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FRouting%2FRouteCollection.php\u0026line=9\u0026search=\u0026replace="},"items":[["routes",{"array":null,"length":3},1],["allRoutes",{"array":null,"length":6},1],["nameList",{"array":null,"length":3},1],["actionList",{"array":null,"length":4},1]]},"192":{"object":"App\\Http\\Kernel","editor":{"file":"/web/app/Http/Kernel.php","line":7,"url":"editor://open/?file=%2Fweb%2Fapp%2FHttp%2FKernel.php\u0026line=7\u0026search=\u0026replace="},"items":[["middleware",[[0,"App\\Http\\Middleware\\TrustProxies"],[1,"Fruitcake\\Cors\\HandleCors"],[2,"App\\Http\\Middleware\\PreventRequestsDuringMaintenance"],[3,"Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize"],[4,"App\\Http\\Middleware\\TrimStrings"],[5,"Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull"]],1],["middlewareGroups",[["web",{"array":null,"length":6}],["api",{"array":null,"length":2}]],1],["routeMiddleware",[["auth","App\\Http\\Middleware\\Authenticate"],["auth.basic","Illuminate\\Auth\\Middleware\\AuthenticateWithBasicAuth"],["cache.headers","Illuminate\\Http\\Middleware\\SetCacheHeaders"],["can","Illuminate\\Auth\\Middleware\\Authorize"],["guest","App\\Http\\Middleware\\RedirectIfAuthenticated"],["password.confirm","Illuminate\\Auth\\Middleware\\RequirePassword"],["signed","Illuminate\\Routing\\Middleware\\ValidateSignature"],["throttle","Illuminate\\Routing\\Middleware\\ThrottleRequests"],["verified","Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified"]],1],["app",{"ref":2},1],["router",{"ref":184},1],["bootstrappers",[[0,"Illuminate\\Foundation\\Bootstrap\\LoadEnvironmentVariables"],[1,"Illuminate\\Foundation\\Bootstrap\\LoadConfiguration"],[2,"Illuminate\\Foundation\\Bootstrap\\HandleExceptions"],[3,"Illuminate\\Foundation\\Bootstrap\\RegisterFacades"],[4,"Illuminate\\Foundation\\Bootstrap\\RegisterProviders"],[5,"Illuminate\\Foundation\\Bootstrap\\BootProviders"]],1],["middlewarePriority",[[0,"Laravel\\Sanctum\\Http\\Middleware\\EnsureFrontendRequestsAreStateful"],[1,"Illuminate\\Cookie\\Middleware\\EncryptCookies"],[2,"Illuminate\\Session\\Middleware\\StartSession"],[3,"Illuminate\\View\\Middleware\\ShareErrorsFromSession"],[4,"Illuminate\\Contracts\\Auth\\Middleware\\AuthenticatesRequests"],[5,"Illuminate\\Routing\\Middleware\\ThrottleRequests"],[6,"Illuminate\\Routing\\Middleware\\ThrottleRequestsWithRedis"],[7,"Illuminate\\Session\\Middleware\\AuthenticateSession"],[8,"Illuminate\\Routing\\Middleware\\SubstituteBindings"],[9,"Illuminate\\Auth\\Middleware\\Authorize"]],1]]},"195":{"object":"Illuminate\\Support\\DateFactory","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Support/DateFactory.php","line":84,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSupport%2FDateFactory.php\u0026line=84\u0026search=\u0026replace="},"items":[]},"204":{"object":"Spatie\\LaravelIgnition\\Recorders\\DumpRecorder\\DumpRecorder","editor":{"file":"/web/vendor/spatie/laravel-ignition/src/Recorders/DumpRecorder/DumpRecorder.php","line":12,"url":"editor://open/?file=%2Fweb%2Fvendor%2Fspatie%2Flaravel-ignition%2Fsrc%2FRecorders%2FDumpRecorder%2FDumpRecorder.php\u0026line=12\u0026search=\u0026replace="},"items":[["dumps",[],1],["app",{"ref":2},1]]},"211":{"object":"Spatie\\LaravelIgnition\\Recorders\\LogRecorder\\LogRecorder","editor":{"file":"/web/vendor/spatie/laravel-ignition/src/Recorders/LogRecorder/LogRecorder.php","line":9,"url":"editor://open/?file=%2Fweb%2Fvendor%2Fspatie%2Flaravel-ignition%2Fsrc%2FRecorders%2FLogRecorder%2FLogRecorder.php\u0026line=9\u0026search=\u0026replace="},"items":[["logMessages",[],1],["app",{"ref":2},1],["maxLogs",null,1]]},"212":{"object":"Spatie\\LaravelIgnition\\Recorders\\QueryRecorder\\QueryRecorder","editor":{"file":"/web/vendor/spatie/laravel-ignition/src/Recorders/QueryRecorder/QueryRecorder.php","line":8,"url":"editor://open/?file=%2Fweb%2Fvendor%2Fspatie%2Flaravel-ignition%2Fsrc%2FRecorders%2FQueryRecorder%2FQueryRecorder.php\u0026line=8\u0026search=\u0026replace="},"items":[["queries",[],1],["app",{"ref":2},1],["reportBindings",true,1],["maxQueries",200,1]]},"213":{"object":"Spatie\\LaravelIgnition\\Recorders\\JobRecorder\\JobRecorder","editor":{"file":"/web/vendor/spatie/laravel-ignition/src/Recorders/JobRecorder/JobRecorder.php","line":19,"url":"editor://open/?file=%2Fweb%2Fvendor%2Fspatie%2Flaravel-ignition%2Fsrc%2FRecorders%2FJobRecorder%2FJobRecorder.php\u0026line=19\u0026search=\u0026replace="},"items":[["job",null,1],["app",{"ref":2},1],["maxChainedJobReportingDepth",5,1]]},"223":{"object":"Illuminate\\Queue\\QueueManager","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Queue/QueueManager.php","line":13,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FQueue%2FQueueManager.php\u0026line=13\u0026search=\u0026replace="},"items":[["app",{"ref":2},1],["connections",[],1],["connectors",[["null",{"ref":225}],["sync",{"ref":226}],["database",{"ref":227}],["redis",{"ref":228}],["beanstalkd",{"ref":229}],["sqs",{"ref":230}]],1]]},"225":{"object":"Closure","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Queue/QueueServiceProvider.php","line":116,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FQueue%2FQueueServiceProvider.php\u0026line=116\u0026search=\u0026replace="}},"226":{"object":"Closure","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Queue/QueueServiceProvider.php","line":129,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FQueue%2FQueueServiceProvider.php\u0026line=129\u0026search=\u0026replace="}},"227":{"object":"Closure","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Queue/QueueServiceProvider.php","line":142,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FQueue%2FQueueServiceProvider.php\u0026line=142\u0026search=\u0026replace="}},"228":{"object":"Closure","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Queue/QueueServiceProvider.php","line":155,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FQueue%2FQueueServiceProvider.php\u0026line=155\u0026search=\u0026replace="}},"229":{"object":"Closure","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Queue/QueueServiceProvider.php","line":168,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FQueue%2FQueueServiceProvider.php\u0026line=168\u0026search=\u0026replace="}},"230":{"object":"Closure","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Queue/QueueServiceProvider.php","line":181,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FQueue%2FQueueServiceProvider.php\u0026line=181\u0026search=\u0026replace="}},"241":{"object":"Illuminate\\Cache\\CacheManager","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php","line":16,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FCacheManager.php\u0026line=16\u0026search=\u0026replace="},"items":[["app",{"ref":2},1],["stores",[["file",{"ref":243}]],1],["customCreators",[],1]]},"243":{"object":"Illuminate\\Cache\\Repository","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Cache/Repository.php","line":23,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FRepository.php\u0026line=23\u0026search=\u0026replace="},"items":[["store",{"ref":242},1],["events",{"ref":27},1],["default",3600,1]]},"240":{"object":"Illuminate\\Cache\\RateLimiter","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Cache/RateLimiter.php","line":9,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FRateLimiter.php\u0026line=9\u0026search=\u0026replace="},"items":[["cache",{"ref":243},1],["limiters",[["api",{"ref":232}]],1]]},"242":{"object":"Illuminate\\Cache\\FileStore","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Cache/FileStore.php","line":13,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FFileStore.php\u0026line=13\u0026search=\u0026replace="}},"232":{"object":"Closure","editor":{"file":"/web/app/Providers/RouteServiceProvider.php","line":59,"url":"editor://open/?file=%2Fweb%2Fapp%2FProviders%2FRouteServiceProvider.php\u0026line=59\u0026search=\u0026replace="}},"13":{"object":"Closure($url, $app)","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Routing/RoutingServiceProvider.php","line":69,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FRouting%2FRoutingServiceProvider.php\u0026line=69\u0026search=\u0026replace="},"items":[["file","/web/vendor/laravel/framework/src/Illuminate/Routing/RoutingServiceProvider.php:69",4]]},"48":{"object":"Closure($app, $request)","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Auth/AuthServiceProvider.php","line":95,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FAuthServiceProvider.php\u0026line=95\u0026search=\u0026replace="},"items":[["file","/web/vendor/laravel/framework/src/Illuminate/Auth/AuthServiceProvider.php:95",4]]},"53":{"object":"Closure($app, $dispatcher)","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Auth/AuthServiceProvider.php","line":109,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FAuthServiceProvider.php\u0026line=109\u0026search=\u0026replace="},"items":[["file","/web/vendor/laravel/framework/src/Illuminate/Auth/AuthServiceProvider.php:109",4]]},"175":{"object":"Closure($request, $app)","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Foundation/Providers/FormRequestServiceProvider.php","line":33,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2FProviders%2FFormRequestServiceProvider.php\u0026line=33\u0026search=\u0026replace="},"items":[["file","/web/vendor/laravel/framework/src/Illuminate/Foundation/Providers/FormRequestServiceProvider.php:33",4]]},"174":{"object":"Closure($resolved)","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Foundation/Providers/FormRequestServiceProvider.php","line":29,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2FProviders%2FFormRequestServiceProvider.php\u0026line=29\u0026search=\u0026replace="},"items":[["file","/web/vendor/laravel/framework/src/Illuminate/Foundation/Providers/FormRequestServiceProvider.php:29",4]]},"179":{"object":"Closure($view)","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Support/ServiceProvider.php","line":165,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSupport%2FServiceProvider.php\u0026line=165\u0026search=\u0026replace="},"items":[["file","/web/vendor/laravel/framework/src/Illuminate/Support/ServiceProvider.php:165",4],["use",{"object":"$path, $namespace","items":[["$path","/web/vendor/laravel/framework/src/Illuminate/Notifications/resources/views",4],["$namespace","notifications",4]],"collapsed":true}]]},"180":{"object":"Closure($view)","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Support/ServiceProvider.php","line":165,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSupport%2FServiceProvider.php\u0026line=165\u0026search=\u0026replace="},"items":[["file","/web/vendor/laravel/framework/src/Illuminate/Support/ServiceProvider.php:165",4],["use",{"object":"$path, $namespace","items":[["$path","/web/vendor/laravel/framework/src/Illuminate/Pagination/resources/views",4],["$namespace","pagination",4]],"collapsed":true}]]},"182":{"object":"Closure($migrator)","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Support/ServiceProvider.php","line":230,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSupport%2FServiceProvider.php\u0026line=230\u0026search=\u0026replace="},"items":[["file","/web/vendor/laravel/framework/src/Illuminate/Support/ServiceProvider.php:230",4],["use",{"object":"$paths","items":[["$paths","/web/vendor/laravel/sanctum/src/../database/migrations",4]],"collapsed":true}]]},"187":{"object":"Closure($service)","editor":{"file":"/web/vendor/laravel/framework/src/Illuminate/Support/Facades/Facade.php","line":51,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSupport%2FFacades%2FFacade.php\u0026line=51\u0026search=\u0026replace="},"items":[["file","/web/vendor/laravel/framework/src/Illuminate/Support/Facades/Facade.php:51",4],["use",{"object":"$callback","items":[["$callback",{"ref":183},4]],"collapsed":true}]]},"183":{"object":"Closure","editor":{"file":"/web/vendor/laravel/sanctum/src/SanctumServiceProvider.php","line":96,"url":"editor://open/?file=%2Fweb%2Fvendor%2Flaravel%2Fsanctum%2Fsrc%2FSanctumServiceProvider.php\u0026line=96\u0026search=\u0026replace="}},"389":{"object":"ErrorException","items":[["context",null,3],["skippable",true,3],["severity",2,1],["message","dir(/web/storage/Doctrine/log): Failed to open directory: No such file or directory",1],["string","","Exception"],["code",0,1],["file","/web/vendor/dek-apps/nette-doctrine/src/Loggers/FileSQLLogger.php",1],["line",50,1],["trace",[[0,[["file","/web/vendor/tracy/tracy/src/Tracy/Debugger/Debugger.php"],["line",377],["function","handleError"],["class","Tracy\\DevelopmentStrategy"],["type","->"],["args",[[0,2],[1,"dir(/web/storage/Doctrine/log): Failed to open directory: No such file or directory"],[2,"/web/vendor/dek-apps/nette-doctrine/src/Loggers/FileSQLLogger.php"],[3,50],[4,null]]]]],[1,[["function","errorHandler"],["class","Tracy\\Debugger"],["type","::"],["args",[[0,2],[1,"dir(/web/storage/Doctrine/log): Failed to open directory: No such file or directory"],[2,"/web/vendor/dek-apps/nette-doctrine/src/Loggers/FileSQLLogger.php"],[3,50]]]]],[2,[["file","/web/vendor/dek-apps/nette-doctrine/src/Loggers/FileSQLLogger.php"],["line",50],["function","dir"],["args",[[0,"/web/storage/Doctrine/log"]]]]],[3,[["file","/web/vendor/dek-apps/nette-doctrine/src/Loggers/FileSQLLogger.php"],["line",19],["function","clean"],["class","DekApps\\NetteDoctrine\\Loggers\\FileSQLLogger"],["type","->"],["args",[[0,600]]]]],[4,[["file","/web/vendor/dek-apps/nette-doctrine/src/EntityManagerFactory.php"],["line",84],["function","__construct"],["class","DekApps\\NetteDoctrine\\Loggers\\FileSQLLogger"],["type","->"],["args",[[0,"/web/storage/Doctrine/log"]]]]],[5,[["file","/web/vendor/dek-apps/nette-doctrine/src/EntityManagerFactory.php"],["line",134],["function","getDefaultLogger"],["class","DekApps\\NetteDoctrine\\EntityManagerFactory"],["type","->"],["args",[]]]],[6,[["file","/web/vendor/dek-apps/nette-doctrine/src/EntityManagerFactory.php"],["line",95],["function","createConfiguration"],["class","DekApps\\NetteDoctrine\\EntityManagerFactory"],["type","->"],["args",[]]]],[7,[["file","/web/app/Providers/DoctrineServiceProvider.php"],["line",20],["function","create"],["class","DekApps\\NetteDoctrine\\EntityManagerFactory"],["type","->"],["args",[]]]],[8,[["file","/web/vendor/laravel/framework/src/Illuminate/Container/Container.php"],["line",873],["function","App\\Providers\\{closure}"],["class","App\\Providers\\DoctrineServiceProvider"],["type","->"],["args",[[0,{"ref":2}],[1,[]]]]]],[9,[["file","/web/vendor/laravel/framework/src/Illuminate/Container/Container.php"],["line",758],["function","build"],["class","Illuminate\\Container\\Container"],["type","->"],["args",[[0,{"ref":173}]]]]],[10,[["file","/web/vendor/laravel/framework/src/Illuminate/Foundation/Application.php"],["line",855],["function","resolve"],["class","Illuminate\\Container\\Container"],["type","->"],["args",[[0,"Doctrine\\ORM\\EntityManager"],[1,[]],[2,true]]]]],[11,[["file","/web/vendor/laravel/framework/src/Illuminate/Container/Container.php"],["line",694],["function","resolve"],["class","Illuminate\\Foundation\\Application"],["type","->"],["args",[[0,"Doctrine\\ORM\\EntityManager"],[1,[]]]]]],[12,[["file","/web/vendor/laravel/framework/src/Illuminate/Foundation/Application.php"],["line",840],["function","make"],["class","Illuminate\\Container\\Container"],["type","->"],["args",[[0,"Doctrine\\ORM\\EntityManager"],[1,[]]]]]],[13,[["file","/web/vendor/laravel/framework/src/Illuminate/Foundation/helpers.php"],["line",119],["function","make"],["class","Illuminate\\Foundation\\Application"],["type","->"],["args",[[0,"Doctrine\\ORM\\EntityManager"],[1,[]]]]]],[14,[["file","/web/vendor/dek-apps/nette-doctrine/src/ConsoleBootstrap/LaravelConsoleBootstrap.php"],["line",24],["function","app"],["args",[[0,"Doctrine\\ORM\\EntityManager"]]]]],[15,[["file","/web/vendor/dek-apps/nette-doctrine/bin/console.php"],["line",88],["function","getEntityManager"],["class","DekApps\\NetteDoctrine\\ConsoleBootstrap\\LaravelConsoleBootstrap"],["type","->"],["args",[]]]],[16,[["file","/web/vendor/dek-apps/nette-doctrine/bin/console"],["line",4],["args",[[0,"/web/vendor/dek-apps/nette-doctrine/bin/console.php"]]],["function","include"]]],[17,[["file","/web/vendor/bin/console"],["line",107],["args",[[0,"/web/vendor/dek-apps/nette-doctrine/bin/console"]]],["function","include"]]]],"Exception"],["previous",null,"Exception"]]}}'>
</div>

<script>
'use strict';
(function(){const MOVE_THRESHOLD=100;class Toggle{static init(){let start;document.documentElement.addEventListener('mousedown',(e)=>{start=[e.clientX,e.clientY];});document.documentElement.addEventListener('click',(e)=>{let el;if(!e.shiftKey&&!e.ctrlKey&&!e.metaKey&&(el=e.target.closest('.tracy-toggle'))&&Math.pow(start[0]-e.clientX,2)+Math.pow(start[1]-e.clientY,2)<MOVE_THRESHOLD){Toggle.toggle(el,undefined,e);e.preventDefault();e.stopImmediatePropagation();}});Toggle.init=function(){};}static toggle(el,expand,e){let collapsed=el.classList.contains('tracy-collapsed'),ref=el.getAttribute('data-tracy-ref')||el.getAttribute('href',2),dest=el;if(typeof expand==='undefined'){expand=collapsed;}if(!ref||ref==='#'){ref='+';}else if(ref.substr(0,1)==='#'){dest=document;}ref=ref.match(/(\^\s*([^+\s]*)\s*)?(\+\s*(\S*)\s*)?(.*)/);dest=ref[1]?dest.parentNode:dest;dest=ref[2]?dest.closest(ref[2]):dest;dest=ref[3]?Toggle.nextElement(dest.nextElementSibling,ref[4]):dest;dest=ref[5]?dest.querySelector(ref[5]):dest;el.classList.toggle('tracy-collapsed',!expand);dest.classList.toggle('tracy-collapsed',!expand);el.dispatchEvent(new CustomEvent('tracy-toggle',{bubbles:true,detail:{relatedTarget:dest,collapsed:!expand,originalEvent:e}}));}static persist(baseEl,restore){let saved=[];baseEl.addEventListener('tracy-toggle',(e)=>{if(saved.indexOf(e.target)<0){saved.push(e.target);}});let toggles=JSON.parse(sessionStorage.getItem('tracy-toggles-'+baseEl.id));if(toggles&&restore!==false){toggles.forEach((item)=>{let el=baseEl;for(let i in item.path){if(!(el=el.children[item.path[i]])){return;}}if(el.textContent===item.text){Toggle.toggle(el,item.expand);}});}window.addEventListener('unload',()=>{toggles=saved.map((el)=>{let item={path:[],text:el.textContent,expand:!el.classList.contains('tracy-collapsed')};do{item.path.unshift(Array.from(el.parentNode.children).indexOf(el));el=el.parentNode;}while(el&&el!==baseEl);return item;});sessionStorage.setItem('tracy-toggles-'+baseEl.id,JSON.stringify(toggles));});}static nextElement(el,selector){while(el&&selector&&!el.matches(selector)){el=el.nextElementSibling;}return el;}}let Tracy=window.Tracy=window.Tracy||{};Tracy.Toggle=Tracy.Toggle||Toggle;})();(function(){class TableSort{static init(){document.documentElement.addEventListener('click',(e)=>{if(e.target.matches('.tracy-sortable > :first-child > tr:first-child *')){TableSort.sort(e.target.closest('td,th'));}});TableSort.init=function(){};}static sort(tcell){let tbody=tcell.closest('table').tBodies[0];let preserveFirst=!tcell.closest('thead')&&!tcell.parentNode.querySelectorAll('td').length;let asc=!(tbody.tracyAsc===tcell.cellIndex);tbody.tracyAsc=asc?tcell.cellIndex:null;let getText=(cell)=>{return cell?(cell.getAttribute('data-order')||cell.innerText):'';};Array.from(tbody.children).slice(preserveFirst?1:0).sort((a,b)=>{return function(v1,v2){return v1!==''&&v2!==''&&!isNaN(v1)&&!isNaN(v2)?v1-v2:v1.toString().localeCompare(v2);}(getText((asc?a:b).children[tcell.cellIndex]),getText((asc?b:a).children[tcell.cellIndex]));}).forEach((tr)=>{tbody.appendChild(tr);});}}let Tracy=window.Tracy=window.Tracy||{};Tracy.TableSort=Tracy.TableSort||TableSort;})();(function(){class Tabs{static init(){document.documentElement.addEventListener('click',(e)=>{let label,context;if(!e.shiftKey&&!e.ctrlKey&&!e.metaKey&&(label=e.target.closest('.tracy-tab-label'))&&(context=e.target.closest('.tracy-tabs'))){Tabs.toggle(context,label);e.preventDefault();e.stopImmediatePropagation();}});Tabs.init=function(){};}static toggle(context,label){let labels=context.querySelector('.tracy-tab-label').parentNode.querySelectorAll('.tracy-tab-label'),panels=context.querySelector('.tracy-tab-panel').parentNode.querySelectorAll(':scope > .tracy-tab-panel');for(let i=0;i<labels.length;i++){labels[i].classList.toggle('tracy-active',labels[i]===label);}for(let i=0;i<panels.length;i++){panels[i].classList.toggle('tracy-active',labels[i]===label);}}}let Tracy=window.Tracy=window.Tracy||{};Tracy.Tabs=Tracy.Tabs||Tabs;})();(function(){const COLLAPSE_COUNT=7,COLLAPSE_COUNT_TOP=14,TYPE_ARRAY='a',TYPE_OBJECT='o',TYPE_RESOURCE='r',PROP_VIRTUAL=4,PROP_PRIVATE=2;const HINT_CTRL='Ctrl-Click to open in editor',HINT_ALT='Alt-Click to expand/collapse all child nodes';class Dumper{static init(context){(context||document).querySelectorAll('[data-tracy-snapshot][data-tracy-dump]').forEach((pre)=>{let snapshot=JSON.parse(pre.getAttribute('data-tracy-snapshot'));pre.removeAttribute('data-tracy-snapshot');pre.appendChild(build(JSON.parse(pre.getAttribute('data-tracy-dump')),snapshot,pre.classList.contains('tracy-collapsed')));pre.removeAttribute('data-tracy-dump');pre.classList.remove('tracy-collapsed');});(context||document).querySelectorAll('meta[itemprop=tracy-snapshot]').forEach((meta)=>{let snapshot=JSON.parse(meta.getAttribute('content'));meta.parentElement.querySelectorAll('[data-tracy-dump]').forEach((pre)=>{if(pre.closest('[data-tracy-snapshot]')){return;}pre.appendChild(build(JSON.parse(pre.getAttribute('data-tracy-dump')),snapshot,pre.classList.contains('tracy-collapsed')));pre.removeAttribute('data-tracy-dump');pre.classList.remove('tracy-collapsed');});});if(Dumper.inited){return;}Dumper.inited=true;document.documentElement.addEventListener('click',(e)=>{let el;if(e.ctrlKey&&(el=e.target.closest('[data-tracy-href]'))){location.href=el.getAttribute('data-tracy-href');return false;}if((el=e.target.closest('[data-tracy-snapshot]'))){let snapshot=JSON.parse(el.getAttribute('data-tracy-snapshot'));el.removeAttribute('data-tracy-snapshot');el.querySelectorAll('[data-tracy-dump]').forEach((toggler)=>{if(!toggler.nextSibling){toggler.after(document.createTextNode('\n'));}toggler.nextSibling.after(buildStruct(JSON.parse(toggler.getAttribute('data-tracy-dump')),snapshot,toggler,true,[]));toggler.removeAttribute('data-tracy-dump');});}});document.documentElement.addEventListener('tracy-toggle',(e)=>{if(!e.target.matches('.tracy-dump *')){return;}let cont=e.detail.relatedTarget;let origE=e.detail.originalEvent;if(origE&&origE.usedIds){toggleChildren(cont,origE.usedIds);return;}else if(origE&&origE.altKey&&cont.querySelector('.tracy-toggle')){if(e.detail.collapsed){e.target.classList.toggle('tracy-collapsed',false);cont.classList.toggle('tracy-collapsed',false);e.detail.collapsed=false;}let expand=e.target.tracyAltExpand=!e.target.tracyAltExpand;toggleChildren(cont,expand?{}:false);}cont.classList.toggle('tracy-dump-flash',!e.detail.collapsed);});document.documentElement.addEventListener('animationend',(e)=>{if(e.animationName==='tracy-dump-flash'){e.target.classList.toggle('tracy-dump-flash',false);}});document.addEventListener('mouseover',(e)=>{if(!e.target.matches('.tracy-dump *')){return;}let el;if(e.target.matches('.tracy-dump-hash')&&(el=e.target.closest('.tracy-dump'))){el.querySelectorAll('.tracy-dump-hash').forEach((el)=>{if(el.textContent===e.target.textContent){el.classList.add('tracy-dump-highlight');}});return;}if((el=e.target.closest('.tracy-toggle'))&&!el.title){el.title=HINT_ALT;}});document.addEventListener('mouseout',(e)=>{if(e.target.matches('.tracy-dump-hash')){document.querySelectorAll('.tracy-dump-hash.tracy-dump-highlight').forEach((el)=>{el.classList.remove('tracy-dump-highlight');});}});Tracy.Toggle.init();}}function build(data,repository,collapsed,parentIds,keyType){let id,type=data===null?'null':typeof data,collapseCount=collapsed===null?COLLAPSE_COUNT:COLLAPSE_COUNT_TOP;if(type==='null'||type==='number'||type==='boolean'){return createEl(null,null,[createEl('span',{'class':'tracy-dump-'+type.replace('ean','')},[data+''])]);}else if(type==='string'){data={string:data.replace(/&/g,'&amp;').replace(/</g,'&lt;'),length:[...data].length};}else if(Array.isArray(data)){data={array:null,items:data};}else if(data.ref){id=data.ref;data=repository[id];if(!data){throw new UnknownEntityException;}}if(data.string!==undefined||data.bin!==undefined){let s=data.string===undefined?data.bin:data.string;if(keyType===TYPE_ARRAY){return createEl(null,null,[createEl('span',{'class':'tracy-dump-string'},{html:'<span class="tracy-dump-lq">\'<\/span>'+s+'<span>\'<\/span>'}),]);}else if(keyType!==undefined){if(type!=='string'){s='<span class="tracy-dump-lq">\'<\/span>'+s+'<span>\'<\/span>';}const classes=['tracy-dump-public','tracy-dump-protected','tracy-dump-private','tracy-dump-dynamic','tracy-dump-virtual',];return createEl(null,null,[createEl('span',{'class':classes[typeof keyType==='string'?PROP_PRIVATE:keyType],'title':typeof keyType==='string'?'declared in '+keyType:null,},{html:s}),]);}let count=(s.match(/\n/g)||[]).length;if(count){let collapsed=count>=COLLAPSE_COUNT;return createEl(null,null,[createEl('span',{'class':collapsed?'tracy-toggle tracy-collapsed':'tracy-toggle'},['string']),'\n',createEl('div',{'class':'tracy-dump-string'+(collapsed?' tracy-collapsed':''),'title':data.length+(data.bin?' bytes':' characters'),},{html:'<span class="tracy-dump-lq">\'<\/span>'+s+'<span>\'<\/span>'}),]);}return createEl(null,null,[createEl('span',{'class':'tracy-dump-string','title':data.length+(data.bin?' bytes':' characters'),},{html:'<span>\'<\/span>'+s+'<span>\'<\/span>'}),]);}else if(data.number){return createEl(null,null,[createEl('span',{'class':'tracy-dump-number'},[data.number])]);}else if(data.text!==undefined){return createEl(null,null,[createEl('span',{class:'tracy-dump-virtual'},[data.text])]);}else{let span=data.array!==undefined?[createEl('span',{'class':'tracy-dump-array'},['array']),' ('+(data.length||data.items.length)+')']:[createEl('span',{'class':data.object?'tracy-dump-object':'tracy-dump-resource',title:data.editor?'Declared in file '+data.editor.file+' on line '+data.editor.line+(data.editor.url?'\n'+HINT_CTRL:'')+'\n'+HINT_ALT:null,'data-tracy-href':data.editor?data.editor.url:null},[data.object||data.resource]),...(id?[' ',createEl('span',{'class':'tracy-dump-hash'},[data.resource?'@'+id.substr(1):'#'+id])]:[])];parentIds=parentIds?parentIds.slice():[];let recursive=id&&parentIds.indexOf(id)>-1;parentIds.push(id);if(recursive||!data.items||!data.items.length){span.push(recursive?' RECURSION':(!data.items||data.items.length?' …':''));return createEl(null,null,span);}collapsed=collapsed===true||data.collapsed||(data.items&&data.items.length>=collapseCount);let toggle=createEl('span',{'class':collapsed?'tracy-toggle tracy-collapsed':'tracy-toggle'},span);return createEl(null,null,[toggle,'\n',buildStruct(data,repository,toggle,collapsed,parentIds),]);}}function buildStruct(data,repository,toggle,collapsed,parentIds){if(Array.isArray(data)){data={items:data};}else if(data.ref){parentIds=parentIds.slice();parentIds.push(data.ref);data=repository[data.ref];}let cut=data.items&&data.length>data.items.length;let type=data.object?TYPE_OBJECT:data.resource?TYPE_RESOURCE:TYPE_ARRAY;let div=createEl('div',{'class':collapsed?'tracy-collapsed':null});if(collapsed){let handler;toggle.addEventListener('tracy-toggle',handler=function(){toggle.removeEventListener('tracy-toggle',handler);createItems(div,data.items,type,repository,parentIds,null);if(cut){createEl(div,null,['…\n']);}});}else{createItems(div,data.items,type,repository,parentIds,true);if(cut){createEl(div,null,['…\n']);}}return div;}function createEl(el,attrs,content){if(!(el instanceof Node)){el=el?document.createElement(el):document.createDocumentFragment();}for(let id in attrs||{}){if(attrs[id]!==null){el.setAttribute(id,attrs[id]);}}if(content&&content.html!==undefined){el.innerHTML=content.html;return el;}content=content||[];for(let id=0;id<content.length;id++){let child=content[id];if(child!==null){el.appendChild(child instanceof Node?child:document.createTextNode(child));}}return el;}function createItems(el,items,type,repository,parentIds,collapsed){let key,val,vis,ref,i,tmp;for(i=0;i<items.length;i++){if(type===TYPE_ARRAY){[key,val,ref]=items[i];}else{[key,val,vis=PROP_VIRTUAL,ref]=items[i];}createEl(el,null,[build(key,null,null,null,type===TYPE_ARRAY?TYPE_ARRAY:vis),type===TYPE_ARRAY?' => ':': ',...(ref?[createEl('span',{'class':'tracy-dump-hash'},['&'+ref]),' ']:[]),tmp=build(val,repository,collapsed,parentIds),tmp.lastElementChild.tagName==='DIV'?'':'\n',]);}}function toggleChildren(cont,usedIds){let hashEl,id;cont.querySelectorAll(':scope > .tracy-toggle').forEach((el)=>{hashEl=(el.querySelector('.tracy-dump-hash')||el.previousElementSibling);id=hashEl&&hashEl.matches('.tracy-dump-hash')?hashEl.textContent:null;if(!usedIds||(id&&usedIds[id])){Tracy.Toggle.toggle(el,false);}else{usedIds[id]=true;Tracy.Toggle.toggle(el,true,{usedIds:usedIds});}});}function UnknownEntityException(){}let Tracy=window.Tracy=window.Tracy||{};Tracy.Dumper=Tracy.Dumper||Dumper;function init(){Tracy.Dumper.init();}if(document.readyState==='loading'){document.addEventListener('DOMContentLoaded',init);}else{init();}})();(function(){class BlueScreen{static init(ajax){let blueScreen=document.getElementById('tracy-bs');let styles=[];for(let i=0;i<document.styleSheets.length;i++){let style=document.styleSheets[i];if(!style.ownerNode.classList.contains('tracy-debug')){style.oldDisabled=style.disabled;style.disabled=true;styles.push(style);}}if(navigator.platform.indexOf('Mac')>-1){blueScreen.classList.add('mac');}document.getElementById('tracy-bs-toggle').addEventListener('tracy-toggle',function(){let collapsed=this.classList.contains('tracy-collapsed');for(let i=0;i<styles.length;i++){styles[i].disabled=collapsed?styles[i].oldDisabled:true;}});if(!ajax){document.body.appendChild(blueScreen);let id=location.href+document.querySelector('.section--error').textContent;Tracy.Toggle.persist(blueScreen,sessionStorage.getItem('tracy-toggles-bskey')===id);sessionStorage.setItem('tracy-toggles-bskey',id);}if(inited){return;}inited=true;document.addEventListener('keyup',(e)=>{if(e.keyCode===27&&!e.shiftKey&&!e.altKey&&!e.ctrlKey&&!e.metaKey){Tracy.Toggle.toggle(document.getElementById('tracy-bs-toggle'));}});blueScreen.addEventListener('tracy-toggle',(e)=>{if(!e.target.matches('.tracy-dump *')&&e.detail.originalEvent){e.detail.relatedTarget.classList.toggle('panel-fadein',!e.detail.collapsed);}});Tracy.TableSort.init();Tracy.Tabs.init();}static loadAjax(content){let ajaxBs=document.getElementById('tracy-bs');if(ajaxBs){ajaxBs.remove();}document.body.insertAdjacentHTML('beforeend',content);ajaxBs=document.getElementById('tracy-bs');Tracy.Dumper.init(ajaxBs);BlueScreen.init(true);window.scrollTo(0,0);}}let inited;let Tracy=window.Tracy=window.Tracy||{};Tracy.BlueScreen=Tracy.BlueScreen||BlueScreen;})();Tracy.BlueScreen.init();
</script>
</body>
</html>
