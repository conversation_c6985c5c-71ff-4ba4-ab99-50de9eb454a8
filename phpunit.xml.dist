<?xml version="1.0" encoding="UTF-8"?>
<phpunit xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:noNamespaceSchemaLocation="./vendor/phpunit/phpunit/phpunit.xsd"
         bootstrap="vendor/autoload.php"
         colors="true"
         verbose="true"
         failOnWarning="true"
         failOnRisky="true"
>
    <testsuites>
        <testsuite name="AllTests">
            <directory>tests/</directory>
        </testsuite>
    </testsuites>
    <filter>
        <whitelist>
            <directory suffix=".php">src/</directory>
        </whitelist>
    </filter>
    <logging>
        <log type="coverage-html" target="build/coverage" />
        <log type="coverage-text" target="build/coverage.txt" showOnlySummary="true" />
    </logging>
</phpunit>
