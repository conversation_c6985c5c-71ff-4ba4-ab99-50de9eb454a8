includes:
    - ./vendor/nunomaduro/larastan/extension.neon

parameters:

    paths:
        - app 
        - bootstrap 
        - config 
        - routes

    # The level 9 is the highest level
    level: 8

    ignoreErrors:
        - '/Parameter #1 \$key of method Illuminate\\Cache\\RateLimiting\\Limit::by\(\) expects string, int<min, -1>\|int<1, max>\|string\|null given/'

    excludePaths:
        - ./routes/console.php #Undefined variable: $this

    checkMissingIterableValueType: false
