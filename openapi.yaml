openapi: 3.1.0
info:
  title: DEK Errorlog
  description: |-
    API dokumentace pro aplikaci pracujíc<PERSON> s chybami ostatních webových klientů.

    Užitečné odkazy:
    - [Errorlog](https://errorlog.dek.cz)
  termsOfService: http://swagger.io/terms/
  contact:
    email: <EMAIL>
  version: 1.0.0
servers:
- url: https://errorlog.dek.cz/api/v1
- url: https://errorlog-dek-cz.delta.dek.cz/api/v1
tags:
- name: log
  description: Zpracování logu z aplikace
- name: touch
  description: Zjišťuje existenci aplikace
  externalDocs:
    description: Bude se to hodit?
    url: http://swagger.io
- name: rundeck
  description: Deploy záznamy z rundeck.dek.cz
paths:
  /log:
    post:
      tags:
      - log
      summary: Zpracuje log z klienta
      parameters:
      - name: app_id
        in: query
        description: ID aplikace
        required: true
        schema:
          type: integer
      - name: filename
        in: query
        description: N<PERSON>zev souboru
        required: true
        schema:
          type: string
      - name: content
        in: query
        description: Obsah
        required: true
        schema:
          type: string
      - name: logger
        in: query
        description: Název loggeru
        required: true
        schema:
          type: string
      - name: logger_version
        in: query
        description: Verze loggeru
        required: true
        schema:
          type: string
      - name: signature
        in: query
        description: Podpis
        required: true
        schema:
          type: string
      - name: timestamp
        in: query
        description: Časová známka (Unix Epoch)
        required: true
        schema:
          type: integer
      requestBody:
        description: Nahraj nový log do errorlogu
        content:
          application/json:
            schema:
              type: object
              properties:
                id:
                  type: integer
                  format: int11
                  examples:
                  - 618
                app_id:
                  type: object
                  format: int11
                  examples:
                  - 2
                filename:
                  type: string
                  format: varchar255
                  examples:
                  - exception.log
                content:
                  type: string
                  format: longblob
                logger:
                  type: string
                  format: varchar255
                  examples:
                  - tracy
                logger_version:
                  type: string
                  format: varchar255
                  examples:
                  - 2.6.8
                parsed:
                  type: boolean
                  format: tinyint1
                  examples:
                  - 0
                  - 1
          application/xml:
            schema:
              type: object
              properties:
                id:
                  type: integer
                  format: int11
                  examples:
                  - 618
                app_id:
                  type: object
                  format: int11
                  examples:
                  - 2
                filename:
                  type: string
                  format: varchar255
                  examples:
                  - exception.log
                content:
                  type: string
                  format: longblob
                logger:
                  type: string
                  format: varchar255
                  examples:
                  - tracy
                logger_version:
                  type: string
                  format: varchar255
                  examples:
                  - 2.6.8
                parsed:
                  type: boolean
                  format: tinyint1
                  examples:
                  - 0
                  - 1
          application/x-www-form-urlencoded:
            schema:
              type: object
              properties:
                id:
                  type: integer
                  format: int11
                  examples:
                  - 618
                app_id:
                  type: object
                  format: int11
                  examples:
                  - 2
                filename:
                  type: string
                  format: varchar255
                  examples:
                  - exception.log
                content:
                  type: string
                  format: longblob
                logger:
                  type: string
                  format: varchar255
                  examples:
                  - tracy
                logger_version:
                  type: string
                  format: varchar255
                  examples:
                  - 2.6.8
                parsed:
                  type: boolean
                  format: tinyint1
                  examples:
                  - 0
                  - 1
        required: true
      responses:
        "400":
          description: Bad request
        "401":
          description: App with given ID not found
        "403":
          description: Request expired
  /touch:
    post:
      tags:
      - touch
      parameters:
      - name: app_id
        in: query
        description: ID aplikace
        required: true
        schema:
          type: integer
      - name: signature
        in: query
        description: Podpis
        required: true
        schema:
          type: string
      - name: timestamp
        in: query
        description: Časová známka (Unix Epoch)
        required: true
        schema:
          type: integer
      responses:
        "200":
          description: OK
        "400":
          description: Bad Request
        "401":
          description: App with given ID not found
        "403":
          description: Request expired
components:
  schemas:
    App:
      type: object
      properties:
        id:
          type: integer
          format: int11
          examples:
          - 2
        title:
          type: string
          format: varchar255
          examples:
          - Dekparty
          - Dekpeople
          - IT Sklad
        slug:
          type: string
          format: varchar255
          examples:
          - dekparty
          - dekpeople
          - it-sklad
        domain:
          type: string
          format: varchar255
          examples:
          - https://gitlab.dek.cz/web/dekparty
          - https://gitlab.dek.cz/web/dekpeople
          - https://gitlab.dek.cz/web/it-sklad
        ssh:
          type: string
          format: longtext
          examples:
          - '---- BEGIN PUBLIC KEY -----'
        ssh_algo:
          type: integer
          format: int11
          examples:
          - 9
        gitlab_id:
          type: integer
          format: int11
          examples:
          - 28
          - 136
          - 661
        gitlab_label:
          type: string
          format: varchar255
          examples:
          - "error,errorlog"
          - bug
          - refactoring
        gitlab_group:
          type: string
          examples:
          - web
          enum:
          - deksoft
          - eshop
          - web
    Log:
      type: object
      properties:
        id:
          type: integer
          format: int11
          examples:
          - 618
        app_id:
          type: object
          format: int11
          examples:
          - 2
        filename:
          type: string
          format: varchar255
          examples:
          - exception.log
        content:
          type: string
          format: longblob
        logger:
          type: string
          format: varchar255
          examples:
          - tracy
        logger_version:
          type: string
          format: varchar255
          examples:
          - 2.6.8
        parsed:
          type: boolean
          format: tinyint1
          examples:
          - 0
          - 1
  requestBodies:
    App:
      description: Pet object that needs to be added to the store
      content:
        application/json:
          schema:
            type: object
            properties:
              id:
                type: integer
                format: int11
                examples:
                - 2
              title:
                type: string
                format: varchar255
                examples:
                - Dekparty
                - Dekpeople
                - IT Sklad
              slug:
                type: string
                format: varchar255
                examples:
                - dekparty
                - dekpeople
                - it-sklad
              domain:
                type: string
                format: varchar255
                examples:
                - https://gitlab.dek.cz/web/dekparty
                - https://gitlab.dek.cz/web/dekpeople
                - https://gitlab.dek.cz/web/it-sklad
              ssh:
                type: string
                format: longtext
                examples:
                - '---- BEGIN PUBLIC KEY -----'
              ssh_algo:
                type: integer
                format: int11
                examples:
                - 9
              gitlab_id:
                type: integer
                format: int11
                examples:
                - 28
                - 136
                - 661
              gitlab_label:
                type: string
                format: varchar255
                examples:
                - "error,errorlog"
                - bug
                - refactoring
              gitlab_group:
                type: string
                examples:
                - web
                enum:
                - deksoft
                - eshop
                - web
        application/xml:
          schema:
            type: object
            properties:
              id:
                type: integer
                format: int11
                examples:
                - 2
              title:
                type: string
                format: varchar255
                examples:
                - Dekparty
                - Dekpeople
                - IT Sklad
              slug:
                type: string
                format: varchar255
                examples:
                - dekparty
                - dekpeople
                - it-sklad
              domain:
                type: string
                format: varchar255
                examples:
                - https://gitlab.dek.cz/web/dekparty
                - https://gitlab.dek.cz/web/dekpeople
                - https://gitlab.dek.cz/web/it-sklad
              ssh:
                type: string
                format: longtext
                examples:
                - '---- BEGIN PUBLIC KEY -----'
              ssh_algo:
                type: integer
                format: int11
                examples:
                - 9
              gitlab_id:
                type: integer
                format: int11
                examples:
                - 28
                - 136
                - 661
              gitlab_label:
                type: string
                format: varchar255
                examples:
                - "error,errorlog"
                - bug
                - refactoring
              gitlab_group:
                type: string
                examples:
                - web
                enum:
                - deksoft
                - eshop
                - web
    Log:
      description: List of user object
      content:
        application/json:
          schema:
            type: array
            items:
              type: object
              properties:
                id:
                  type: integer
                  format: int11
                  examples:
                  - 2
                title:
                  type: string
                  format: varchar255
                  examples:
                  - Dekparty
                  - Dekpeople
                  - IT Sklad
                slug:
                  type: string
                  format: varchar255
                  examples:
                  - dekparty
                  - dekpeople
                  - it-sklad
                domain:
                  type: string
                  format: varchar255
                  examples:
                  - https://gitlab.dek.cz/web/dekparty
                  - https://gitlab.dek.cz/web/dekpeople
                  - https://gitlab.dek.cz/web/it-sklad
                ssh:
                  type: string
                  format: longtext
                  examples:
                  - '---- BEGIN PUBLIC KEY -----'
                ssh_algo:
                  type: integer
                  format: int11
                  examples:
                  - 9
                gitlab_id:
                  type: integer
                  format: int11
                  examples:
                  - 28
                  - 136
                  - 661
                gitlab_label:
                  type: string
                  format: varchar255
                  examples:
                  - "error,errorlog"
                  - bug
                  - refactoring
                gitlab_group:
                  type: string
                  examples:
                  - web
                  enum:
                  - deksoft
                  - eshop
                  - web
