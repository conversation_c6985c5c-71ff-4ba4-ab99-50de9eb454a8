DEKCOMPOSER?=composer
PHP?=php
DEKCONFIGFILE?=devel

help:
	@echo "help                  - napoveda"
	@echo "update                - update na produkci"
	@echo "update-devel          - update na vyvoji"
	@echo "copy-config           - zkopiruje pozadovany .env soubor"
	@echo "clear-cache           - smaze cache"
	@echo "orm-generate-proxies  - vygeneruje cache pro Doctrinu (na produkci je AUTOGENERATE_NEVER)"
	@echo "migrate-db            - aktualizuje DB"

update:
	make copy-config
	$(DEKCOMPOSER) install --no-ansi --no-interaction --no-progress --no-dev --optimize-autoloader
	make cache-clear
	make orm-generate-proxies
	make migrate-db

update-devel:
	make copy-config
	$(DEKCOMPOSER) install
	make cache-clear

copy-config:
	cp -f dist/$(DEKCONFIGFILE).env .env
	
cache-clear:
	$(PHP) artisan cache:clear --no-interaction
	
orm-generate-proxies:
	$(PHP) vendor/bin/console orm:generate-proxies --no-ansi --no-interaction
	
migrate-db:
	$(PHP) vendor/bin/console migrations:migrate --no-ansi --no-interaction

