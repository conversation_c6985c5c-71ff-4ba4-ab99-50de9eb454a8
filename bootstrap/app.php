<?php

use App\Http\Middleware\DoctrineEntityFinder;
use DekApps\LaravelTracy\ExceptionsHelper;
use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        web: __DIR__.'/../routes/web.php',
        commands: __DIR__.'/../routes/console.php',
        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware) {
        $middleware->trustProxies(at: '*');
        $middleware->validateCsrfTokens([
            '/api/*'
        ]);
        $middleware->web(append: DoctrineEntityFinder::class);
    })
    ->withExceptions(function (Exceptions $exceptions) {
        ExceptionsHelper::renderExceptionByTracy($exceptions)
            ->json('api/*');
    })
    ->create();
