<?php

use <PERSON>\Debugger;

return [

    /*
    |--------------------------------------------------------------------------
    | Vývoj<PERSON><PERSON>sk<PERSON> vs produk<PERSON><PERSON><PERSON>
    |--------------------------------------------------------------------------
    |
    | Vývojářský/produkční režim můžeme přímo nastavit použitím konstanty
    | Debugger::Development nebo Debugger::Production, případně nastavit
    | IP adresu, nebo token v cookies.
    | https://tracy.nette.org/cs/guide#toc-vyvojarsky-vs-produkcni-rezim
    |
    | Debugger::Development, Debugger::Production, (string|string[]) IP
    |
    */

    'mode' => env('APP_DEBUG') === true ? Debugger::Development : Debugger::Production,

    /*
    |--------------------------------------------------------------------------
    | Logování chyb
    |--------------------------------------------------------------------------
    |
    | V produkčním režimu Tracy automaticky všechny chyby a zachycené výjimky
    | zaznamenává do textového logu. Aby logování mohlo probíhat, musíme
    | nastavit absolutní cestu k logovacímu adresáři.
    | https://tracy.nette.org/cs/guide#toc-logovani-chyb
    |
    | (string) absolutní cesta k adresáři
    |
    */

    'directory' => env('TRACY_DIR', storage_path('logs/tracy')),

    /*
    |--------------------------------------------------------------------------
    | Odesílání chyby na email
    |--------------------------------------------------------------------------
    |
    | Pro skutečného profíka je error log klíčovým zdrojem informací a chce být
    | ihned informován o každé nové chybě. Tracy mu v tom vychází vstříc, umí
    | totiž o novém záznamu v logu informovat e-mailem.
    | https://tracy.nette.org/cs/guide#toc-logovani-chyb
    |
    | to: (string|string[]|null) e-mail, na který se posílají notifikace
    | from: (string|null) odesílatel e-mailu
    | email_snooze: (string|null) po jaké nejkratší době odeslat další email?
    |
    */

    'email' => [
        'to' => env('TRACY_EMAIL'),
        'from' => env('MAIL_FROM_ADDRESS'),
        'snooze' => '2 days',
    ],


    /*
    |--------------------------------------------------------------------------
    | Strict mode
    |--------------------------------------------------------------------------
    |
    | Ve vývojovém režimu zobrazí chyby typu notice, warning jako BlueScreen.
    | Je možné vybrat jen některé úrovně chyb, např.:
    | E_USER_DEPRECATED | E_DEPRECATED
    | https://tracy.nette.org/cs/configuring#toc-ostatni
    |
    | (bool|int) výchozí je false
    |
    */

    'strict_mode' => true,

    /*
    |--------------------------------------------------------------------------
    | Vendor deprecations
    |--------------------------------------------------------------------------
    |
    | Možno vypnout/zapnout deprecated chyby ve vendoru. Pro cely vendor, nebo
    | jen pro konkretni baliky. Napr.:
    | '*' - cely vendor
    | ['dek-apps'] - vse pod dek-apps
    | ['symfony/console'] - konkretni balik
    |
    | (string[]|string) pro cely vendor pouzit '*'
    |
    */

    'disable_vendor_deprecations' => [],

    /*
    |--------------------------------------------------------------------------
    | Chování dump()
    |--------------------------------------------------------------------------
    | https://tracy.nette.org/cs/configuring#toc-chovani-dump
    |
    | max_length: maximální délka řetězce, (int|null)
    | max_depth: maximální hloubka zanoření, (int|null)
    | keys_to_hide: skrýt hodnoty těchto klíčů, (string[])
    | theme: vizuální téma, (light|dark)
    | show_location: zobrazit místo, kde byla volána funkce dump()? (bool|null)
    |
    | null - výchozí dle verze Tracy
    |
    */

    'dump' => [
        'max_length' => null,
        'max_depth' => null,
        'keys_to_hide' => [],
        'theme' => 'light',
        'show_location' => null,
    ]
];
