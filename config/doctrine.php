<?php

return [
    'orm' => [
        'dev_mode' => env('APP_DEBUG'),
        'paths' => app_path('Model/Entities'),
        'temp_dir' => storage_path('doctrine'),
        'annotations' => false,
        'schema_tool_update_command' => true,
        'ignored_tables' => ['jobs', 'failed_jobs'],
        'db_params' => [
            'driver' => 'pdo_mysql',
            'host' => env('DB_HOST'),
            'port' => env('DB_PORT'),
            'user' => env('DB_USERNAME'),
            'password' => env('DB_PASSWORD'),
            'dbname' => env('DB_DATABASE'),
            'charset' => 'utf8mb4',
        ]
    ],

    'migrations' => [
        'table_storage' => [
            'version_column_length' => 512
        ],
        'migrations_paths' => [
            "App\\Model\\Migrations" => "/web/app/Model/Migrations"
        ],
        'all_or_nothing' => true,
    ]
];
