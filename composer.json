{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "repositories": [{"type": "composer", "url": "https://composer.dek.cz"}], "require": {"php": "^8.0", "dek-apps/dektrine": "^2.1", "dek-apps/laravel-latte": "^3.0", "dek-apps/laravel-tracy": "^2.0", "dek-apps/oauth2-dek": "^1.0", "dek-apps/simple-ssl": "^1.1", "erusev/parsedown": "^1.7", "guzzlehttp/guzzle": "^7.4", "laravel/framework": "^12.0", "laravel/helpers": "^1.5", "laravel/tinker": "^2.9", "omines/oauth2-gitlab": "^3.6"}, "require-dev": {"fakerphp/faker": "^1.9.1", "friendsofphp/php-cs-fixer": "^3.8", "laravel/sail": "^1.0.1", "mockery/mockery": "^1.4.4", "nunomaduro/collision": "^8.1", "phpunit/phpunit": "^11.0", "spatie/laravel-ignition": "^2.0"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Seeders\\": "database/seeders/", "Database\\Migrations\\": "database/migrations/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "DekApps\\LaravelTracy\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('dist/devel.env', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true}, "minimum-stability": "stable", "prefer-stable": true}