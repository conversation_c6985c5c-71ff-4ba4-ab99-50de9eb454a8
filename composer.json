{"name": "guzzlehttp/uri-template", "description": "A polyfill class for uri_template of PHP", "keywords": ["guzzlehttp", "uri-template"], "license": "MIT", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/gmponos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}], "repositories": [{"type": "package", "package": {"name": "uri-template/tests", "version": "1.0.0", "dist": {"url": "https://github.com/uri-templates/uritemplate-test/archive/520fdd8b0f78779d12178c357a986e0e727f4bd0.zip", "type": "zip"}}}], "require": {"php": "^7.2.5 || ^8.0", "symfony/polyfill-php80": "^1.24"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "phpunit/phpunit": "^8.5.36 || ^9.6.15", "uri-template/tests": "1.0.0"}, "autoload": {"psr-4": {"GuzzleHttp\\UriTemplate\\": "src"}}, "autoload-dev": {"psr-4": {"GuzzleHttp\\UriTemplate\\Tests\\": "tests"}}, "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}}, "config": {"allow-plugins": {"bamarni/composer-bin-plugin": true}, "preferred-install": "dist", "sort-packages": true}}