<?php

namespace App\Auth;

use App\Enums\Logger;
use App\Model\Entities\App;
use App\Model\Facades\AppFacade;
use DekApps\SimpleSSL\SSLException;
use Illuminate\Http\Request;

class SSL
{
    public const int REQUEST_EXPIRATION = 30;

    public function __construct(
        private readonly AppFacade $appFacade,
    ) {
    }

    /**
     * @throws SSLException
     */
    public function validate(Request $request): App
    {
        $app = $this->appFacade->getAppById($request->input('app_id'));
        if (!$app) {
            throw new SSLException('App with given ID not found', \App\Auth\SSLException::APP_NOT_FOUND);
        }

        // Novy zjednoduseny tvar podpisu
        $signature = $app->getId() . $request->input('ts');
        $verified = $this->validateSignature($request, $app, $signature);
        if (! $verified) {
            // Stary (paranoidni, zbytecne slozity) tvar podpisu
            $signature = $this->createSignatureOld($request);
            $verified = $this->validateSignature($request, $app, $signature);
        }

        if (! $verified) {
            throw new SSLException('Authentication failed', \App\Auth\SSLException::INVALID_SIGNATURE);
        }

        if (! $this->validateTimestamp($request->input('ts'))) {
            throw new SSLException('Authentication failed', \App\Auth\SSLException::SIGNATURE_EXPIRED);
        }

        return $app;
    }

    private function validateSignature(Request $request, App $app, string $signature): bool
    {
        $ssl = new \DekApps\SimpleSSL\SSL();
        $ssl->setRemoteKey($app->getSsh());
        $ssl->setAlgo($app->getSshAlgo()->value);

        return $ssl->verify($signature, $request->input('signature'));
    }

    private function validateTimestamp(int $timestamp): bool
    {
        return time() - $timestamp < self::REQUEST_EXPIRATION;
    }

    /**
     * @deprecated Use createSignature instead.
     */
    private function createSignatureOld(Request $request): string
    {
        $app_id = $request->input('app_id');
        $filename = $request->input('filename');
        $logger = Logger::tryFrom($request->input('logger'));
        $logger_version = $request->input('logger_version');
        $ts = $request->input('ts');
        return $app_id . $filename . $logger?->value . $logger_version . $ts;
    }
}
