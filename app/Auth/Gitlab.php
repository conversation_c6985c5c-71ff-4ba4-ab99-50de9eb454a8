<?php

namespace App\Auth;

use Illuminate\Contracts\Auth\Authenticatable;
use Illuminate\Support\Facades\Auth;
use League\OAuth2\Client\Provider\Exception\IdentityProviderException;
use League\OAuth2\Client\Token\AccessTokenInterface;
use Omines\OAuth2\Client\Provider\Gitlab as GitlabProvider;

readonly class Gitlab
{
    public function __construct(
        private GitlabProvider $gitlab,
        private GitlabServiceProvider $gitlabServiceProvider
    ) {
    }

    public function authorizationUrl(): string
    {
        $authorizationUrl = $this->gitlab->getAuthorizationUrl();

        session()->put('oauth2state', $this->gitlab->getState());
        session()->put('oauth2pkceCode', $this->gitlab->getPkceCode());
        return $authorizationUrl;
    }

    /**
     * @throws OAuthException
     * @throws IdentityProviderException
     */
    public function callback(?string $code, ?string $state): bool
    {
        if (! $code) {
            throw new OAuthException('Missing code.', OAuthException::INVALID_CODE);
        }

        if (! $this->checkState($state)) {
            throw new OAuthException('Missing state or state mismatch.', OAuthException::INVALID_STATE);
        }

        $this->gitlab->setPkceCode(session('oauth2pkceCode'));

        $token = $this->getToken($code);
        session()->put('oauth2token', $token->jsonSerialize());

        $user = $this->getUser($token);

        if (! $user) {
            throw new OAuthException('User not found.', OAuthException::USER_NOT_FOUND);
        }

        Auth::guard('gitlab')->login($user);
        return true;
    }

    protected function checkState(?string $state): bool
    {
        if (!$state || !session('oauth2state') || $state !== session('oauth2state')) {
            session()->forget('oauth2state');
            return false;
        } else {
            return true;
        }
    }

    /**
     * @throws IdentityProviderException
     */
    protected function getToken(string $code): AccessTokenInterface
    {
        return $this->gitlab->getAccessToken('authorization_code', [
            'code' => $code,
        ]);
    }

    /**
     * @throws OAuthException
     */
    public function refreshToken(): void
    {
        if (isset(session('oauth2token', [])['expires']) && session('oauth2token')['expires'] < time()) {
            try {
                $response = $this->gitlab->getAccessToken('refresh_token', [
                    'refresh_token' => session('oauth2token')['refresh_token'],
                ]);
            } catch (IdentityProviderException $e) {
                throw new OAuthException('Failed to refresh token.', OAuthException::REFRESH_FAILED, $e);
            }

            session()->put('oauth2token', $response->jsonSerialize());
        }
    }

    protected function getUser(AccessTokenInterface $token): ?Authenticatable
    {
        $resourceOwner = $this->gitlab->getResourceOwner($token)->toArray();

        if ($resourceOwner) {
            return $this->gitlabServiceProvider->retrieveByCredentials($resourceOwner);
        } else {
            return null;
        }
    }
}
