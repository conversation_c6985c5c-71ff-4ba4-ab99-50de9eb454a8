<?php

namespace App\Auth;

use App\Model\Entities\User;
use App\Model\Facades\UserFacade;
use Illuminate\Contracts\Auth\{
    Authenticatable,
    UserProvider
};

readonly class GitlabServiceProvider implements UserProvider
{
    public function __construct(
        private UserFacade    $userFacade
    ) {
    }

    public function retrieveByCredentials(array $credentials): ?Authenticatable
    {
        $user = $this->userFacade->getUserByGitlabId($credentials['id']);

        if ($user) {
            return new GitlabUser($user);
        } else {
            $new = new User();
            $new->setName($credentials['name']);
            $new->setEmail($credentials['email']);
            $new->setGitlabId($credentials['id']);
            $new->setAvatar($credentials['avatar_url']);
            $new->setGitlabUrl($credentials['web_url']);
            $this->userFacade->saveUser($new);

            return new GitlabUser($new);
        }
    }

    public function retrieveById($identifier): ?Authenticatable
    {
        $user = $this->userFacade->getUserById($identifier);
        return $user ? new GitlabUser($user) : null;
    }

    public function retrieveByToken($identifier, $token): ?Authenticatable
    {
        throw new \LogicException(__METHOD__ . '() is not implemented for Gitlab OAuth');
    }

    public function updateRememberToken(Authenticatable $user, $token): void
    {
        throw new \LogicException(__METHOD__ . '() is not implemented for Gitlab OAuth');
    }

    public function validateCredentials(Authenticatable $user, array $credentials): bool
    {
        throw new \LogicException(__METHOD__ . '() is not implemented for Gitlab OAuth');
    }

    public function rehashPasswordIfRequired(Authenticatable $user, array $credentials, bool $force = false)
    {
        throw new \LogicException(__METHOD__ . '() is not implemented for Gitlab OAuth');
    }
}
