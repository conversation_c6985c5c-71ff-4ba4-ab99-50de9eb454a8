<?php

namespace App\Auth;

use App\Enums\Role;
use Illuminate\Contracts\Auth\Authenticatable;
use App\Model\Entities\User;

class GitlabUser implements Authenticatable, IGitlabAccessible
{
    private const string GITLAB_API_TOKEN = 'auth_gitlab_api_token';
    private const string ROLE = 'auth_role';

    public function __construct(
        private readonly User $user
    ) {
    }

    public function getAuthIdentifier(): int
    {
        return $this->user->getId();
    }

    public function getAuthIdentifierName(): string
    {
        return 'id';
    }

    public function getAuthPassword(): string
    {
        return '';
    }

    public function getRememberToken(): string
    {
        return '';
    }

    public function getRememberTokenName(): string
    {
        return '';
    }

    public function setRememberToken($value): void
    {
    }

    public function getUser(): User
    {
        return $this->user;
    }

    public function getIdentity(): User
    {
        return $this->user;
    }

    public function getRole(): Role
    {
        return session()->get(self::ROLE);
    }

    public function setRole(Role $role): self
    {
        session()->put(self::ROLE, $role);
        return $this;
    }

    public function getGitlabApiToken(): ?string
    {
        return session()->get(self::GITLAB_API_TOKEN);
    }

    public function setGitlabApiToken(?string $gitlabApiToken): self
    {
        session()->put(self::GITLAB_API_TOKEN, $gitlabApiToken);
        return $this;
    }

    public function isGitlabAccessible(): bool
    {
        return session()->has(self::GITLAB_API_TOKEN) && $this->getRole() === Role::DEVELOPER;
    }

    public function getAuthPasswordName(): false
    {
        return false;
    }
}
