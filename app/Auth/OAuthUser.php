<?php

namespace App\Auth;

use App\Enums\Role;
use Illuminate\Contracts\Auth\Authenticatable;
use App\Model\Entities\User;

class OAuthUser implements Authenticatable
{
    private const string GITLAB_API_TOKEN = 'auth_gitlab_api_token';
    private const string ROLE = 'auth_role';

    public function __construct(
        protected User $user
    ) {
    }

    public function getAuthIdentifier(): int
    {
        return $this->user->getId();
    }

    public function getAuthIdentifierName(): string
    {
        return 'id';
    }

    public function getAuthPassword(): string
    {
        return '';
    }

    public function getAuthPasswordName()
    {
        //
    }

    public function getRememberToken(): string
    {
        return '';
    }

    public function getRememberTokenName(): string
    {
        return '';
    }

    public function setRememberToken($value): void
    {
    }

    public function getUser(): User
    {
        return $this->user;
    }

    public function getIdentity(): User
    {
        return $this->user;
    }

    public function getRole(): Role
    {
        return session()->get(self::ROLE);
    }

    public function setRole(Role $role): self
    {
        session()->put(self::ROLE, $role);
        return $this;
    }

    public function isGitlabAccessible(): bool
    {
        return session()->has(self::GITLAB_API_TOKEN) && $this->getRole() === Role::DEVELOPER;
    }
}
