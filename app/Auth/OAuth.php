<?php

namespace App\Auth;

use App\Model\Entities\App;
use App\Model\Facades\AppFacade;
use DekApps\OAuth2\Client\Provider\Dek;
use Illuminate\Contracts\Auth\Authenticatable;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use League\OAuth2\Client\Provider\Exception\IdentityProviderException;
use League\OAuth2\Client\Token\AccessToken;
use League\OAuth2\Client\Token\AccessTokenInterface;

readonly class OAuth
{
    protected const string ALLOWED = 'admin';

    public function __construct(
        private Dek                  $oAuth,
        private OAuthServiceProvider $oAuthServiceProvider,
        private AppFacade $appFacade,
    ) {
    }

    /**
     * Ziska authorization URL pro presmerovani na OAuth login
     */
    public function authorizationUrl(): string
    {
        $authorizationUrl = $this->oAuth->getAuthorizationUrl(['scope' => ['user']]);

        session()->put('oauth2state', $this->oAuth->getState());
        session()->put('oauth2pkceCode', $this->oAuth->getPkceCode());
        return $authorizationUrl;
    }

    /**
     * Zpracuje redirect_url z OAuth login stranky
     * @throws OAuthException
     * @throws IdentityProviderException
     */
    public function callback(?string $code, ?string $state): bool
    {
        if (! $code) {
            throw new OAuthException('Missing code.', OAuthException::INVALID_CODE);
        }

        if (! $this->checkState($state)) {
            throw new OAuthException('Missing state or state mismatch.', OAuthException::INVALID_STATE);
        }

        $this->oAuth->setPkceCode(session('oauth2pkceCode'));

        $token = $this->getToken($code);

        $user = $this->getUser($token);

        if (! $user) {
            throw new OAuthException('User not found.', OAuthException::USER_NOT_FOUND);
        }

        Auth::guard('oauth')->login($user);
        return true;
    }

    /**
     * Overi bearer token a ziska podle neho aplikaci
     * @throws OAuthException
     * @throws IdentityProviderException
     */
    public function getAppByToken(Request $request): App
    {
        $bearer = $request->bearerToken();

        if (! $bearer) {
            throw new OAuthException('Missing token.', OAuthException::INVALID_TOKEN);
        }

        $token = new AccessToken(['access_token' => $bearer]);

        $app = $this->getApp($token);

        if (! $app) {
            throw new OAuthException('Application not found.', OAuthException::APP_NOT_FOUND);
        }

        return $app;
    }

    protected function checkState(?string $state): bool
    {
        if (!$state || !session('oauth2state') || $state !== session('oauth2state')) {
            session()->forget('oauth2state');
            return false;
        } else {
            return true;
        }
    }

    protected function getToken(string $code): AccessTokenInterface
    {
        return $this->oAuth->getAccessToken('authorization_code', [
            'code' => $code,
        ]);
    }

    protected function getUser(AccessTokenInterface $token): ?Authenticatable
    {
        $resourceOwner = $this->oAuth->getResourceOwner($token)->toArray();

        if (! in_array(self::ALLOWED, array_values($resourceOwner['groups']))) {
            return null;
        }

        return isset($resourceOwner['userid']) ? $this->oAuthServiceProvider->retrieveByCredentials($resourceOwner) : null;
    }

    protected function getApp(AccessTokenInterface $token): ?App
    {
        $resourceOwner = $this->oAuth->getResourceOwner($token)->toArray();

        $clientId = $resourceOwner['aud'][0] ?? null;
        return $clientId ? $this->appFacade->getAppByOAuth($clientId) : null;
    }
}
