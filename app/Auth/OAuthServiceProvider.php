<?php

namespace App\Auth;

use App\Model\Entities\User;
use App\Model\Facades\UserFacade;
use Illuminate\Contracts\Auth\{
    Authenticatable,
    UserProvider
};

readonly class OAuthServiceProvider implements UserProvider
{
    public function __construct(
        private UserFacade $facade
    ) {
    }

    public function retrieveByCredentials(array $credentials): ?Authenticatable
    {
        $user = $this->facade->getUserByEmail($credentials['email']);

        if (!$user) {
            $user = new User();
            $user->setName($credentials['displayname']);
            $user->setEmail($credentials['email']);

            $this->facade->saveUser($user);
        }

        return new OAuthUser($user);
    }

    public function retrieveById($identifier): ?Authenticatable
    {
        $user = $this->facade->getUserById($identifier);
        return $user ? new OAuthUser($user) : null;
    }

    public function retrieveByToken($identifier, $token): ?Authenticatable
    {
        throw new \LogicException(__METHOD__ . '() is not implemented for OAuth 2');
    }

    public function updateRememberToken(Authenticatable $user, $token): void
    {
        throw new \LogicException(__METHOD__ . '() is not implemented for OAuth 2');
    }

    public function validateCredentials(Authenticatable $user, array $credentials): bool
    {
        throw new \LogicException(__METHOD__ . '() is not implemented for OAuth 2');
    }

    public function rehashPasswordIfRequired(Authenticatable $user, array $credentials, bool $force = false)
    {
        throw new \LogicException(__METHOD__ . '() is not implemented for OAuth 2');
    }
}
