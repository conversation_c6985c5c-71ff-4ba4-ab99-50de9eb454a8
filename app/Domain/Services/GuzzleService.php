<?php

namespace App\Domain\Services;

use App\Auth\Gitlab;
use App\Model\Entities\Error;
use App\Model\Entities\Issue;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;
use Illuminate\Support\Str;
use Tracy\Debugger;

readonly class GuzzleService
{
    public function __construct(
        private Client $guzzleClient,
        private Gitlab $gitlab,
    ) {
    }

    protected function guzzleWrapper(string $method, string $url, array $options = []): mixed
    {
        $this->gitlab->refreshToken();

        $defaultOptions = [
            'headers' => [
                'Authorization' => 'Bearer ' . $this->getToken(),
            ]
        ];

        try {
            $response = $this->guzzleClient->$method($url, array_merge($defaultOptions, $options));

            return json_decode($response->getBody(), true);
        } catch (RequestException $e) {
            Debugger::log($e, Debugger::EXCEPTION);
            return null;
        }
    }

    public function postGitlabIssue(Error $error): Issue
    {
        $message = $error->getMessage();

        $response = $this->guzzleWrapper('post', config('gitlab.api_project') . $error->getApp()->getGitlabId() . '/issues', [
            'form_params' => [
                'title' => $error->getType()->getTitle() . ": " . Str::limit($message),
                'description' => "Errorlog odkaz: " . url()->previous() . (strlen($message) > 100 ? PHP_EOL . PHP_EOL . $message : ""),
                'labels' => $error->getApp()->getGitlabLabel(),
                'assignee_id' => $error->getUser()?->getGitlabId()
            ]
        ]);

        $issue = new Issue();
        $issue->setError($error);
        $issue->setIid($response['iid']);
        $issue->setLabel(implode(',', $response['labels']));

        return $issue;
    }

    public function getGitlabUser(int $id): string
    {
        return $this->guzzleWrapper('get', config('gitlab.api_user').'/'.$id)['web_url'];
    }

    public function getRepositoryInfo(int $id): ?array
    {
        return $this->guzzleWrapper('get', config('gitlab.api_project').$id);
    }

    public function getAvatar(string $email): ?string
    {
        return $this->guzzleWrapper('get', config('gitlab.api_avatar'), [
            'form_params' => [
                'email' => $email
            ]
        ])['avatar_url'] ?? null;
    }

    public function addGitlabComment(Error $error, string $content): array
    {
        return $this->guzzleWrapper('post', str_replace(['%id%', '%iid%'], [$error->getApp()->getGitlabId(), $error->getIssue()->getIid()], config('gitlab.api_comment')), [
            'form_params' => [
                'body' => $content
            ]
        ]);
    }

    public function getGitlabIssueInfo(Error $error): array
    {
        return $this->guzzleWrapper('get', str_replace(['%id%', '%iid%'], [
            $error->getApp()->getGitlabId(),
            $error->getIssue()->getIid()
        ], config('gitlab.api_issue')));
    }

    public function updateGitlabIssue(Error $error, string $stateEvent)
    {
        return $this->guzzleWrapper('put', str_replace(['%id%', '%iid%'], [$error->getApp()->getGitlabId(), $error->getIssue()->getIid()], config('gitlab.api_issue')), [
            'form_params' => [
                'state_event' => $stateEvent
            ]
        ]);
    }

    public function getGitlabNotes(Error $error): array
    {
        return $this->guzzleWrapper('get', str_replace(['%id%', '%iid%'], [$error->getApp()->getGitlabId(), $error->getIssue()->getIid()], config('gitlab.api_comment')));
    }

    public function getProjectPath(Error $error): string
    {
        return $this->guzzleWrapper('get', config('gitlab.api_project').$error->getApp()->getGitlabId())['web_url'];
    }

    protected function getToken(): string
    {
        return session('oauth2token')['access_token'];
    }
}
