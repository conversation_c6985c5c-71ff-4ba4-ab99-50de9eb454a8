<?php

namespace App\Domain\Parsers;

use App\Enums\ErrorType;
use App\Events\ErrorCreatedEvent;
use App\Model\Entities\Error;
use App\Model\Entities\Log;
use App\Model\Entities\Occurrence;
use App\Model\Entities\Trace;
use DateTimeImmutable;
use Doctrine\ORM\EntityManagerInterface;

class TracyParser implements IParser
{
    private EntityManagerInterface $entityManager;

    private string $errorLinePattern = "/\[([0-9\-\s]{19})] ([A-Za-z0-9\s\\\]+): (.*) in (.*):([0-9]+)\s+@\s+(\S*)(\s+@@\s+(.*))?/";
    private string $traceFilePattern = '#^exception--([0-9]{4}-[0-9]{2}-[0-9]{2}--[0-9]{2}-[0-9]{2})--([a-z0-9]+)\.(html)$#';

    public function setup(EntityManagerInterface $entityManager): void
    {
        $this->entityManager = $entityManager;
    }

    public function parseTrace(Log $log): bool
    {
        if (preg_match($this->traceFilePattern, $log->getFilename(), $matches)) {
            if ($this->traceExists($matches[2])) {
                return true;
            }
            $trace = new Trace();
            $trace->setName($log->getFilename());
            $trace->setContent($log->getContent());
            $trace->setExtension($matches[3]);
            $trace->setFileCreated(DateTimeImmutable::createFromFormat('Y-m-d--H-i', $matches[1]));
            $trace->setHash($matches[2]);
            $trace->setMimeType('text/html');
            $trace->setSize(strlen($log->getContent()));

            return $this->insertTrace($trace) !== null;
        }
        return false;
    }

    public function parseErrors(Log $log): bool
    {
        if (preg_match_all($this->errorLinePattern, $log->getContent(), $m, PREG_SET_ORDER)) {
            $this->transactionBegin();
            foreach ($m as $matches) {
                $occurrence = new Occurrence();
                $occurrence->setDate(DateTimeImmutable::createFromFormat('Y-m-d H-i-s', $matches[1]));

                $error = new Error();
                $error->addOccurrence($occurrence);
                $errorType = ErrorType::tryFromTitle($matches[2]);
                if ($errorType === null) {
                    // Exception
                    $error->setType(ErrorType::EXCEPTION);
                    $error->setException($matches[2]);
                } else {
                    // PHP Error
                    $error->setType($errorType);
                }

                $error->setApp($log->getApp());
                $error->setMessage($matches[3]);
                $error->setFile($matches[4]);
                $error->setLine($matches[5]);
                $error->setFilename($log->getFilename());
                $error->setUrl($matches[6]);
                $error->setOriginal($log->getContent());
                $error->createChecksum();

                if (isset($matches[8])) {
                    $traceFilename = $matches[8];
                    $trace = $this->getTraceByFilename($traceFilename);
                    $traceId = $trace['id'] ?? null;
                }

                $errorId = $this->insertError($error, $traceId ?? null);

                if ($errorId === null) {
                    return $this->transactionRollback();
                }

                $occId = $this->insertOccurrence($occurrence, $errorId);

                if ($occId === null) {
                    return $this->transactionRollback();
                }
            }
            return $this->transactionCommit();
        }
        return false;
    }

    private function transactionBegin(): void
    {
        $this->entityManager->getConnection()->executeStatement('BEGIN');
    }

    private function transactionCommit(): bool
    {
        $this->entityManager->getConnection()->executeStatement('COMMIT');
        return true;
    }

    private function transactionRollback(): bool
    {
        $this->entityManager->getConnection()->executeStatement('ROLLBACK');
        return false;
    }

    private function getTraceByFilename(string $filename): ?array
    {
        $connection = $this->entityManager->getConnection();
        $trace = $connection->prepare('SELECT * FROM trace WHERE name = ?');
        $trace->bindValue(1, $filename);
        $result = $trace->executeQuery()->fetchAllAssociative();

        return count($result) > 0 ? $result[0] : null;
    }

    private function traceExists(string $hash): bool
    {
        $connection = $this->entityManager->getConnection();
        $stmt = $connection->prepare('SELECT COUNT(*) FROM trace WHERE hash = ?');
        $stmt->bindValue(1, $hash);

        return $stmt->executeQuery()->fetchOne() > 0;
    }

    private function getErrorByChecksum(string $checksum): ?array
    {
        $connection = $this->entityManager->getConnection();
        $error = $connection->prepare('SELECT * FROM error WHERE checksum = ?');
        $error->bindValue(1, $checksum);
        $result = $error->executeQuery()->fetchAllAssociative();

        return count($result) > 0 ? $result[0] : null;
    }

    private function insertTrace(Trace $trace): ?int
    {
        $id = null;
        $connection = $this->entityManager->getConnection();

        $result = $connection->insert('trace', [
            'name' => $trace->getName(),
            'hash' => $trace->getHash(),
            'content' => $trace->getContent(),
            'extension' => $trace->getExtension(),
            'mime_type' => $trace->getMimeType(),
            'size' => $trace->getSize(),
            'file_created' => $trace->getFileCreated()->format('Y-m-d H:i:s'),
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s'),
        ]);

        if ($result === 1) {
            $id = $connection->lastInsertId();
        }

        return $id;
    }

    private function insertError(Error $error, ?int $traceId): ?int
    {
        $id = null;
        $connection = $this->entityManager->getConnection();

        $existingError = $this->getErrorByChecksum($error->getChecksum());

        if ($existingError) {
            // mark $existingError as resolved => 0 in DB
            $connection->update('error', ['resolved' => 0, 'user_id' => null], ['id' => $existingError['id']]);

            $id = $existingError['id'];
        } else {
            $result = $connection->insert('error', [
                'type' => $error->getType()->value,
                'message' => $error->getMessage(),
                'file' => $error->getFile(),
                'line' => $error->getLine(),
                'original' => $error->getOriginal(),
                'url' => $error->getUrl(),
                'checksum' => $error->getChecksum(),
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
                'app_id' => $error->getApp()->getId(),
                'filename' => $error->getFilename(),
                'exception' => $error->getException(),
                'resolved' => 0,
                'trace_id' => $traceId
            ]);

            if ($result === 1) {
                $id = $connection->lastInsertId();
            }

            ErrorCreatedEvent::dispatch($id);
        }

        return $id;
    }

    private function insertOccurrence(Occurrence $occurrence, int $errorId): ?int
    {
        $id = null;
        $connection = $this->entityManager->getConnection();

        $result = $connection->insert('occurrence', [
            'error_id' => $errorId,
            'date' => $occurrence->getDate()->format('Y-m-d H:i:s'),
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s'),
        ]);

        if ($result === 1) {
            $id = $connection->lastInsertId();
        }

        return $id;
    }
}
