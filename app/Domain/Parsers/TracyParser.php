<?php

namespace App\Domain\Parsers;

use App\Enums\ErrorType;
use App\Events\ErrorCreatedEvent;
use App\Model\Entities\Error;
use App\Model\Entities\Log;
use App\Model\Entities\Occurrence;
use App\Model\Entities\Trace;
use DateTimeImmutable;
use Doctrine\ORM\EntityManagerInterface;

class TracyParser implements IParser
{
    private EntityManagerInterface $entityManager;
    private bool $debug = true;

    private string $errorLinePattern = "/\[([0-9\-\s]{19})] ([A-Za-z0-9\s\\\]+): (.*) in (.*):([0-9]+)\s+@\s+(\S*)(\s+@@\s+(.*))?/";
    private string $traceFilePattern = '#^exception--([0-9]{4}-[0-9]{2}-[0-9]{2}--[0-9]{2}-[0-9]{2})--([a-z0-9]+)\.(html)$#';

    public function setup(EntityManagerInterface $entityManager): void
    {
        $this->debug("TracyParser: Setting up with EntityManager");
        $this->entityManager = $entityManager;
        $this->debug("TracyParser: Setup complete");
    }

    private function debug(string $message): void
    {
        if ($this->debug) {
            echo "DEBUG: " . $message . "\n";
        }
    }

    public function parseTrace(Log $log): bool
    {
        $this->debug("TracyParser: Starting parseTrace for log {$log->getId()}, filename: {$log->getFilename()}");

        if (preg_match($this->traceFilePattern, $log->getFilename(), $matches)) {
            $this->debug("TracyParser: Filename matches trace pattern");

            if ($this->traceExists($matches[2])) {
                $this->debug("TracyParser: Trace already exists, returning true");
                return true;
            }

            $this->debug("TracyParser: Getting content size for trace");
            $contentSize = $this->getLogContentSize($log->getId());
            $this->debug("TracyParser: Content size: " . round($contentSize / 1024, 2) . " KB");

            if ($contentSize > 10 * 1024 * 1024) {
                $this->debug("TracyParser: Trace too large, skipping");
                return false;
            }

            $this->debug("TracyParser: Loading trace content safely");
            $content = $this->getLogContentSafely($log->getId());
            if (!$content) {
                $this->debug("TracyParser: Failed to load content");
                return false;
            }

            $this->debug("TracyParser: Creating trace entity");
            $trace = new Trace();
            $trace->setName($log->getFilename());
            $trace->setContent($content);
            $trace->setExtension($matches[3]);
            $trace->setFileCreated(DateTimeImmutable::createFromFormat('Y-m-d--H-i', $matches[1]));
            $trace->setHash($matches[2]);
            $trace->setMimeType('text/html');
            $trace->setSize(strlen($content));

            $this->debug("TracyParser: Inserting trace");
            $result = $this->insertTrace($trace) !== null;

            unset($content);
            $this->debug("TracyParser: Trace processing complete, result: " . ($result ? 'SUCCESS' : 'FAILED'));

            return $result;
        }

        $this->debug("TracyParser: Filename does not match trace pattern");
        return false;
    }

    public function parseErrors(Log $log): bool
    {
        $this->debug("TracyParser: Starting parseErrors for log {$log->getId()}");

        // Get content size first to check if it's safe to process
        $this->debug("TracyParser: Getting content size");
        $contentSize = $this->getLogContentSize($log->getId());
        $this->debug("TracyParser: Content size: " . round($contentSize / 1024, 2) . " KB");

        // Skip very large logs to prevent memory issues
        if ($contentSize > 5 * 1024 * 1024) { // 5MB limit
            $this->debug("TracyParser: Log too large for error parsing, skipping");
            return false;
        }

        // For smaller logs, process normally but with memory management
        $this->debug("TracyParser: Loading content safely for error parsing");
        $content = $this->getLogContentSafely($log->getId());
        if (!$content) {
            $this->debug("TracyParser: Failed to load content for error parsing");
            return false;
        }

        $this->debug("TracyParser: Content loaded, size: " . strlen($content) . " bytes");
        $this->debug("TracyParser: Running regex pattern match");

        if (preg_match_all($this->errorLinePattern, $content, $m, PREG_SET_ORDER)) {
            $this->debug("TracyParser: Found " . count($m) . " error matches");
            $this->debug("TracyParser: Beginning transaction");
            $this->transactionBegin();

            foreach ($m as $index => $matches) {
                $this->debug("TracyParser: Processing error match " . ($index + 1) . "/" . count($m));
                try {
                    $this->processErrorMatch($matches, $log, $content);
                    $this->debug("TracyParser: Error match " . ($index + 1) . " processed successfully");
                } catch (\Exception $e) {
                    $this->debug("TracyParser: Error match " . ($index + 1) . " failed: " . $e->getMessage());
                    // Continue with other matches if one fails
                    continue;
                }
            }

            // Clear content from memory immediately
            unset($content);
            $this->debug("TracyParser: Content cleared from memory");

            $this->debug("TracyParser: Committing transaction");
            return $this->transactionCommit();
        }

        $this->debug("TracyParser: No error matches found");
        // Clear content from memory
        unset($content);
        return false;
    }

    /**
     * Process a single error match
     */
    private function processErrorMatch(array $matches, Log $log, string $content): void
    {
        $this->debug("TracyParser: Creating occurrence from date: {$matches[1]}");
        $occurrence = new Occurrence();
        $occurrence->setDate(DateTimeImmutable::createFromFormat('Y-m-d H-i-s', $matches[1]));

        $this->debug("TracyParser: Creating error entity");
        $error = new Error();
        $error->addOccurrence($occurrence);

        $this->debug("TracyParser: Determining error type for: {$matches[2]}");
        $errorType = ErrorType::tryFromTitle($matches[2]);
        if ($errorType === null) {
            $this->debug("TracyParser: Setting as EXCEPTION");
            $error->setType(ErrorType::EXCEPTION);
            $error->setException($matches[2]);
        } else {
            $this->debug("TracyParser: Setting error type: {$errorType->name}");
            $error->setType($errorType);
        }

        $this->debug("TracyParser: Setting error properties");
        $error->setApp($log->getApp());
        $error->setMessage($matches[3]);
        $error->setFile($matches[4]);
        $error->setLine($matches[5]);
        $error->setFilename($log->getFilename());
        $error->setUrl($matches[6]);

        $this->debug("TracyParser: Setting original content (size: " . strlen($content) . " bytes)");
        $error->setOriginal($content);

        $this->debug("TracyParser: Creating checksum");
        $error->createChecksum();

        $traceId = null;
        if (isset($matches[8])) {
            $this->debug("TracyParser: Looking for trace: {$matches[8]}");
            $trace = $this->getTraceByFilename($matches[8]);
            $traceId = $trace['id'] ?? null;
            $this->debug("TracyParser: Trace ID: " . ($traceId ?? 'not found'));
        }

        $this->debug("TracyParser: Inserting error");
        $errorId = $this->insertError($error, $traceId);
        if ($errorId === null) {
            throw new \Exception("Failed to insert error");
        }
        $this->debug("TracyParser: Error inserted with ID: {$errorId}");

        $this->debug("TracyParser: Inserting occurrence");
        $occId = $this->insertOccurrence($occurrence, $errorId);
        if ($occId === null) {
            throw new \Exception("Failed to insert occurrence");
        }
        $this->debug("TracyParser: Occurrence inserted with ID: {$occId}");
    }

    /**
     * Get log content size without loading content
     */
    private function getLogContentSize(int $logId): int
    {
        $connection = $this->entityManager->getConnection();
        $sql = 'SELECT LENGTH(content) as size FROM log WHERE id = ?';
        $stmt = $connection->prepare($sql);
        $stmt->bindValue(1, $logId);
        return (int) $stmt->executeQuery()->fetchOne();
    }

    /**
     * Get log content safely with memory management
     */
    private function getLogContentSafely(int $logId): ?string
    {
        try {
            $connection = $this->entityManager->getConnection();
            $sql = 'SELECT content FROM log WHERE id = ?';
            $stmt = $connection->prepare($sql);
            $stmt->bindValue(1, $logId);
            $result = $stmt->executeQuery()->fetchOne();

            if (is_resource($result)) {
                $content = stream_get_contents($result);
                fclose($result);
                return $content;
            }

            return $result;
        } catch (\Exception $e) {
            return null;
        }
    }

    private function transactionBegin(): void
    {
        $this->entityManager->getConnection()->executeStatement('BEGIN');
    }

    private function transactionCommit(): bool
    {
        $this->entityManager->getConnection()->executeStatement('COMMIT');
        return true;
    }

    private function transactionRollback(): bool
    {
        $this->entityManager->getConnection()->executeStatement('ROLLBACK');
        return false;
    }

    private function getTraceByFilename(string $filename): ?array
    {
        $connection = $this->entityManager->getConnection();
        $trace = $connection->prepare('SELECT * FROM trace WHERE name = ?');
        $trace->bindValue(1, $filename);
        $result = $trace->executeQuery()->fetchAllAssociative();

        return count($result) > 0 ? $result[0] : null;
    }

    private function traceExists(string $hash): bool
    {
        $connection = $this->entityManager->getConnection();
        $stmt = $connection->prepare('SELECT COUNT(*) FROM trace WHERE hash = ?');
        $stmt->bindValue(1, $hash);

        return $stmt->executeQuery()->fetchOne() > 0;
    }

    private function getErrorByChecksum(string $checksum): ?array
    {
        $connection = $this->entityManager->getConnection();
        $error = $connection->prepare('SELECT * FROM error WHERE checksum = ?');
        $error->bindValue(1, $checksum);
        $result = $error->executeQuery()->fetchAllAssociative();

        return count($result) > 0 ? $result[0] : null;
    }

    private function insertTrace(Trace $trace): ?int
    {
        $id = null;
        $connection = $this->entityManager->getConnection();

        $result = $connection->insert('trace', [
            'name' => $trace->getName(),
            'hash' => $trace->getHash(),
            'content' => $trace->getContent(),
            'extension' => $trace->getExtension(),
            'mime_type' => $trace->getMimeType(),
            'size' => $trace->getSize(),
            'file_created' => $trace->getFileCreated()->format('Y-m-d H:i:s'),
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s'),
        ]);

        if ($result === 1) {
            $id = $connection->lastInsertId();
        }

        return $id;
    }

    private function insertError(Error $error, ?int $traceId): ?int
    {
        $id = null;
        $connection = $this->entityManager->getConnection();

        $existingError = $this->getErrorByChecksum($error->getChecksum());

        if ($existingError) {
            // mark $existingError as resolved => 0 in DB
            $connection->update('error', ['resolved' => 0, 'user_id' => null], ['id' => $existingError['id']]);

            $id = $existingError['id'];
        } else {
            $result = $connection->insert('error', [
                'type' => $error->getType()->value,
                'message' => $error->getMessage(),
                'file' => $error->getFile(),
                'line' => $error->getLine(),
                'original' => $error->getOriginal(),
                'url' => $error->getUrl(),
                'checksum' => $error->getChecksum(),
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
                'app_id' => $error->getApp()->getId(),
                'filename' => $error->getFilename(),
                'exception' => $error->getException(),
                'resolved' => 0,
                'trace_id' => $traceId
            ]);

            if ($result === 1) {
                $id = $connection->lastInsertId();
            }

            ErrorCreatedEvent::dispatch($id);
        }

        return $id;
    }

    private function insertOccurrence(Occurrence $occurrence, int $errorId): ?int
    {
        $id = null;
        $connection = $this->entityManager->getConnection();

        $result = $connection->insert('occurrence', [
            'error_id' => $errorId,
            'date' => $occurrence->getDate()->format('Y-m-d H:i:s'),
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s'),
        ]);

        if ($result === 1) {
            $id = $connection->lastInsertId();
        }

        return $id;
    }
}
