<?php

namespace App\Domain\Parsers;

use App\Enums\Logger;
use LogicException;

class ParserFactory
{
    private array $parsers;
    private array $instances = [];

    public function __construct()
    {
        $this->parsers = [
            Logger::TRACY->value => TracyParser::class
        ];
    }

    public function createParser(Logger $logger)
    {
        if (!isset($this->parsers[$logger->value])) {
            throw new LogicException("Parser not found");
        }
        $class = $this->parsers[$logger->value];
        if (!isset($this->instances[$class])) {
            $this->instances[$class] = new $class();
        }
        return $this->instances[$class];
    }
}
