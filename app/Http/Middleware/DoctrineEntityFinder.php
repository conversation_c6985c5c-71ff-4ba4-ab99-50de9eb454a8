<?php

namespace App\Http\Middleware;

use Closure;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\EntityRepository;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class DoctrineEntityFinder
{
    /**
     * Handle an incoming request.
     *
     * @param \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response) $next
     * @throws \ReflectionException
     */
    public function handle(Request $request, Closure $next): Response
    {
        $params = $this->getRouterParameters($request);
        $controllerParameters = $this->getControllerParameters($request);

        foreach ($controllerParameters as $parameter) {
            $name = $parameter->getName();
            if (isset($params[$name])) {
                $className = $parameter->getType()->getName();
                $id = $params[$name];
                if (class_exists($className, false)) {
                    $repo = $this->getRepository($className);
                    $entity = $repo->find($id);
                    abort_if($entity === null, 404);
                    $request->route()->setParameter($parameter->getName(), $entity);
                }
            }
        }

        return $next($request);
    }

    protected function getRouterParameters(Request $request): array
    {
        return $request->route()->parameters();
    }

    protected function getControllerParameters(Request $request): array
    {
        $uses = $request->route()->getAction('uses');
        if (is_string($uses)) {
            list($controller, $method) = explode('@', $request->route()->getAction('controller'));
            $rc = new \ReflectionClass($controller);
            $func = $rc->getMethod($method);
        } else {
            $func = new \ReflectionFunction($uses);
        }
        return $func->getParameters();
    }

    protected function getRepository(string $className): EntityRepository
    {
        return app(EntityManagerInterface::class)->getRepository($className);
    }
}
