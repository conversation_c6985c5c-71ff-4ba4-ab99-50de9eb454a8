<?php

namespace App\Http\Controllers;

use App\Enums\ErrorStatus;
use App\Model\Entities\Error;
use App\Model\Facades\AppFacade;
use App\Model\Facades\ErrorFacade;
use App\View\Components\FlashMessage\Message;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\View\View;

class ErrorController extends Controller
{
    protected const int PAGINATE_PAGE = 15;

    public function __construct(
        private readonly ErrorFacade $errorFacade,
        private readonly AppFacade   $appFacade
    ) {
    }

    public function index(Request $request): View
    {
        $page = $this->getPage();
        $statuses = $this->stateToStatusEnums($request->get('state'));
        $query = $request->get('q');

        return $query ? $this->searchErrors($query, $statuses, $page) : $this->allErrors($statuses, $page);
    }

    private function allErrors(array $statuses, int $page): View
    {
        $user = Auth::user()->getAuthIdentifier();

        $errors = $this->errorFacade->getErrorsByUser($user, $statuses, self::PAGINATE_PAGE, $page);
        $paginatedErrors = $this->paginateWithTotal($errors['items'], $errors['total'], self::PAGINATE_PAGE, $page)->withPath(url()->current());

        $data = [
            'application' => null,
            'paginatedErrors' => $paginatedErrors,
            'countUnresolved' => $this->errorFacade->countErrorsByUser($user, [ErrorStatus::OPEN]),
            'countAssigned' => $this->errorFacade->countErrorsByUser($user, [ErrorStatus::ASSIGNED]),
            'countResolved' => $this->errorFacade->countErrorsByUser($user, [ErrorStatus::RESOLVED]),
            'countTotal' => $this->errorFacade->countErrorsByUser($user, [])
        ];

        return view('homepage', $data);
    }

    private function searchErrors(string $query, array $statuses, int $page): View
    {
        $errors = $this->errorFacade->searchErrors($query, $statuses, self::PAGINATE_PAGE, $page);
        $paginatedErrors = $this->paginateWithTotal($errors['items'], $errors['total'], self::PAGINATE_PAGE, $page)->withPath(url()->current());

        $data = [
            'application' => null,
            'paginatedErrors' => $paginatedErrors,
            'countUnresolved' => $this->errorFacade->countFoundErrors($query, [ErrorStatus::OPEN]),
            'countAssigned' => $this->errorFacade->countFoundErrors($query, [ErrorStatus::ASSIGNED]),
            'countResolved' => $this->errorFacade->countFoundErrors($query, [ErrorStatus::RESOLVED]),
            'countTotal' => $this->errorFacade->countFoundErrors($query, [])
        ];

        return view('homepage', $data);
    }

    public function appErrors(Request $request, string $appSlug): View
    {
        $app = $this->appFacade->getAppBySlug($appSlug);

        if (!$app) {
            abort(404);
        }

        $statuses = $this->stateToStatusEnums($request->get('state'));

        $page = $this->getPage();
        $errors = $this->errorFacade->getErrorsByApp($app, $statuses, self::PAGINATE_PAGE, $page);
        $paginatedErrors = $this->paginateWithTotal($errors['items'], $errors['total'], self::PAGINATE_PAGE, $page)->withPath(url()->current());

        $data = [
            'application' => $app,
            'paginatedErrors' => $paginatedErrors,
            'countUnresolved' => $this->errorFacade->countErrorsByApp($app, [ErrorStatus::OPEN]),
            'countAssigned' => $this->errorFacade->countErrorsByApp($app, [ErrorStatus::ASSIGNED]),
            'countResolved' => $this->errorFacade->countErrorsByApp($app, [ErrorStatus::RESOLVED]),
            'countTotal' => $this->errorFacade->countErrorsByApp($app, [])
        ];

        return view('homepage', $data);
    }

    public function children(Error $error): View
    {
        $children = $this->errorFacade->getDependantErrors($error);

        return view('homepage', [
            'application' => null,
            'paginatedErrors' => $this->paginateWithTotal($children, count($children), self::PAGINATE_PAGE, 1)->withPath(url()->current())
        ]);
    }

    public function addErrorDependant(Request $request): RedirectResponse
    {
        $childError = $this->errorFacade->getErrorById($request->get('child-error'));
        $parentError = $this->errorFacade->getErrorById($request->get('parent-error'));
        if (!$childError || !$parentError) {
            abort(404);
        } elseif ($childError === $parentError) {
            $this->flashMessage(Message::error('Nelze vytvořit závislost na stejné chybě.'));
            return back();
        } else {
            $parentError->addDependant($childError);
            $this->errorFacade->saveError($parentError);

            $childError->setDependsOn($parentError);
            $this->errorFacade->saveError($childError);
        }

        $this->flashMessage(Message::success('Bylo vytvořeno spojení mezi chybami '.$parentError->getId().' a '.$childError->getId()));
        return back();
    }

    /**
     * @param ?string $state
     * @return ErrorStatus[]
     */
    private function stateToStatusEnums(?string $state): array
    {
        if ($state === 'all') {
            return [];
        } elseif (! is_null($state) && $status = ErrorStatus::tryFrom($state)) {
            return [$status];
        } else {
            return [ErrorStatus::OPEN, ErrorStatus::ASSIGNED];
        }
    }
}
