<?php

namespace App\Http\Controllers;

use App\View\Components\FlashMessage\Message;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Foundation\Bus\DispatchesJobs;
use Illuminate\Foundation\Validation\ValidatesRequests;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Pagination\Paginator;
use Illuminate\Routing\Controller as BaseController;
use Illuminate\Support\Collection;

class Controller extends BaseController
{
    use AuthorizesRequests;
    use DispatchesJobs;
    use ValidatesRequests;

    protected function flashMessage(Message $message): void
    {
        session()->flash(Message::SESS_KEY, $message);
    }

    protected function getPage(): int
    {
        return $page = max(1, request()->integer('page', 1));
    }

    public function paginate(array $items, int $perPage = 5, ?int $page = null, array $options = []): LengthAwarePaginator
    {
        $page = $page ?: (Paginator::resolveCurrentPage() ?: 1);
        $items = Collection::make($items);
        return new LengthAwarePaginator($items->forPage($page, $perPage), $items->count(), $perPage, $page, $options);
    }

    protected function paginateWithTotal(array $items, int $total, int $perPage = 15, int $page = 1, array $options = []): LengthAwarePaginator
    {
        $page = $page ?: (Paginator::resolveCurrentPage() ?: 1);
        return new LengthAwarePaginator($items, $total, $perPage, $page, $options);
    }
}
