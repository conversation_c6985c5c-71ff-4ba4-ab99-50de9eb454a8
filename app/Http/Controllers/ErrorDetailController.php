<?php

namespace App\Http\Controllers;

use App\Components\Calendar\Calendar;
use App\Domain\Services\GuzzleService;
use App\Enums\Action;
use App\Events\ActivityEvent;
use App\Model\Entities\Activity;
use App\Model\Entities\Comment;
use App\Model\Entities\Error;
use App\Model\Facades\ErrorFacade;
use App\Model\Facades\UserFacade;
use App\View\Components\FlashMessage\Message;
use GuzzleHttp\Exception\RequestException;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Auth;
use Illuminate\View\View;
use Parsedown;

class ErrorDetailController extends Controller
{
    public function __construct(
        private readonly ErrorFacade $errorFacade,
        private readonly UserFacade $userFacade,
        private readonly GuzzleService $guzzle
    ) {
    }
    public function detail(Calendar $calendar, Error $error): View
    {
        $isDev = Auth::guard('gitlab')->check();

        $calendar->init($error->getOccurrences(), $error);
        $occurrencesCount = count($error->getOccurrences());
        $lastOcc = $error->getOccurrences()[$occurrencesCount-1]->getDate()->format('d.m.Y \v H:i');

        if ($error->getIssue() && $isDev) {
            $issueInfo = $this->guzzle->getGitlabIssueInfo($error);
            try {
                $comments = array_reverse($this->guzzle->getGitlabNotes($error));
                foreach ($comments as &$comment) {
                    if (!$comment['system']) {
                        $parseDown = new Parsedown();
                        $comment['body'] = $parseDown->text($comment['body']);
                    }
                }
            } catch (RequestException) {
                $comments = [];
            }
        }

        return view('error.detail', [
            'error' => $error,
            'calendar' => $calendar,
            'lastOcc' => $lastOcc,
            'occurrencesCount' => $occurrencesCount,
            'users' => $this->userFacade->getUsers(),
            'user' => $error->getUser(),
            'comments' => $comments ?? [],
            'issueState' => $issueInfo ?? [],
            'gurl' => $isDev ? $this->guzzle->getProjectPath($error) : null,
        ]);
    }

    public function performAction(Request $request, Error $error, string $action): RedirectResponse
    {
        switch ($action) {
            case 'resolve':
                // Označit chybu jako vyřešenou
                $error->resolve();

                // Vyřešit i všechny potomky
                Artisan::call('errorlog:recursive-resolve', [
                    'errorId' => $error->getId(),
                ]);

                // Zavřít Gitlab issue
                $error->getIssue() && $this->closeIssue($error);

                // Nastavit komentář
                $comment = new Comment();
                $comment->setError($error);
                $comment->setUser($error->getUser());
                $comment->setText($request->filled('comment') ? Parsedown::instance()->line($request->input('comment')) : null);
                $this->errorFacade->saveComment($comment);

                // Uložit chybu
                $this->errorFacade->saveError($error);

                // Zaznamenat aktivitu
                $activity = new Activity();
                $activity->setError($error);
                $activity->setUser($error->getUser());
                $activity->setAction(Action::RESOLVED_ERROR);
                $activity->setActivityEntity('Error');
                $activity->setActivityEntityId($error->getId());
                $activity->setActivityDetail([]);
                ActivityEvent::dispatch($activity);

                return back()->with(Message::SESS_KEY, Message::success('Chyba byla označena jako vyřešená'));

            case 'unresolve':
                $error->unresolve();
                $error->getIssue() && $this->reopenIssue($error);
                $this->errorFacade->saveError($error);
                return back()->with(Message::SESS_KEY, Message::success('Chyba byla označena jako nevyřešená'));

            case 'create-gitlab-issue':
                if (!$error->getUser()) {
                    $error->setUser(Auth::guard('gitlab')->user()->getIdentity());
                    session()->flash(Message::SESS_KEY, Message::warning('Chyba neměla řešitele, byl přiřazen automaticky.'));
                } else {
                    session()->flash(Message::SESS_KEY, Message::success('Gitlab issue byla úspěšně vytvořena.'));
                }

                try {
                    $issue = $this->guzzle->postGitlabIssue($error);
                    $this->errorFacade->saveIssue($issue);

                    $activity = new Activity();
                    $activity->setError($error);
                    $activity->setUser($error->getUser());
                    $activity->setAction(Action::CREATE_ISSUE);
                    $activity->setActivityEntity('Issue');
                    $activity->setActivityEntityId($issue->getIid());
                    $activity->setActivityDetail(null);
                    ActivityEvent::dispatch($activity);

                    return back();
                } catch (RequestException $e) {
                    return back()->with(Message::SESS_KEY, Message::error('Gitlab: ' . $e->getMessage() . ', kód: ' . $e->getCode()));
                }

            case 'close':
                try {
                    $this->closeIssue($error);
                    return back()->with(Message::SESS_KEY, Message::success('Issue s ID ' . $error->getIssue()->getId() . ' byla zavřena.'));
                } catch (RequestException $e) {
                    return back()->with(Message::SESS_KEY, Message::error('Gitlab: ' . $e->getMessage() . ', kód: ' . $e->getCode()));
                }

            case 'reopen':
                try {
                    $this->reopenIssue($error);
                    return back()->with(Message::SESS_KEY, Message::success('Issue s ID ' . $error->getIssue()->getId() . ' byla znovu otevřena.'));
                } catch (RequestException $e) {
                    return back()->with(Message::SESS_KEY, Message::error('Gitlab: ' . $e->getMessage() . ', kód: ' . $e->getCode()));
                }

            case 'own':
                if ($request->has('uid')) {
                    $user = $this->userFacade->getUserById($request->get('uid'));
                } else {
                    $user = Auth::guard('gitlab')->user()->getIdentity();
                }
                $error->setUser($user);
                $this->errorFacade->saveError($error);
                return back()->with(Message::SESS_KEY, Message::success('Vlastník byl úspěšně přiřazen.'));

            case 'disown':
                $error->unsetUser();
                $this->errorFacade->saveError($error);
                return back()->with(Message::SESS_KEY, Message::success('Vlastník byl úspěšně odebrán.'));

            case 'add-comment':
                dd('geasgaeh');
                try {
                    if (!$request->has('comment') || empty($request->get('comment'))) {
                        return back()->with(Message::SESS_KEY, Message::error('Komentář nesmí být prázdný.'));
                    }
                    $this->guzzle->addGitlabComment($error, $request->get('comment'));
                    return back()->with(Message::SESS_KEY, Message::success('Komentář byl úspěšně přidán.'));
                } catch (RequestException $e) {
                    return back()->with(Message::SESS_KEY, Message::error('Gitlab: ' . $e->getMessage() . ', kód: ' . $e->getCode()));
                }

            default:
                abort(404);
        }
    }

    public function trace(Error $error): Response
    {
        $trace = $error->getTrace();
        abort_if(!$trace, 404);

        $content = $trace->getContent();
        $mimeType = $trace->getMimeType();

        return response($content, 200)
            ->header('Content-Type', $mimeType);
    }

    protected function closeIssue(Error $error): void
    {
        $this->guzzle->updateGitlabIssue($error, 'close');
    }

    protected function reopenIssue(Error $error): void
    {
        $this->guzzle->updateGitlabIssue($error, 'reopen');
    }
}
