<?php

namespace App\Http\Controllers;

use App\Domain\Services\GuzzleService;
use App\Model\Entities\User;
use App\Model\Facades\UserFacade;
use App\View\Components\FlashMessage\Message;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\View\View;

class UserController extends Controller
{
    protected const int PAGINATE_PAGE = 10;

    public function __construct(
        private readonly UserFacade $userFacade,
        private readonly GuzzleService $guzzle
    ) {
    }

    public function index(): View
    {
        $users = $this->paginate($this->userFacade->getUsers(), self::PAGINATE_PAGE);

        return view('users.index', compact('users'));
    }

    public function create(): View
    {
        return view('users.edit', ['user' => null]);
    }

    public function store(Request $request): RedirectResponse
    {
        return $this->createOrUpdate($request);
    }

    public function edit(User $user): View
    {
        return view('users.edit', compact('user'));
    }

    public function update(Request $request, User $user): RedirectResponse
    {
        return $this->createOrUpdate($request, $user);
    }

    public function destroy(User $user): RedirectResponse
    {
        $this->userFacade->deleteUser($user);

        $this->flashMessage(Message::success('Uživatel byl úspěšně smazán.'));
        return to_route('users.index');
    }

    private function createOrUpdate(Request $request, ?User $user = null)
    {
        if ($user === null) {
            $user = new User();
            $success = 'Uživatel byl úspěšně přidán';
        } else {
            $success = 'Uživatel byl úspěšně upraven';
        }

        $user->setName($request->input('name'));
        $user->setEmail($request->input('email'));
        $user->setGitlabId($request->input('gid'));
        $user->setAvatar($this->guzzle->getAvatar($user->getEmail()));
        $user->setGitlabUrl($this->guzzle->getGitlabUser($user->getGitlabId()));

        $duplicity = $this->userFacade->hasDuplicity($user, $user->getId());
        if ($duplicity) {
            $errors = [];
            if (in_array('email', $duplicity)) {
                $errors['email'] = 'Uživatel s tímto emailem již existuje';
            }
            if (in_array('gitlabId', $duplicity)) {
                $errors['email'] = 'Uživatel s tímto Gitlab ID již existuje';
            }
            return back()->withInput()->withErrors($errors);
        }

        $this->userFacade->saveUser($user);

        $this->flashMessage(Message::success($success));
        return to_route('users.index');
    }
}
