<?php

namespace App\Http\Controllers;

use App\Auth\Gitlab;
use App\Auth\OAuth;
use App\Auth\OAuthException;
use App\View\Components\FlashMessage\Message;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\View\View;
use League\OAuth2\Client\Provider\Exception\IdentityProviderException;
use Tracy\Debugger;

class AuthController extends Controller
{
    public function __construct(
        private readonly OAuth $oauth,
        private readonly Gitlab $gitlab
    ) {
    }

    public function login(): View
    {
        return view('auth/login');
    }

    public function authenticate(): RedirectResponse
    {
        $authorizationUrl = $this->oauth->authorizationUrl();

        return redirect($authorizationUrl);
    }

    public function handleCallback(Request $request): RedirectResponse
    {
        try {
            $this->oauth->callback($request->input('code'), $request->input('state'));
            return redirect()->intended();
        } catch (OAuthException | IdentityProviderException $e) {
            if ($e instanceof OAuthException && $e->getCode() === OAuthException::INVALID_CODE) {
                return $this->authenticate();
            }

            Debugger::log($e, Debugger::EXCEPTION);
            $this->flashMessage(Message::error('Přihlášení se nezdařilo. Zkuste to prosím později.'));

            return to_route('login');
        }
    }

    public function authenticateGitlab(Request $request): RedirectResponse
    {
        $authorizationUrl = $this->gitlab->authorizationUrl();

        return redirect($authorizationUrl);
        //        $credentials = [
        //            'username' => $request->post('username'),
        //            'password' => $request->post('password')
        //        ];
        //
        //        if (Auth::guard('gitlab')->attempt($credentials)) {
        //            $this->gitlab->setUser($this->facade->getUserByUsername($credentials['username']));
        //            $request->session()->regenerate();
        //
        //            $this->flashMessage(Message::success('Přihlášení proběhlo úspěšně.'));
        //            return redirect()->intended();
        //        } else {
        //            $this->flashMessage(Message::error('Neplatné přihlašovací údaje.'));
        //            return back();
        //        }
    }

    public function handleGitlabCallback(Request $request): RedirectResponse
    {
        try {
            $this->gitlab->callback($request->input('code'), $request->input('state'));
            return redirect()->intended();
        } catch (OAuthException | IdentityProviderException $e) {
            Debugger::log($e, Debugger::EXCEPTION);
            $this->flashMessage(Message::error('Přihlášení se nezdařilo. Zkuste to prosím později.'));

            return to_route('login');
        }
    }

    public function logout(Request $request): RedirectResponse
    {
        Auth::logout();

        $request->session()->invalidate();
        $request->session()->regenerateToken();

        $this->flashMessage(Message::success('Odhlášení proběhlo úspěšně.'));
        return to_route('login');
    }

    //    public function authorizeGitlab(Request $request): RedirectResponse
    //    {
    //        $user = Auth::user();
    //
    //        $userEntity = $user->getUser();
    //        $userEntity->setGitlabId($request->integer('gid'));
    //        $this->facade->saveUser($userEntity);
    //
    //        $user->setRole(Role::DEVELOPER);
    //        $user->setGitlabApiToken($request->input('gat'));
    //
    //        return to_route('homepage');
    //    }
}
