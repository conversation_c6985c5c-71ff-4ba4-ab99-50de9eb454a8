<?php

namespace App\Http\Controllers;

use App\Domain\Services\GuzzleService;
use App\Enums\SSHAlgo;
use App\Http\Requests\AppRequest;
use App\Model\Entities\App;
use App\Model\Facades\AppFacade;
use App\Model\Facades\ErrorFacade;
use App\Model\Facades\UserFacade;
use App\View\Components\FlashMessage\Message;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;
use Illuminate\View\View;

class AppController extends Controller
{
    protected const int PAGINATE_PAGE = 8;

    public function __construct(
        private readonly AppFacade   $appFacade,
        private readonly ErrorFacade $errorFacade,
        private readonly GuzzleService $guzzle,
        private readonly UserFacade $userFacade,
    ) {
    }

    public function index(Request $request): View
    {
        $user = Auth::user()->getUser();
        $page = $this->getPage();
        $sorted = $request->get('sort');

        // Save user's sort preference if provided
        if ($request->filled('sort') || $user->getAppFilter()) {
            $sorted = $request->input('sort', $user->getAppFilter());
            $user->setAppFilter($sorted);
            $this->userFacade->saveUser($user);
        }

        // Get paginated apps
        $result = $this->appFacade->getApps($sorted, self::PAGINATE_PAGE, $page);
        $apps = $this->paginateWithTotal($result['items'], $result['total'], self::PAGINATE_PAGE, $page);

        // Count errors for each app
        $counted = $this->errorFacade->countErrorsByApps($apps->items());

        return view('apps.index', compact('apps', 'counted', 'sorted'));
    }

    public function create(): View
    {
        return view('apps.new');
    }

    public function store(AppRequest $request): RedirectResponse
    {
        if ($this->appFacade->getAppByGitlabId($request->integer('gid')) !== null) {
            $this->flashMessage(Message::error('Aplikace s tímto GitLab ID již existuje.'));
            return back()->withInput();
        } else {
            $app = new App();
            $this->updateApp($request, $app);

            $this->flashMessage(Message::success('Aplikace byla úspěšně přidána.'));
            return to_route('apps.index');
        }
    }

    public function edit(App $app): View
    {
        return view('apps.edit', compact('app'));
    }

    public function update(AppRequest $request, App $app): RedirectResponse
    {
        $this->updateApp($request, $app);

        $this->flashMessage(Message::success('Aplikace byla úspěšně aktualizována.'));
        return to_route('apps.index');
    }

    public function showErrorLog(Request $request, string $appSlug): View
    {
        $app = $this->retrieveAppBySlug($appSlug);

        $page = $this->getPage();
        $perPage = 50;

        $occurrences = $this->errorFacade->getErrorOccurrences($app, $perPage, $page);

        $lines = [];
        foreach ($occurrences['items'] as $occurrence) {
            $error = $occurrence->getError();

            $style = $error->isResolved() ? 'resolved' : ($error->getUser() ? 'assigned' : '');
            $current = $error->getId() === ((int) $request->get('error'));

            $msg = $occurrence->getLogLine();

            $anchor = $occurrence->getDate()->getTimestamp() . '_' . $occurrence->getId();
            $lines[$anchor] = [
                'msg' => $msg,
                'style' => $style,
                'current' => $current,
                'anchor' => $anchor,
            ];
        }

        return view('error.log', [
            'slug' => $appSlug,
            'lines' => new LengthAwarePaginator($lines, $occurrences['total'], $perPage, $page),
        ]);
    }

    public function action(Request $request, App $app): RedirectResponse
    {
        if ($request->filled('visibility')) {
            $this->appFacade->updateAppVisibility($request->boolean('visibility'), $app->getId());
            $this->flashMessage(Message::success('Viditelnost aplikace byla úspěšně aktualizována.'));
        } elseif ($request->filled('mailbox')) {
            $this->appFacade->updateAppMailbox($request->boolean('mailbox'), $app->getId());
            $this->flashMessage(Message::success('Mailbox byl úspěšně aktualizován.'));
        }

        return back();
    }

    private function updateApp(Request $request, App $app): void
    {
        $gitlabData = $this->guzzle->getRepositoryInfo($request->integer('gid'));
        abort_if(!$gitlabData, 404);

        $app->setTitle($gitlabData['name']);
        $app->setSlug(Str::slug($gitlabData['name']));
        $app->setDomain($gitlabData['web_url']);
        $app->setSsh($request->input('sshkey'));
        $app->setOauthClientId($request->input('oauth_client_id'));
        $app->setSshAlgo($request->enum('sshalgo', SSHAlgo::class));
        $app->setGitlabId($request->integer('gid'));
        $app->setGitlabGroup($gitlabData['namespace']['name']);
        $app->setGitlabLabel($request->input('glabels'));

        $this->appFacade->saveApp($app);
    }

    private function retrieveAppBySlug(string $slug): App
    {
        $app = $this->appFacade->getAppBySlug($slug);
        abort_if(!$app, 404);

        return $app;
    }
}
