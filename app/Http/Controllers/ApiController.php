<?php

namespace App\Http\Controllers;

use App\Auth\OAuth;
use App\Auth\SSL;
use App\Enums\Logger;
use App\Exceptions\ApiException;
use App\Model\Entities\App;
use App\Model\Entities\Log;
use App\Model\Facades\LogFacade;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class ApiController extends Controller
{
    public function __construct(
        private readonly LogFacade $logFacade,
        private readonly OAuth $oAuth,
        private readonly SSL $ssl,
    ) {
    }

    public function log(Request $request): JsonResponse
    {
        try {
            $request->validate(
                [
                    'filename' => 'required|string',
                    'content' => 'required|string',
                    'logger' => 'required|string',
                    'logger_version' => 'required|string',
                ],
                [
                    'filename.required' => 'Chybí povinný parametr filename',
                    'filename.string' => 'Parametr filename musí být řetězec',
                    'content.required' => 'Chybí povinný parametr content',
                    'content.string' => 'Parametr content musí být řetězec',
                    'logger.required' => 'Chybí povinný parametr logger',
                    'logger.string' => 'Parametr logger musí být řetězec',
                    'logger_version.required' => 'Chybí povinný parametr logger_version',
                    'logger_version.string' => 'Parametr logger_version musí být řetězec',
                ]
            );

            $app = $this->authenticate($request);

            $filename = $request->input('filename');
            $content = $request->input('content');
            $logger = Logger::tryFrom($request->input('logger'));
            $logger_version = $request->input('logger_version');

            $log = new Log();
            $log->setApp($app);
            $log->setFilename($filename);
            $log->setContent($content);
            $log->setLogger($logger);
            $log->setLoggerVersion($logger_version);
            $this->logFacade->saveLog($log);

            return response()->json(['responseMessage' => "OK"]);
        } catch (ApiException $e) {
            return response()->json(['responseMessage' => $e->getMessage()], $e->getCode());
        }
    }

    public function touch(Request $request): JsonResponse
    {
        try {
            $this->authenticate($request);
            return response()->json(['responseMessage' => "OK"]);
        } catch (ApiException $e) {
            return response()->json(['responseMessage' => $e->getMessage()], $e->getCode());
        }
    }

    /**
     * @throws ApiException
     */
    private function authenticate(Request $request): App
    {
        try {
            if ($request->has('signature') && $request->has('app_id') && $request->has('ts')) {
                // SSH authentication
                $app = $this->ssl->validate($request);
            } else {
                // OAuth authentication
                $app = $this->oAuth->getAppByToken($request);
            }
            return $app;
        } catch (\Exception $e) {
            throw new ApiException('Authentication failed', 401);
        }
    }
}
