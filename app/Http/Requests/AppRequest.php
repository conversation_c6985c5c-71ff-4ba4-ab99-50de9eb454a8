<?php

namespace App\Http\Requests;

use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Foundation\Http\FormRequest;

class AppRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'gid' => 'required|integer',
            'glabels' => 'required',
            'radio-choice' => 'required|in:ssh,oauth',
            'sshkey' => 'required_if:radio-choice,ssh',
            'oauth_client_id' => 'required_if:radio-choice,oauth',
            'sshalgo' => 'required|integer',
        ];
    }

    public function messages(): array
    {
        return [
            'gid.required' => 'Gitlab ID je povinné.',
            'gid.integer' => 'Gitlab ID musí být č<PERSON>lo.',
            'glabels.required' => 'Gitlab štítky jsou povinné.',
            'radio-choice.required' => 'Musí být vybrané buď SSH nebo OAuth ověření',
            'radio-choice.in' => 'Musí být vybrané buď SSH nebo OAuth ověření',
            'sshkey.required_if' => 'SSH klíč je povinný.',
            'oauth_client_id.required_if' => 'OAuth klient je povinný.',
            'sshalgo.required' => 'Algoritmus SSH klíče je povinný.',
            'sshalgo.integer' => 'Algoritmus SSH klíče musí být číslo.',
        ];
    }
}
