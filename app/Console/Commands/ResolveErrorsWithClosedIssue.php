<?php

namespace App\Console\Commands;

use App\Domain\Services\GuzzleService;
use App\Model\Facades\ErrorFacade;
use Illuminate\Console\Command;

class ResolveErrorsWithClosedIssue extends Command
{
    public function __construct(
        private readonly ErrorFacade   $errorFacade,
        private readonly GuzzleService $guzzle,
    ) {
        parent::__construct();
    }

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'errorlog:resolve-error-with-closed-issues';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This command automatically resolves all error, that have their issue set and closed.';

    /**
     * Execute the console command.
     */
    public function handle(): void
    {
        $errors = $this->errorFacade->getErrors();
        foreach ($errors as $error) {
            if ($error->getIssue() && $this->guzzle->getGitlabIssueInfo($error)['state'] === 'closed') {
                $error->resolve();
                $this->errorFacade->saveError($error);
            }
        }
    }
}
