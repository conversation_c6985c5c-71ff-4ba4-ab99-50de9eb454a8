<?php

namespace App\Console\Commands;

use App\Domain\Parsers\ParserFactory;
use App\Model\Entities\Log;
use App\Model\Facades\LogFacade;
use Doctrine\ORM\EntityManagerInterface;
use Illuminate\Console\Command;

class ErrorParse extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'errorlog:parse';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Rozparsuje logy a vytvoří z nich záznamy o chybách.';

    public function __construct(
        private readonly LogFacade   $logFacade,
        private readonly EntityManagerInterface $entityManager,
    ) {
        parent::__construct();
    }

    public function handle(): int
    {
        $factory = new ParserFactory();

        // Process one log at a time
        $processed = 0;
        $failed = 0;

        while (true) {
            // Get one unparsed log using simple query
            $logs = $this->logFacade->getLogs();

            if (empty($logs)) {
                break; // No more logs
            }

            // Take only the first log
            $log = $logs[0];

            try {
                $parser = $factory->createParser($log->getLogger());
                $parser->setup($this->entityManager);

                $traceResult = $parser->parseTrace($log);
                $errorResult = $parser->parseErrors($log);

                if ($traceResult || $errorResult) {
                    $this->logParsed($log);
                }

                $processed++;

                if ($processed % 100 === 0) {
                    $this->info("Processed {$processed} logs...");
                }

            } catch (\Exception $e) {
                $failed++;
                $this->error("Failed to process log {$log->getId()}: " . $e->getMessage());
                // Mark as parsed even if failed to avoid infinite loop
                $this->logParsed($log);
            }

            // Clean up memory
            $this->entityManager->detach($log);
            $this->entityManager->clear();
            gc_collect_cycles();

            // Clear the logs array
            unset($logs);
        }

        $this->info("Processing completed!");
        $this->info("Successfully processed: {$processed} logs");
        $this->info("Failed: {$failed} logs");

        return 0;
    }

    private function logParsed(Log $log): void
    {
        $connection = $this->entityManager->getConnection();
        $log->markAsParsed();
        $connection->update('log', ['parsed' => 1], ['id' => $log->getId()]);
    }

}
