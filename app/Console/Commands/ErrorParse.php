<?php

namespace App\Console\Commands;

use App\Domain\Parsers\ParserFactory;
use App\Model\Entities\Log;
use App\Model\Facades\LogFacade;
use Doctrine\ORM\EntityManagerInterface;
use Illuminate\Console\Command;

class ErrorParse extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'errorlog:parse {--batch-size=10 : Number of logs to process in each batch} {--memory-limit=256M : Memory limit for the command} {--max-memory-mb=150 : Maximum memory usage in MB before forcing cleanup} {--one-by-one : Process logs one by one with detailed error reporting}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Rozparsuje logy a vytvoří z nich záznamy o chybách.';

    public function __construct(
        private readonly LogFacade   $logFacade,
        private readonly EntityManagerInterface $entityManager,
    ) {
        parent::__construct();
    }

    public function handle(): int
    {
        // Set memory limit if provided
        $memoryLimit = $this->option('memory-limit');
        if ($memoryLimit) {
            ini_set('memory_limit', $memoryLimit);
        }

        $batchSize = (int) $this->option('batch-size');
        $maxMemoryMb = (int) $this->option('max-memory-mb');
        $oneByOne = $this->option('one-by-one');

        // Get total count for progress tracking
        $totalLogs = $this->logFacade->countUnparsedLogs();
        $this->info("Found {$totalLogs} unparsed logs to process");

        if ($oneByOne) {
            $this->info("Processing logs ONE BY ONE with detailed error reporting");
            return $this->processOneByOne($totalLogs);
        }

        $this->info("Using batch size: {$batchSize}, Memory limit: {$maxMemoryMb}MB");

        if ($totalLogs === 0) {
            $this->info('No logs to process');
            return 0;
        }

        $processed = 0;
        $offset = 0;
        $memoryThreshold = $maxMemoryMb * 1024 * 1024; // Convert MB to bytes
        $startTime = microtime(true);

        while ($offset < $totalLogs) {
            $this->info("Processing batch: " . ($offset + 1) . " to " . min($offset + $batchSize, $totalLogs) . " of {$totalLogs}");

            // Get only log IDs to minimize memory usage
            $logIds = $this->logFacade->getUnparsedLogIds($batchSize, $offset);

            if (empty($logIds)) {
                break;
            }

            // Process each log by ID individually with immediate cleanup
            foreach ($logIds as $logId) {
                try {
                    $this->processLogById($logId);
                    $processed++;
                } catch (\Exception $e) {
                    $this->error("Failed to process log {$logId}: " . $e->getMessage());
                    // Continue processing other logs
                }

                // Force cleanup after every 5 logs
                if ($processed % 5 === 0) {
                    $this->aggressiveCleanup();
                }

                // Check memory usage after each log
                $currentMemory = memory_get_usage(true);
                if ($currentMemory > $memoryThreshold) {
                    $this->warn("Memory usage high: " . round($currentMemory / 1024 / 1024, 2) . " MB - forcing cleanup");
                    $this->aggressiveCleanup();
                }
            }

            // Clear everything after batch
            $this->aggressiveCleanup();

            // Update offset for next batch
            $offset += $batchSize;

            // Show memory usage
            $memoryUsage = round(memory_get_usage(true) / 1024 / 1024, 2);
            $this->info("Memory usage: {$memoryUsage} MB");
        }

        $endTime = microtime(true);
        $duration = round($endTime - $startTime, 2);
        $finalMemory = round(memory_get_usage(true) / 1024 / 1024, 2);

        $this->info("Successfully processed {$processed} logs in {$duration} seconds");
        $this->info("Final memory usage: {$finalMemory} MB");
        return 0;
    }

    /**
     * Process logs one by one with detailed error reporting
     */
    private function processOneByOne(int $totalLogs): int
    {
        $processed = 0;
        $failed = 0;
        $offset = 0;
        $startTime = microtime(true);

        $this->info("Starting one-by-one processing...");

        while ($offset < $totalLogs) {
            // Get one log at a time
            $logIds = $this->logFacade->getUnparsedLogIds(1, $offset);

            if (empty($logIds)) {
                break;
            }

            $logId = $logIds[0];
            $this->info("Processing log ID: {$logId} ({$processed + 1}/{$totalLogs})");

            try {
                $this->processLogByIdDetailed($logId);
                $processed++;
                $this->info("✓ Log {$logId} processed successfully");
            } catch (\Exception $e) {
                $failed++;
                $this->error("✗ Log {$logId} FAILED: " . $e->getMessage());
                $this->error("Stack trace: " . $e->getTraceAsString());
            }

            // Aggressive cleanup after each log
            $this->aggressiveCleanup();

            $offset++;

            // Show progress every 10 logs
            if (($processed + $failed) % 10 === 0) {
                $memoryUsage = round(memory_get_usage(true) / 1024 / 1024, 2);
                $this->info("Progress: {$processed} processed, {$failed} failed, Memory: {$memoryUsage} MB");
            }
        }

        $endTime = microtime(true);
        $duration = round($endTime - $startTime, 2);
        $finalMemory = round(memory_get_usage(true) / 1024 / 1024, 2);

        $this->info("One-by-one processing completed in {$duration} seconds");
        $this->info("Successfully processed: {$processed} logs");
        $this->info("Failed: {$failed} logs");
        $this->info("Final memory usage: {$finalMemory} MB");

        return 0;
    }

    /**
     * Process a single log by ID with maximum memory efficiency using raw data
     */
    private function processLogById(int $logId): void
    {
        try {
            // Get log data without creating entities
            $logData = $this->logFacade->getLogDataWithContent($logId);

            if (!$logData) {
                $this->warn("Log {$logId} not found or already parsed");
                return;
            }

            // Process using raw data to avoid entity overhead
            $this->processLogData($logData);

            // Mark as parsed using direct SQL
            $this->markLogAsParsed($logId);

            // Clear references
            unset($logData);

        } catch (\Exception $e) {
            $this->error("Error processing log {$logId}: " . $e->getMessage());
        }
    }

    /**
     * Process a single log with detailed error reporting
     */
    private function processLogByIdDetailed(int $logId): void
    {
        // Get log data without creating entities
        $logData = $this->logFacade->getLogDataWithContent($logId);

        if (!$logData) {
            throw new \Exception("Log {$logId} not found or already parsed");
        }

        $this->info("  - Log data loaded: filename={$logData['filename']}, logger={$logData['logger']}");

        // Handle BLOB content
        $content = $logData['content'];
        if (is_resource($content)) {
            $content = stream_get_contents($content);
            $this->info("  - Content loaded from BLOB resource, size: " . strlen($content) . " bytes");
        } else {
            $this->info("  - Content loaded as string, size: " . strlen($content) . " bytes");
        }

        $processed = false;

        // Parse trace files
        $traceFilePattern = '#^exception--([0-9]{4}-[0-9]{2}-[0-9]{2}--[0-9]{2}-[0-9]{2})--([a-z0-9]+)\.(html)$#';
        if (preg_match($traceFilePattern, $logData['filename'], $matches)) {
            $this->info("  - Processing as trace file");
            $this->processTraceDataDetailed($logData, $content, $matches);
            $processed = true;
        }

        // Parse error logs
        $errorLinePattern = "/\[([0-9\-\s]{19})] ([A-Za-z0-9\s\\\]+): (.*) in (.*):([0-9]+)\s+@\s+(\S*)(\s+@@\s+(.*))?/";
        if (preg_match_all($errorLinePattern, $content, $matches, PREG_SET_ORDER)) {
            $this->info("  - Processing as error log, found " . count($matches) . " error matches");
            $this->processErrorDataDetailed($logData, $content, $matches);
            $processed = true;
        }

        if (!$processed) {
            $this->info("  - No matching patterns found, skipping");
        }

        // Mark as parsed using direct SQL
        $this->markLogAsParsed($logId);
        $this->info("  - Marked as parsed");

        // Clear content from memory immediately
        unset($content, $logData);
    }

    /**
     * Process log data without creating Log entities
     */
    private function processLogData(array $logData): void
    {
        $connection = $this->entityManager->getConnection();

        // Handle BLOB content
        $content = $logData['content'];
        if (is_resource($content)) {
            $content = stream_get_contents($content);
        }

        // Parse trace files
        $traceFilePattern = '#^exception--([0-9]{4}-[0-9]{2}-[0-9]{2}--[0-9]{2}-[0-9]{2})--([a-z0-9]+)\.(html)$#';
        if (preg_match($traceFilePattern, $logData['filename'], $matches)) {
            $this->processTraceData($logData, $content, $matches, $connection);
        }

        // Parse error logs
        $errorLinePattern = "/\[([0-9\-\s]{19})] ([A-Za-z0-9\s\\\]+): (.*) in (.*):([0-9]+)\s+@\s+(\S*)(\s+@@\s+(.*))?/";
        if (preg_match_all($errorLinePattern, $content, $matches, PREG_SET_ORDER)) {
            $this->processErrorData($logData, $content, $matches, $connection);
        }

        // Clear content from memory immediately
        unset($content);
    }

    /**
     * Aggressive memory cleanup
     */
    private function aggressiveCleanup(): void
    {
        // Clear entity manager
        $this->entityManager->clear();

        // Force garbage collection
        gc_collect_cycles();

        // Clear any potential circular references
        if (function_exists('gc_mem_caches')) {
            gc_mem_caches();
        }
    }

    /**
     * Mark log as parsed using direct SQL
     */
    private function markLogAsParsed(int $logId): void
    {
        $connection = $this->entityManager->getConnection();
        $connection->update('log', ['parsed' => 1], ['id' => $logId]);
    }

    /**
     * Process trace data without entities
     */
    private function processTraceData(array $logData, string $content, array $matches, $connection): void
    {
        $hash = $matches[2];

        // Check if trace already exists
        $stmt = $connection->prepare('SELECT COUNT(*) FROM trace WHERE hash = ?');
        $stmt->bindValue(1, $hash);
        if ($stmt->executeQuery()->fetchOne() > 0) {
            return; // Trace already exists
        }

        // Insert new trace
        $connection->insert('trace', [
            'name' => $logData['filename'],
            'hash' => $hash,
            'content' => $content,
            'extension' => $matches[3],
            'mime_type' => 'text/html',
            'size' => strlen($content),
            'file_created' => \DateTimeImmutable::createFromFormat('Y-m-d--H-i', $matches[1])->format('Y-m-d H:i:s'),
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s'),
        ]);
    }

    /**
     * Process trace data with detailed logging
     */
    private function processTraceDataDetailed(array $logData, string $content, array $matches): void
    {
        $hash = $matches[2];
        $this->info("    - Trace hash: {$hash}");

        $connection = $this->entityManager->getConnection();

        // Check if trace already exists
        $stmt = $connection->prepare('SELECT COUNT(*) FROM trace WHERE hash = ?');
        $stmt->bindValue(1, $hash);
        $count = $stmt->executeQuery()->fetchOne();

        if ($count > 0) {
            $this->info("    - Trace already exists, skipping");
            return;
        }

        $this->info("    - Inserting new trace");

        // Insert new trace
        $result = $connection->insert('trace', [
            'name' => $logData['filename'],
            'hash' => $hash,
            'content' => $content,
            'extension' => $matches[3],
            'mime_type' => 'text/html',
            'size' => strlen($content),
            'file_created' => \DateTimeImmutable::createFromFormat('Y-m-d--H-i', $matches[1])->format('Y-m-d H:i:s'),
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s'),
        ]);

        $this->info("    - Trace inserted successfully, result: {$result}");
    }

    /**
     * Process error data with detailed logging
     */
    private function processErrorDataDetailed(array $logData, string $content, array $matches): void
    {
        $connection = $this->entityManager->getConnection();

        foreach ($matches as $index => $match) {
            $this->info("    - Processing error match " . ($index + 1) . "/" . count($matches));
            $this->info("      Error type: {$match[2]}, Message: " . substr($match[3], 0, 50) . "...");

            $connection->executeStatement('BEGIN');
            try {
                $this->insertErrorFromMatchDetailed($logData, $content, $match, $connection);
                $connection->executeStatement('COMMIT');
                $this->info("      ✓ Error match processed successfully");
            } catch (\Exception $e) {
                $connection->executeStatement('ROLLBACK');
                $this->error("      ✗ Error match failed: " . $e->getMessage());
                throw $e; // Re-throw to see the full error in one-by-one mode
            }
        }
    }

    /**
     * Process error data without entities
     */
    private function processErrorData(array $logData, string $content, array $matches, $connection): void
    {
        // Process each error match individually to avoid large transactions
        foreach ($matches as $match) {
            $connection->executeStatement('BEGIN');
            try {
                $this->insertErrorFromMatch($logData, $content, $match, $connection);
                $connection->executeStatement('COMMIT');
            } catch (\Exception $e) {
                $connection->executeStatement('ROLLBACK');
                $this->error("Failed to insert error from match: " . $e->getMessage());
                // Continue with next match instead of failing entire log
            }
        }
    }

    /**
     * Insert error from regex match without entities
     */
    private function insertErrorFromMatch(array $logData, string $content, array $match, $connection): void
    {
        // Create checksum
        $checksumData = $match[2] . '|' . $match[3] . '|' . $match[4] . '|' . $match[5];
        $checksum = md5($checksumData);

        // Check if error already exists
        $stmt = $connection->prepare('SELECT id FROM error WHERE checksum = ?');
        $stmt->bindValue(1, $checksum);
        $existingError = $stmt->executeQuery()->fetchAssociative();

        if ($existingError) {
            // Mark existing error as unresolved
            $connection->update('error', ['resolved' => 0, 'user_id' => null], ['id' => $existingError['id']]);
            $errorId = $existingError['id'];
        } else {
            // Determine error type
            $errorType = $this->getErrorTypeValue($match[2]);
            $exception = $errorType === -1 ? $match[2] : null;

            // Get trace ID if exists
            $traceId = null;
            if (isset($match[8])) {
                $traceStmt = $connection->prepare('SELECT id FROM trace WHERE name = ?');
                $traceStmt->bindValue(1, $match[8]);
                $traceResult = $traceStmt->executeQuery()->fetchAssociative();
                $traceId = $traceResult['id'] ?? null;
            }

            // Insert new error
            $connection->insert('error', [
                'type' => $errorType,
                'message' => $match[3],
                'file' => $match[4],
                'line' => (int) $match[5],
                'original' => $content,
                'url' => $match[6],
                'checksum' => $checksum,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
                'app_id' => $logData['app_id'],
                'filename' => $logData['filename'],
                'exception' => $exception,
                'resolved' => 0,
                'trace_id' => $traceId
            ]);

            $errorId = $connection->lastInsertId();
        }

        // Insert occurrence
        $connection->insert('occurrence', [
            'error_id' => $errorId,
            'date' => \DateTimeImmutable::createFromFormat('Y-m-d H-i-s', $match[1])->format('Y-m-d H:i:s'),
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s'),
        ]);
    }

    /**
     * Insert error from regex match with detailed logging
     */
    private function insertErrorFromMatchDetailed(array $logData, string $content, array $match, $connection): void
    {
        // Create checksum
        $checksumData = $match[2] . '|' . $match[3] . '|' . $match[4] . '|' . $match[5];
        $checksum = md5($checksumData);
        $this->info("        - Checksum: {$checksum}");

        // Check if error already exists
        $stmt = $connection->prepare('SELECT id FROM error WHERE checksum = ?');
        $stmt->bindValue(1, $checksum);
        $existingError = $stmt->executeQuery()->fetchAssociative();

        if ($existingError) {
            $this->info("        - Error already exists (ID: {$existingError['id']}), updating");
            // Mark existing error as unresolved
            $connection->update('error', ['resolved' => 0, 'user_id' => null], ['id' => $existingError['id']]);
            $errorId = $existingError['id'];
        } else {
            $this->info("        - Creating new error");

            // Determine error type
            $errorType = $this->getErrorTypeValue($match[2]);
            $exception = $errorType === -1 ? $match[2] : null;
            $this->info("        - Error type: {$match[2]} -> {$errorType}");

            // Get trace ID if exists
            $traceId = null;
            if (isset($match[8])) {
                $this->info("        - Looking for trace: {$match[8]}");
                $traceStmt = $connection->prepare('SELECT id FROM trace WHERE name = ?');
                $traceStmt->bindValue(1, $match[8]);
                $traceResult = $traceStmt->executeQuery()->fetchAssociative();
                $traceId = $traceResult['id'] ?? null;
                $this->info("        - Trace ID: " . ($traceId ?? 'not found'));
            }

            // Parse date
            $dateStr = $match[1];
            $this->info("        - Parsing date: {$dateStr}");
            try {
                $date = \DateTimeImmutable::createFromFormat('Y-m-d H-i-s', $dateStr);
                if (!$date) {
                    throw new \Exception("Failed to parse date: {$dateStr}");
                }
                $formattedDate = $date->format('Y-m-d H:i:s');
                $this->info("        - Date formatted: {$formattedDate}");
            } catch (\Exception $e) {
                $this->error("        - Date parsing failed: " . $e->getMessage());
                throw $e;
            }

            // Insert new error
            $this->info("        - Inserting error into database");
            $result = $connection->insert('error', [
                'type' => $errorType,
                'message' => $match[3],
                'file' => $match[4],
                'line' => (int) $match[5],
                'original' => $content,
                'url' => $match[6],
                'checksum' => $checksum,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
                'app_id' => $logData['app_id'],
                'filename' => $logData['filename'],
                'exception' => $exception,
                'resolved' => 0,
                'trace_id' => $traceId
            ]);

            $errorId = $connection->lastInsertId();
            $this->info("        - Error inserted with ID: {$errorId}");
        }

        // Insert occurrence
        $this->info("        - Inserting occurrence for error ID: {$errorId}");
        $result = $connection->insert('occurrence', [
            'error_id' => $errorId,
            'date' => $formattedDate ?? $date->format('Y-m-d H:i:s'),
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s'),
        ]);
        $this->info("        - Occurrence inserted successfully");
    }

    /**
     * Get error type integer value from title
     */
    private function getErrorTypeValue(string $title): int
    {
        // Map error titles to ErrorType enum integer values
        return match ($title) {
            'Fatal error', 'Error' => 1,        // E_ERROR
            'Warning' => 2,                      // E_WARNING
            'Parse error' => 4,                  // E_PARSE
            'Notice' => 8,                       // E_NOTICE
            'Core error' => 16,                  // E_CORE_ERROR
            'Core warning' => 32,                // E_CORE_WARNING
            'Compile error' => 64,               // E_COMPILE_ERROR
            'Compile warning' => 128,            // E_COMPILE_WARNING
            'User error' => 256,                 // E_USER_ERROR
            'User warning' => 512,               // E_USER_WARNING
            'User notice' => 1024,               // E_USER_NOTICE
            'Strict warning' => 2048,            // E_STRICT
            'Recoverable error' => 4096,         // E_RECOVERABLE_ERROR
            'Deprecated' => 8196,                // E_DEPRECATED
            'User deprecated' => 16384,          // E_USER_DEPRECATED
            default => -1,                       // EXCEPTION
        };
    }
}
