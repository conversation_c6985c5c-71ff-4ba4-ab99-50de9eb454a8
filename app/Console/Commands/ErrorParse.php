<?php

namespace App\Console\Commands;

use App\Domain\Parsers\ParserFactory;
use App\Model\Entities\Log;
use App\Model\Facades\LogFacade;
use Doctrine\ORM\EntityManagerInterface;
use Illuminate\Console\Command;

class ErrorParse extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'errorlog:parse';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Rozparsuje logy a vytvoří z nich záznamy o chybách.';

    public function __construct(
        private readonly LogFacade   $logFacade,
        private readonly EntityManagerInterface $entityManager,
    ) {
        parent::__construct();
    }

    public function handle(): int
    {
        $factory = new ParserFactory();
        $logs = $this->logFacade->getLogs();

        foreach ($logs as $log) {
            $parser = $factory->createParser($log->getLogger());
            $parser->setup($this->entityManager);
            $result = $parser->parseTrace($log);
            if ($result) {
                $this->logParsed($log);
            }
        }

        foreach ($logs as $log) {
            $parser = $factory->createParser($log->getLogger());
            $parser->setup($this->entityManager);
            $result = $parser->parseErrors($log);
            if ($result) {
                $this->logParsed($log);
            }
        }

        return 0;
    }

    private function logParsed(Log $log): void
    {
        $connection = $this->entityManager->getConnection();
        $log->markAsParsed();
        $connection->update('log', ['parsed' => 1], ['id' => $log->getId()]);
    }
}
