<?php

namespace App\Console\Commands;

use App\Domain\Parsers\ParserFactory;
use App\Model\Entities\Log;
use App\Model\Facades\LogFacade;
use Doctrine\ORM\EntityManagerInterface;
use Illuminate\Console\Command;

class ErrorParse extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'errorlog:parse {--batch-size=20 : Number of logs to process in each batch} {--memory-limit=256M : Memory limit for the command} {--max-memory-mb=200 : Maximum memory usage in MB before forcing cleanup}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Rozparsuje logy a vytvoří z nich záznamy o chybách.';

    public function __construct(
        private readonly LogFacade   $logFacade,
        private readonly EntityManagerInterface $entityManager,
    ) {
        parent::__construct();
    }

    public function handle(): int
    {
        // Set memory limit if provided
        $memoryLimit = $this->option('memory-limit');
        if ($memoryLimit) {
            ini_set('memory_limit', $memoryLimit);
        }

        $batchSize = (int) $this->option('batch-size');
        $maxMemoryMb = (int) $this->option('max-memory-mb');

        // Get total count for progress tracking
        $totalLogs = $this->logFacade->countUnparsedLogs();
        $this->info("Found {$totalLogs} unparsed logs to process");
        $this->info("Using batch size: {$batchSize}, Memory limit: {$maxMemoryMb}MB");

        if ($totalLogs === 0) {
            $this->info('No logs to process');
            return 0;
        }

        $processed = 0;
        $offset = 0;
        $memoryThreshold = $maxMemoryMb * 1024 * 1024; // Convert MB to bytes
        $startTime = microtime(true);

        while ($offset < $totalLogs) {
            $this->info("Processing batch: " . ($offset + 1) . " to " . min($offset + $batchSize, $totalLogs) . " of {$totalLogs}");

            // Get only log IDs to minimize memory usage
            $logIds = $this->logFacade->getUnparsedLogIds($batchSize, $offset);

            if (empty($logIds)) {
                break;
            }

            // Process each log by ID individually with immediate cleanup
            foreach ($logIds as $logId) {
                $this->processLogById($logId);
                $processed++;

                // Check memory usage after each log
                $currentMemory = memory_get_usage(true);
                if ($currentMemory > $memoryThreshold) {
                    $this->warn("Memory usage high: " . round($currentMemory / 1024 / 1024, 2) . " MB - forcing cleanup");
                    $this->aggressiveCleanup();
                }

                // Force cleanup after every 10 logs
                if ($processed % 10 === 0) {
                    $this->aggressiveCleanup();
                }
            }

            // Clear everything after batch
            $this->aggressiveCleanup();

            // Update offset for next batch
            $offset += $batchSize;

            // Show memory usage
            $memoryUsage = round(memory_get_usage(true) / 1024 / 1024, 2);
            $this->info("Memory usage: {$memoryUsage} MB");
        }

        $endTime = microtime(true);
        $duration = round($endTime - $startTime, 2);
        $finalMemory = round(memory_get_usage(true) / 1024 / 1024, 2);

        $this->info("Successfully processed {$processed} logs in {$duration} seconds");
        $this->info("Final memory usage: {$finalMemory} MB");
        return 0;
    }

    /**
     * Process a single log by ID with maximum memory efficiency
     */
    private function processLogById(int $logId): void
    {
        try {
            // Load log with minimal memory footprint
            $log = $this->logFacade->getLogByIdMinimal($logId);

            if (!$log) {
                $this->warn("Log {$logId} not found");
                return;
            }

            // Create fresh parser instance for each log to avoid memory accumulation
            $factory = new ParserFactory();
            $parser = $factory->createParser($log->getLogger());
            $parser->setup($this->entityManager);

            // Parse trace first
            $traceResult = $parser->parseTrace($log);

            // Parse errors
            $errorResult = $parser->parseErrors($log);

            // Mark as parsed if either operation succeeded
            if ($traceResult || $errorResult) {
                $this->logParsed($log);
            }

            // Immediately detach the log entity to free memory
            $this->entityManager->detach($log);

            // Clear any references
            unset($log, $parser, $factory);

        } catch (\Exception $e) {
            $this->error("Error processing log {$logId}: " . $e->getMessage());
        }
    }

    /**
     * Aggressive memory cleanup
     */
    private function aggressiveCleanup(): void
    {
        // Clear entity manager
        $this->entityManager->clear();

        // Force garbage collection
        gc_collect_cycles();

        // Clear any potential circular references
        if (function_exists('gc_mem_caches')) {
            gc_mem_caches();
        }
    }

    private function logParsed(Log $log): void
    {
        $connection = $this->entityManager->getConnection();
        $log->markAsParsed();
        $connection->update('log', ['parsed' => 1], ['id' => $log->getId()]);
    }
}
