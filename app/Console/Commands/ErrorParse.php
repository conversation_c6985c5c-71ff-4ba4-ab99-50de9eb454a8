<?php

namespace App\Console\Commands;

use App\Domain\Parsers\ParserFactory;
use App\Model\Entities\Log;
use App\Model\Facades\LogFacade;
use Doctrine\ORM\EntityManagerInterface;
use Illuminate\Console\Command;

class ErrorParse extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'errorlog:parse';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Rozparsuje logy a vytvoří z nich záznamy o chybách.';

    public function __construct(
        private readonly LogFacade   $logFacade,
        private readonly EntityManagerInterface $entityManager,
    ) {
        parent::__construct();
    }

    public function handle(): int
    {
        $totalLogs = $this->logFacade->countUnparsedLogs();
        $this->info("Found {$totalLogs} unparsed logs to process");

        if ($totalLogs === 0) {
            $this->info('No logs to process');
            return 0;
        }

        $processed = 0;
        $failed = 0;

        // Process logs one by one
        while (true) {
            // Get one unparsed log
            $logIds = $this->logFacade->getUnparsedLogIds(1, 0);

            if (empty($logIds)) {
                break; // No more logs to process
            }

            $logId = $logIds[0];

            try {
                $this->processOneLog($logId);
                $processed++;

                if ($processed % 100 === 0) {
                    $this->info("Processed {$processed} logs...");
                }
            } catch (\Exception $e) {
                $failed++;
                $this->error("Failed to process log {$logId}: " . $e->getMessage());
            }

            // Clean up memory after each log
            $this->entityManager->clear();
            gc_collect_cycles();
        }

        $this->info("Processing completed!");
        $this->info("Successfully processed: {$processed} logs");
        $this->info("Failed: {$failed} logs");

        return 0;
    }

    /**
     * Process a single log using the original parser
     */
    private function processOneLog(int $logId): void
    {
        // Get the log entity
        $log = $this->entityManager->getRepository(Log::class)->find($logId);

        if (!$log || $log->isParsed()) {
            return;
        }

        // Use the original parser
        $factory = new ParserFactory();
        $parser = $factory->createParser($log->getLogger());
        $parser->setup($this->entityManager);

        // Parse trace and errors
        $traceResult = $parser->parseTrace($log);
        $errorResult = $parser->parseErrors($log);

        // Mark as parsed if either succeeded
        if ($traceResult || $errorResult) {
            $log->markAsParsed();
            $this->entityManager->getConnection()->update('log', ['parsed' => 1], ['id' => $log->getId()]);
        }

        // Detach the entity to free memory
        $this->entityManager->detach($log);
    }

}
