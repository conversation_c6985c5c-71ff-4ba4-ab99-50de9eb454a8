<?php

namespace App\Console\Commands;

use App\Domain\Parsers\ParserFactory;
use App\Model\Entities\Log;
use App\Model\Facades\LogFacade;
use Doctrine\ORM\EntityManagerInterface;
use Illuminate\Console\Command;

class ErrorParse extends Command
{
    protected $signature = 'errorlog:parse';
    protected $description = 'Rozparsuje logy a vytvoří z nich záznamy o chybách.';

    public function __construct(
        private readonly LogFacade   $logFacade,
        private readonly EntityManagerInterface $entityManager,
    ) {
        parent::__construct();
    }

    public function handle(): int
    {
        $processed = 0;
        $failed = 0;

        while (true) {
            // Get one unparsed log
            $logs = $this->logFacade->getLogs();

            if (empty($logs)) {
                break; // No more logs
            }

            $log = $logs[0];

            try {
                $factory = new ParserFactory();
                $parser = $factory->createParser($log->getLogger());
                $parser->setup($this->entityManager);

                $traceResult = $parser->parseTrace($log);
                $errorResult = $parser->parseErrors($log);

                $this->markLogAsParsed($log->getId());
                $processed++;

                if ($processed % 100 === 0) {
                    $memory = round(memory_get_usage(true) / 1024 / 1024, 2);
                    $this->info("Processed {$processed} logs, Memory: {$memory} MB");
                }

            } catch (\Exception $e) {
                $failed++;
                $this->error("Failed log {$log->getId()}: " . $e->getMessage());
                // Mark as parsed to avoid infinite loop
                $this->markLogAsParsed($log->getId());
            }

            // Clean up memory
            $this->entityManager->detach($log);
            $this->entityManager->clear();
            gc_collect_cycles();
            unset($logs, $log, $parser, $factory);
        }

        $this->info("Completed! Processed: {$processed}, Failed: {$failed}");
        return 0;
    }

    private function markLogAsParsed(int $logId): void
    {
        $connection = $this->entityManager->getConnection();
        $connection->update('log', ['parsed' => 1], ['id' => $logId]);
    }
}
