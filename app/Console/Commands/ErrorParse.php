<?php

namespace App\Console\Commands;

use App\Domain\Parsers\ParserFactory;
use App\Model\Entities\Log;
use App\Model\Facades\LogFacade;
use Doctrine\ORM\EntityManagerInterface;
use Illuminate\Console\Command;

class ErrorParse extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'errorlog:parse';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Rozparsuje logy a vytvoří z nich záznamy o chybách.';

    public function __construct(
        private readonly LogFacade   $logFacade,
        private readonly EntityManagerInterface $entityManager,
    ) {
        parent::__construct();
    }

    public function handle(): int
    {
        $this->info("=== STARTING ERROR LOG PARSING ===");

        try {
            $this->info("Step 1: Initializing ParserFactory...");
            $factory = new ParserFactory();
            $this->info("✓ ParserFactory created successfully");

            $this->info("Step 2: Counting unparsed logs...");
            $totalLogs = $this->logFacade->countUnparsedLogs();
            $this->info("✓ Found {$totalLogs} unparsed logs");

            if ($totalLogs === 0) {
                $this->info("No logs to process, exiting.");
                return 0;
            }

        } catch (\Exception $e) {
            $this->error("FAILED during initialization: " . $e->getMessage());
            $this->error("Stack trace: " . $e->getTraceAsString());
            return 1;
        }

        // Process one log at a time
        $processed = 0;
        $failed = 0;

        $this->info("Step 3: Starting log processing loop...");

        while (true) {
            try {
                $this->info("Step 3.1: Getting next unparsed log...");
                $memoryBefore = round(memory_get_usage(true) / 1024 / 1024, 2);
                $this->info("Memory before getting log: {$memoryBefore} MB");

                $logs = $this->logFacade->getLogs();

                if (empty($logs)) {
                    $this->info("✓ No more logs to process, breaking loop");
                    break; // No more logs
                }

                $log = $logs[0];
                $this->info("✓ Got log ID: {$log->getId()}, filename: {$log->getFilename()}");

                $this->info("Step 3.2: Creating parser for logger: {$log->getLogger()->value}");
                $parser = $factory->createParser($log->getLogger());
                $this->info("✓ Parser created");

                $this->info("Step 3.3: Setting up parser with EntityManager...");
                $parser->setup($this->entityManager);
                $this->info("✓ Parser setup complete");

                $this->info("Step 3.4: Checking log size first...");
                // Check log size without loading content
                $logSize = $this->getLogSize($log->getId());
                $this->info("Log size: " . round($logSize / 1024, 2) . " KB");

                if ($logSize > 2 * 1024 * 1024) { // If larger than 2MB
                    $this->info("Large log detected (>2MB), skipping parsing to save memory");
                    $traceResult = false;
                    $errorResult = false;
                } else {
                    $this->info("Step 3.4: Parsing trace...");
                    $traceResult = $parser->parseTrace($log);
                    $this->info("✓ Trace parsing result: " . ($traceResult ? 'SUCCESS' : 'NO_MATCH'));

                    $this->info("Step 3.5: Parsing errors...");
                    $errorResult = $parser->parseErrors($log);
                    $this->info("✓ Error parsing result: " . ($errorResult ? 'SUCCESS' : 'NO_MATCH'));
                }

                if ($traceResult || $errorResult) {
                    $this->info("Step 3.6: Marking log as parsed...");
                    $this->logParsed($log);
                    $this->info("✓ Log marked as parsed");
                } else {
                    $this->info("Step 3.6: No results, marking as parsed anyway...");
                    $this->logParsed($log);
                    $this->info("✓ Log marked as parsed (no matches)");
                }

                $processed++;
                $this->info("✓ Log {$log->getId()} processed successfully (total: {$processed})");

            } catch (\Exception $e) {
                $failed++;
                $this->error("✗ FAILED to process log: " . $e->getMessage());
                $this->error("Error details: " . $e->getFile() . ":" . $e->getLine());
                $this->error("Stack trace: " . $e->getTraceAsString());

                // Try to mark as parsed to avoid infinite loop
                try {
                    if (isset($log)) {
                        $this->logParsed($log);
                        $this->info("✓ Failed log marked as parsed to avoid infinite loop");
                    }
                } catch (\Exception $markError) {
                    $this->error("✗ Could not mark failed log as parsed: " . $markError->getMessage());
                }
            }

            try {
                $this->info("Step 3.7: Cleaning up memory...");
                if (isset($log)) {
                    $this->entityManager->detach($log);
                }
                $this->entityManager->clear();
                gc_collect_cycles();
                unset($logs, $log, $parser);

                $memoryAfter = round(memory_get_usage(true) / 1024 / 1024, 2);
                $this->info("✓ Memory cleanup complete. Memory: {$memoryAfter} MB");

                if ($processed % 10 === 0) {
                    $this->info("=== PROGRESS: {$processed} processed, {$failed} failed ===");
                }

            } catch (\Exception $e) {
                $this->error("✗ Error during cleanup: " . $e->getMessage());
            }
        }

        $this->info("=== PROCESSING COMPLETED ===");
        $this->info("Successfully processed: {$processed} logs");
        $this->info("Failed: {$failed} logs");

        return 0;
    }

    private function logParsed(Log $log): void
    {
        try {
            $this->info("    - Getting database connection...");
            $connection = $this->entityManager->getConnection();
            $this->info("    - ✓ Connection obtained");

            $this->info("    - Marking log entity as parsed...");
            $log->markAsParsed();
            $this->info("    - ✓ Entity marked as parsed");

            $this->info("    - Updating database record...");
            $result = $connection->update('log', ['parsed' => 1], ['id' => $log->getId()]);
            $this->info("    - ✓ Database updated, affected rows: {$result}");

        } catch (\Exception $e) {
            $this->error("    - ✗ Error in logParsed: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Get log content size without loading the content into memory
     */
    private function getLogSize(int $logId): int
    {
        try {
            $connection = $this->entityManager->getConnection();
            $sql = 'SELECT LENGTH(content) as size FROM log WHERE id = ?';
            $stmt = $connection->prepare($sql);
            $stmt->bindValue(1, $logId);
            $result = $stmt->executeQuery()->fetchOne();
            return (int) $result;
        } catch (\Exception $e) {
            $this->error("Could not get log size: " . $e->getMessage());
            return 0;
        }
    }

}
