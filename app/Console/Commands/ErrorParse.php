<?php

namespace App\Console\Commands;

use App\Domain\Parsers\ParserFactory;
use App\Model\Entities\Log;
use App\Model\Facades\LogFacade;
use Doctrine\ORM\EntityManagerInterface;
use Illuminate\Console\Command;

class ErrorParse extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'errorlog:parse {--batch-size=20 : Number of logs to process in each batch} {--memory-limit=256M : Memory limit for the command} {--max-memory-mb=200 : Maximum memory usage in MB before forcing cleanup}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Rozparsuje logy a vytvoří z nich záznamy o chybách.';

    public function __construct(
        private readonly LogFacade   $logFacade,
        private readonly EntityManagerInterface $entityManager,
    ) {
        parent::__construct();
    }

    public function handle(): int
    {
        // Set memory limit if provided
        $memoryLimit = $this->option('memory-limit');
        if ($memoryLimit) {
            ini_set('memory_limit', $memoryLimit);
        }

        $batchSize = (int) $this->option('batch-size');
        $maxMemoryMb = (int) $this->option('max-memory-mb');

        // Get total count for progress tracking
        $totalLogs = $this->logFacade->countUnparsedLogs();
        $this->info("Found {$totalLogs} unparsed logs to process");
        $this->info("Using batch size: {$batchSize}, Memory limit: {$maxMemoryMb}MB");

        if ($totalLogs === 0) {
            $this->info('No logs to process');
            return 0;
        }

        $processed = 0;
        $offset = 0;
        $memoryThreshold = $maxMemoryMb * 1024 * 1024; // Convert MB to bytes
        $startTime = microtime(true);

        while ($offset < $totalLogs) {
            $this->info("Processing batch: " . ($offset + 1) . " to " . min($offset + $batchSize, $totalLogs) . " of {$totalLogs}");

            // Get only log IDs to minimize memory usage
            $logIds = $this->logFacade->getUnparsedLogIds($batchSize, $offset);

            if (empty($logIds)) {
                break;
            }

            // Process each log by ID individually with immediate cleanup
            foreach ($logIds as $logId) {
                $this->processLogById($logId);
                $processed++;

                // Check memory usage after each log
                $currentMemory = memory_get_usage(true);
                if ($currentMemory > $memoryThreshold) {
                    $this->warn("Memory usage high: " . round($currentMemory / 1024 / 1024, 2) . " MB - forcing cleanup");
                    $this->aggressiveCleanup();
                }

                // Force cleanup after every 10 logs
                if ($processed % 10 === 0) {
                    $this->aggressiveCleanup();
                }
            }

            // Clear everything after batch
            $this->aggressiveCleanup();

            // Update offset for next batch
            $offset += $batchSize;

            // Show memory usage
            $memoryUsage = round(memory_get_usage(true) / 1024 / 1024, 2);
            $this->info("Memory usage: {$memoryUsage} MB");
        }

        $endTime = microtime(true);
        $duration = round($endTime - $startTime, 2);
        $finalMemory = round(memory_get_usage(true) / 1024 / 1024, 2);

        $this->info("Successfully processed {$processed} logs in {$duration} seconds");
        $this->info("Final memory usage: {$finalMemory} MB");
        return 0;
    }

    /**
     * Process a single log by ID with maximum memory efficiency using raw data
     */
    private function processLogById(int $logId): void
    {
        try {
            // Get log data without creating entities
            $logData = $this->logFacade->getLogDataWithContent($logId);

            if (!$logData) {
                $this->warn("Log {$logId} not found or already parsed");
                return;
            }

            // Process using raw data to avoid entity overhead
            $this->processLogData($logData);

            // Mark as parsed using direct SQL
            $this->markLogAsParsed($logId);

            // Clear references
            unset($logData);

        } catch (\Exception $e) {
            $this->error("Error processing log {$logId}: " . $e->getMessage());
        }
    }

    /**
     * Process log data without creating Log entities
     */
    private function processLogData(array $logData): void
    {
        $connection = $this->entityManager->getConnection();

        // Handle BLOB content
        $content = $logData['content'];
        if (is_resource($content)) {
            $content = stream_get_contents($content);
        }

        // Parse trace files
        $traceFilePattern = '#^exception--([0-9]{4}-[0-9]{2}-[0-9]{2}--[0-9]{2}-[0-9]{2})--([a-z0-9]+)\.(html)$#';
        if (preg_match($traceFilePattern, $logData['filename'], $matches)) {
            $this->processTraceData($logData, $content, $matches, $connection);
        }

        // Parse error logs
        $errorLinePattern = "/\[([0-9\-\s]{19})] ([A-Za-z0-9\s\\\]+): (.*) in (.*):([0-9]+)\s+@\s+(\S*)(\s+@@\s+(.*))?/";
        if (preg_match_all($errorLinePattern, $content, $matches, PREG_SET_ORDER)) {
            $this->processErrorData($logData, $content, $matches, $connection);
        }

        // Clear content from memory immediately
        unset($content);
    }

    /**
     * Aggressive memory cleanup
     */
    private function aggressiveCleanup(): void
    {
        // Clear entity manager
        $this->entityManager->clear();

        // Force garbage collection
        gc_collect_cycles();

        // Clear any potential circular references
        if (function_exists('gc_mem_caches')) {
            gc_mem_caches();
        }
    }

    /**
     * Mark log as parsed using direct SQL
     */
    private function markLogAsParsed(int $logId): void
    {
        $connection = $this->entityManager->getConnection();
        $connection->update('log', ['parsed' => 1], ['id' => $logId]);
    }

    /**
     * Process trace data without entities
     */
    private function processTraceData(array $logData, string $content, array $matches, $connection): void
    {
        $hash = $matches[2];

        // Check if trace already exists
        $stmt = $connection->prepare('SELECT COUNT(*) FROM trace WHERE hash = ?');
        $stmt->bindValue(1, $hash);
        if ($stmt->executeQuery()->fetchOne() > 0) {
            return; // Trace already exists
        }

        // Insert new trace
        $connection->insert('trace', [
            'name' => $logData['filename'],
            'hash' => $hash,
            'content' => $content,
            'extension' => $matches[3],
            'mime_type' => 'text/html',
            'size' => strlen($content),
            'file_created' => \DateTimeImmutable::createFromFormat('Y-m-d--H-i', $matches[1])->format('Y-m-d H:i:s'),
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s'),
        ]);
    }

    /**
     * Process error data without entities
     */
    private function processErrorData(array $logData, string $content, array $matches, $connection): void
    {
        $connection->executeStatement('BEGIN');

        try {
            foreach ($matches as $match) {
                $this->insertErrorFromMatch($logData, $content, $match, $connection);
            }
            $connection->executeStatement('COMMIT');
        } catch (\Exception $e) {
            $connection->executeStatement('ROLLBACK');
            throw $e;
        }
    }

    /**
     * Insert error from regex match without entities
     */
    private function insertErrorFromMatch(array $logData, string $content, array $match, $connection): void
    {
        // Create checksum
        $checksumData = $match[2] . '|' . $match[3] . '|' . $match[4] . '|' . $match[5];
        $checksum = md5($checksumData);

        // Check if error already exists
        $stmt = $connection->prepare('SELECT id FROM error WHERE checksum = ?');
        $stmt->bindValue(1, $checksum);
        $existingError = $stmt->executeQuery()->fetchAssociative();

        if ($existingError) {
            // Mark existing error as unresolved
            $connection->update('error', ['resolved' => 0, 'user_id' => null], ['id' => $existingError['id']]);
            $errorId = $existingError['id'];
        } else {
            // Determine error type
            $errorType = $this->getErrorTypeValue($match[2]);
            $exception = $errorType === 'exception' ? $match[2] : null;

            // Get trace ID if exists
            $traceId = null;
            if (isset($match[8])) {
                $traceStmt = $connection->prepare('SELECT id FROM trace WHERE name = ?');
                $traceStmt->bindValue(1, $match[8]);
                $traceResult = $traceStmt->executeQuery()->fetchAssociative();
                $traceId = $traceResult['id'] ?? null;
            }

            // Insert new error
            $connection->insert('error', [
                'type' => $errorType,
                'message' => $match[3],
                'file' => $match[4],
                'line' => (int) $match[5],
                'original' => $content,
                'url' => $match[6],
                'checksum' => $checksum,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
                'app_id' => $logData['app_id'],
                'filename' => $logData['filename'],
                'exception' => $exception,
                'resolved' => 0,
                'trace_id' => $traceId
            ]);

            $errorId = $connection->lastInsertId();
        }

        // Insert occurrence
        $connection->insert('occurrence', [
            'error_id' => $errorId,
            'date' => \DateTimeImmutable::createFromFormat('Y-m-d H-i-s', $match[1])->format('Y-m-d H:i:s'),
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s'),
        ]);
    }

    /**
     * Get error type value from title
     */
    private function getErrorTypeValue(string $title): string
    {
        // Map common PHP error types
        $errorTypes = [
            'Fatal error' => 'fatal_error',
            'Parse error' => 'parse_error',
            'Warning' => 'warning',
            'Notice' => 'notice',
            'Strict Standards' => 'strict_standards',
            'Deprecated' => 'deprecated',
        ];

        return $errorTypes[$title] ?? 'exception';
    }
}
