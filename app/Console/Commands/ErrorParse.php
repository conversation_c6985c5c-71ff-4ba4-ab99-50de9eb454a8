<?php

namespace App\Console\Commands;

use App\Domain\Parsers\ParserFactory;
use App\Model\Entities\Log;
use App\Model\Facades\LogFacade;
use Doctrine\ORM\EntityManagerInterface;
use Illuminate\Console\Command;

class ErrorParse extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'errorlog:parse {--batch-size=100 : Number of logs to process in each batch} {--memory-limit=256M : Memory limit for the command}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Rozparsuje logy a vytvoří z nich záznamy o chybách.';

    public function __construct(
        private readonly LogFacade   $logFacade,
        private readonly EntityManagerInterface $entityManager,
    ) {
        parent::__construct();
    }

    public function handle(): int
    {
        // Set memory limit if provided
        $memoryLimit = $this->option('memory-limit');
        if ($memoryLimit) {
            ini_set('memory_limit', $memoryLimit);
        }

        $batchSize = (int) $this->option('batch-size');
        $factory = new ParserFactory();

        // Get total count for progress tracking
        $totalLogs = $this->logFacade->countUnparsedLogs();
        $this->info("Found {$totalLogs} unparsed logs to process");

        if ($totalLogs === 0) {
            $this->info('No logs to process');
            return 0;
        }

        $processed = 0;
        $offset = 0;

        while ($offset < $totalLogs) {
            $this->info("Processing batch: " . ($offset + 1) . " to " . min($offset + $batchSize, $totalLogs) . " of {$totalLogs}");

            // Get batch of logs
            $logs = $this->logFacade->getLogsBatch($batchSize, $offset);

            if (empty($logs)) {
                break;
            }

            // Process each log in the batch
            foreach ($logs as $log) {
                $this->processLog($log, $factory);
                $processed++;
            }

            // Clear entity manager to free memory
            $this->entityManager->clear();

            // Force garbage collection
            gc_collect_cycles();

            // Update offset for next batch
            $offset += $batchSize;

            // Show memory usage
            $memoryUsage = round(memory_get_usage(true) / 1024 / 1024, 2);
            $this->info("Memory usage: {$memoryUsage} MB");
        }

        $this->info("Successfully processed {$processed} logs");
        return 0;
    }

    /**
     * Process a single log for both trace and error parsing
     */
    private function processLog(Log $log, ParserFactory $factory): void
    {
        try {
            $parser = $factory->createParser($log->getLogger());
            $parser->setup($this->entityManager);

            // Parse trace first
            $traceResult = $parser->parseTrace($log);

            // Parse errors
            $errorResult = $parser->parseErrors($log);

            // Mark as parsed if either operation succeeded
            if ($traceResult || $errorResult) {
                $this->logParsed($log);
            }

        } catch (\Exception $e) {
            $this->error("Error processing log {$log->getId()}: " . $e->getMessage());
        }
    }

    private function logParsed(Log $log): void
    {
        $connection = $this->entityManager->getConnection();
        $log->markAsParsed();
        $connection->update('log', ['parsed' => 1], ['id' => $log->getId()]);
    }
}
