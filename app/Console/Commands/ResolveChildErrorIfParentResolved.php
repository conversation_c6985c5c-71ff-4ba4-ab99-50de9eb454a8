<?php

namespace App\Console\Commands;

use App\Model\Entities\Error;
use App\Model\Facades\ErrorFacade;
use Illuminate\Console\Command;

class ResolveChildErrorIfParentResolved extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'errorlog:recursive-resolve {errorId}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Pokud je rodič<PERSON>sk<PERSON> chyba vyř<PERSON>, vyřeš<PERSON> i všechny její potomky.';

    public function __construct(
        private readonly ErrorFacade $facade,
    )
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $id = (int) $this->argument('errorId');
        $error = $this->facade->getErrorById($id);

        if ($error && $error->getDependants()->count() > 0) {
            $this->resolveDependants($error);
        }

        return Command::SUCCESS;
    }

    protected function resolveDependants(Error $error): void
    {
        foreach ($error->getDependants() as $dependant) {
            $dependant->resolve();
            $this->facade->saveError($dependant);
            if ($dependant->getDependants()->count() > 0) {
                $this->resolveDependants($dependant);
            }
        }
    }
}
