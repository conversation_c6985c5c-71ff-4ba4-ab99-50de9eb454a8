<?php

namespace App\Model\Entities;

use App\Model\Helpers\EntityToArray;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use ReflectionClass;

abstract class AbstractEntity implements IEntity
{
    use Traits\TId;
    use Traits\TReportable;


    public function toArray(): array
    {
        $helper = new EntityToArray($this);
        return $helper->toArray();
    }

    public function fill(array $data): void
    {
        foreach ($data as $colName => $val) {
            $setter = 'set' . ucfirst($colName);
            $adder = 'add' . ucfirst($colName);
            if (method_exists($this, $setter)) {
                $this->$setter($val);
            } elseif (method_exists($this, $adder)) {
                $this->$adder($val);
            }
        }
    }

    public static function create(array $values): IEntity
    {
        if (($diff = array_diff(static::getRequired(), array_keys($values))) !== []) {
            throw new \InvalidArgumentException('Required properties missing: ' . implode(', ', $diff));
        }
        $requiredValues = array_filter($values, fn ($k) => in_array($k, self::getRequired()), ARRAY_FILTER_USE_KEY);
        $entity = new static(...$requiredValues);
        $entity->fill($values);
        return $entity;
    }

    private function bindWithDeprecated(IEntity $entity, ?string $inversedBy = null): void
    {
        if ($inversedBy === null) {
            $inversedBy = lcfirst(substr(strrchr($this::class, '\\'), 1));
            if (property_exists($entity, $inversedBy . 's')) {
                $inversedBy = $inversedBy . 's';
            }
        }

        if (!property_exists($entity, $inversedBy)) {
            throw new \RuntimeException('Entity ' . $entity::class . ' doesn\'t have property ' . $inversedBy);
        }

        $getter = 'get' . ucfirst($inversedBy);
        $binded = $entity->$getter();

        if ($binded === null) {
            $setter = 'set' . ucfirst($inversedBy);
            $entity->$setter($this);
        } elseif ($binded instanceof Collection && !$binded->contains($this)) {
            $adder = 'add' . ucfirst(substr($inversedBy, 0, -1));  // 's' na konci (mnozne cislo)
            $entity->$adder($this);
        }
    }

    protected function bindWith(IEntity $entity, ?string $inversedGetter = null, ?string $inversedSetter = null): void
    {
        if ($inversedGetter === null) {
            $inversedGetter = 'get' . substr(strrchr($this::class, '\\'), 1);
            if (method_exists($entity, $inversedGetter . 's')) {
                $inversedGetter .= 's';
            }
        } elseif (!preg_match('/^get[A-Z]/', $inversedGetter)) {
            // deprecated
            $this->bindWithDeprecated($entity, $inversedGetter);
            return;
        }

        if (!method_exists($entity, $inversedGetter)) {
            throw new \RuntimeException('Entity ' . $entity::class . ' doesn\'t have method ' . $inversedGetter);
        }

        $binded = $entity->$inversedGetter();

        if ($binded === null || ($binded instanceof Collection && !$binded->contains($this))) {
            if ($inversedSetter === null) {
                $inversedSetter = ($binded === null ? 'set' : 'add') . substr(strrchr($this::class, '\\'), 1);
            }
            if (!method_exists($entity, $inversedSetter)) {
                throw new \RuntimeException('Entity ' . $entity::class . ' doesn\'t have method ' . $inversedSetter);
            }
            $entity->$inversedSetter($this);
        }
    }

    public static function getRequired(): array
    {
        $required = [];
        $reflection = new ReflectionClass(static::class);
        foreach ($reflection->getProperties() as $property) {
            $generated = $property->getAttributes(ORM\GeneratedValue::class);
            if (isset($generated[0])) {
                continue;
            }
            $column = $property->getAttributes(ORM\Column::class);
            if (isset($column[0])) {
                $default = $property->getDefaultValue();
                $args = $column[0]->getArguments();
                $nullable = $args['nullable'] ?? false;
                if ($default === null && $nullable === false) {
                    $required[] = $property->getName();
                }
            }
        }
        return $required;
    }

    protected function filterDeleted(Collection $collection): Collection
    {
        return $collection->filter(function ($entity) {
            return !$entity->isDeleted();
        });
    }
}
