<?php

namespace App\Model\Entities;

use App\Model\Repositories\Repository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: Repository::class)]
#[ORM\HasLifecycleCallbacks]
class Comment extends AbstractEntity
{
    #[ORM\ManyToOne(targetEntity: "Error", inversedBy: "comments")]
    protected ?Error $error = null;

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    protected ?string $text = null;

    #[ORM\ManyToOne(targetEntity: "User", inversedBy: "comments")]
    protected ?User $user = null;

    public function setError(Error $error): self
    {
        $this->error = $error;
        $this->bindWith($error);
        return $this;
    }

    public function getError(): ?Error
    {
        return $this->error;
    }

    public function setText(?string $text = null): self
    {
        $this->text = $text;
        return $this;
    }

    public function getText(): ?string
    {
        return $this->text;
    }

    public function setUser(User $user): self
    {
        $this->user = $user;
        $this->bindWith($user);
        return $this;
    }

    public function getUser(): ?User
    {
        return $this->user;
    }
}
