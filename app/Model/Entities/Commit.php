<?php

namespace App\Model\Entities;

use App\Model\Repositories\Repository;
use Doctrine\Common\Collections\Collection;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\ORM\Mapping as ORM;
use Doctrine\ORM\Mapping\UniqueConstraint;

#[ORM\Entity(repositoryClass: Repository::class)]
#[UniqueConstraint(name: "hash", columns: ["hash"])]
#[ORM\HasLifecycleCallbacks]
class Commit extends AbstractEntity
{
    #[ORM\ManyToMany(targetEntity: "Error", inversedBy: "commits", cascade: ['persist'])]
    protected Collection $errors;

    #[ORM\Column]
    protected string $hash;

    public function __construct()
    {
        $this->errors = new ArrayCollection();
    }


    public function addError(Error $error): self
    {
        $this->errors[] = $error;
        $this->bindWith($error);
        return $this;
    }

    public function getErrors(): Collection
    {
        return $this->errors;
    }

    public function setHash(string $hash): self
    {
        $this->hash = $hash;
        return $this;
    }

    public function getHash(): string
    {
        return $this->hash;
    }
}
