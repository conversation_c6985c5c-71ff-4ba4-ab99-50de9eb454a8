<?php

namespace App\Model\Entities;

use App\Model\Repositories\Repository;
use DateTimeImmutable;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: Repository::class)]
#[ORM\HasLifecycleCallbacks]
class Occurrence extends AbstractEntity
{
    #[ORM\ManyToOne(targetEntity: "Error", inversedBy: "occurrences", cascade: ['persist'])]
    protected ?Error $error = null;

    #[ORM\Column(type: Types::DATETIME_IMMUTABLE)]
    protected DateTimeImmutable $date;

    public function setError(Error $error): self
    {
        $this->error = $error;
        $this->bindWith($error);
        return $this;
    }

    public function getError(): ?Error
    {
        return $this->error;
    }

    public function setDate(DateTimeImmutable $date): self
    {
        $this->date = $date;
        return $this;
    }

    public function getDate(): DateTimeImmutable
    {
        return $this->date;
    }

    public function getLogLine(): string
    {
        $error = $this->getError();

        $line = '[' . $this->date->format('Y-m-d H-i-s') . '] '
            . ($error->getTypeOrException())
            . ': ' . $error->getMessage()
            . ' in ' . $error->getFile()
            . ':' . $error->getLine()
            . ' @ ' . $error->getUrl();

        if ($error->getTrace()) {
            $line .= ' @@ ' . $error->getTrace()?->getName();
        }

        return $line;
    }
}
