<?php

namespace App\Model\Entities;

use App\Enums\Logger;
use App\Model\Repositories\Repository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: Repository::class)]
#[ORM\HasLifecycleCallbacks]
class Log extends AbstractEntity
{
//    #[ORM\ManyToOne(targetEntity: "App", inversedBy: "logs", cascade: ["persist"])]
    #[ORM\ManyToOne]
    protected ?App $app = null;

    #[ORM\Column]
    protected string $filename;

    #[ORM\Column(type: Types::BLOB)]
    protected /*resource*/ $content;

    #[ORM\Column(type: Types::STRING, enumType: Logger::class)]
    protected Logger $logger;

    #[ORM\Column]
    protected string $loggerVersion;

    #[ORM\Column]
    protected bool $parsed = false;

    public function setApp(App $app): self
    {
        $this->app = $app;
//        $this->bindWith($app);
        return $this;
    }

    public function getApp(): ?App
    {
        return $this->app;
    }

    public function setFilename(string $filename): self
    {
        $this->filename = $filename;
        return $this;
    }

    public function getFilename(): string
    {
        return $this->filename;
    }

    public function setContent(string $content): self
    {
        $this->content = $content;
        return $this;
    }

    public function getContent(): string
    {
        if (is_resource($this->content)) {
            $this->content = stream_get_contents($this->content);
        }
        return $this->content;
    }

    public function setLogger(Logger $logger): Log
    {
        $this->logger = $logger;
        return $this;
    }

    public function getLogger(): Logger
    {
        return $this->logger;
    }

    public function setLoggerVersion(string $loggerVersion): self
    {
        $this->loggerVersion = $loggerVersion;
        return $this;
    }

    public function getLoggerVersion(): string
    {
        return $this->loggerVersion;
    }

    public function markAsParsed(): self
    {
        $this->parsed = true;
        return $this;
    }

    public function isParsed(): bool
    {
        return $this->parsed;
    }
}
