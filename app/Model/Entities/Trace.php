<?php

namespace App\Model\Entities;

use App\Model\Repositories\Repository;
use DateTimeImmutable;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: Repository::class)]
#[ORM\HasLifecycleCallbacks]
#[ORM\UniqueConstraint(columns: ['hash'])]
class Trace extends AbstractEntity
{
    #[ORM\OneToMany(targetEntity: "Error", mappedBy: "trace")]
    protected Collection $errors;

    #[ORM\Column]
    protected string $name;

    #[ORM\Column]
    protected string $hash;

    #[ORM\Column(type: Types::BLOB, nullable: true)]
    protected $content = null;

    #[ORM\Column(type: Types::JSON, nullable: true)]
    protected ?string $stack = null;

    #[ORM\Column]
    protected string $extension;

    #[ORM\Column]
    protected string $mimeType;

    #[ORM\Column]
    protected int $size;

    #[ORM\Column(type: Types::DATETIME_IMMUTABLE)]
    protected DateTimeImmutable $fileCreated;

    public function __construct()
    {
        $this->errors = new ArrayCollection();
    }

    public function addError(Error $error): self
    {
        $this->errors[] = $error;
        $this->bindWith($error);
        return $this;
    }

    public function getErrors(): Collection
    {
        return $this->errors;
    }

    public function setName(string $name): self
    {
        $this->name = $name;
        return $this;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function setHash(string $hash): self
    {
        $this->hash = $hash;
        return $this;
    }

    public function getHash(): string
    {
        return $this->hash;
    }

    public function setContent(string $content): self
    {
        $this->content = $content;
        return $this;
    }

    public function getContent(): string
    {
        if (is_resource($this->content)) {
            $this->content = stream_get_contents($this->content);
        }

        return $this->content;
    }

    public function setExtension(string $extension): self
    {
        $this->extension = $extension;
        return $this;
    }

    public function getExtension(): string
    {
        return $this->extension;
    }

    public function setMimeType(string $mimeType): self
    {
        $this->mimeType = $mimeType;
        return $this;
    }

    public function getMimeType(): string
    {
        return $this->mimeType;
    }

    public function setSize(int $size): self
    {
        $this->size = $size;
        return $this;
    }

    public function getSize(): int
    {
        return $this->size;
    }

    public function setFileCreated(DateTimeImmutable $fileCreated): self
    {
        $this->fileCreated = $fileCreated;
        return $this;
    }

    public function getFileCreated(): DateTimeImmutable
    {
        return $this->fileCreated;
    }
}
