<?php

declare(strict_types=1);

namespace App\Model\Entities\Traits;

use Doctrine\ORM\Mapping as ORM;

trait TImage
{
    #[ORM\Column(nullable: true)]
    protected ?string $img = null;


    public function getImg(bool $asString = false): \SplFileInfo|string|null
    {
        $img = is_string($this->img) ? public_path($this->img) : null;
        if ($asString) {
            return (string) $img;  // null na prazdny retezec
        }
        return $img !== null ? new \SplFileInfo($img) : null;
    }

    public function getImgUrl(): ?string
    {
        return $this->img;
    }

    public function setImg(\SplFileInfo|string|null $img): void
    {
        if ($img instanceof \SplFileInfo) {
            $img = $img->getPathname();
        }
        if (is_string($img)) {
            $img = preg_replace('#^'.preg_quote(public_path()).'#', '', $img);
        }
        $this->img = $img ?: null;  // prazdny retezec na null
    }
}
