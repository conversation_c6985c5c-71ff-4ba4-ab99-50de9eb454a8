<?php

declare(strict_types=1);

namespace App\Model\Entities\Traits;

use Doctrine\ORM\Mapping as ORM;

trait TPerson
{
    #[ORM\Column(length: 30, nullable: true)]
    protected ?string $degree_before = null;

    #[ORM\Column(length: 30, nullable: true)]
    protected ?string $degree_after = null;

    #[ORM\Column]
    protected string $first_name;

    #[ORM\Column]
    protected string $last_name;

    /**
     * @param ?string $degreeBefore
     * @return void
     */
    public function setDegreeBefore(?string $degreeBefore): void
    {
        $this->degree_before = $degreeBefore;
    }

    /**
     * @param ?string $degreeAfter
     * @return void
     */
    public function setDegreeAfter(?string $degreeAfter): void
    {
        $this->degree_after = $degreeAfter;
    }

    /**
     * @param string $firstName
     * @return void
     */
    public function setFirstName(string $firstName): void
    {
        $this->first_name = $firstName;
    }

    /**
     * @param string $lastName
     * @return void
     */
    public function setLastName(string $lastName): void
    {
        $this->last_name = $lastName;
    }

    /**
     * @return string
     */
    public function getDegreeBefore(): ?string
    {
        return $this->degree_before;
    }

    /**
     * @return string
     */
    public function getDegreeAfter(): ?string
    {
        return $this->degree_after;
    }

    /**
     * @return string
     */
    public function getFirstName(): string
    {
        return $this->first_name;
    }

    /**
     * @return string
     */
    public function getLastName(): string
    {
        return $this->last_name;
    }

    /**
     * @param bool $degreesIncluded
     * @param bool $lastNameFirst
     * @return string
     */
    public function getFullName(bool $degreesIncluded = true, bool $lastNameFirst = false): string
    {
        $fullName = $lastNameFirst
                ? trim($this->getLastName() . ' ' . $this->getFirstName())
                : trim($this->getFirstName() . ' ' . $this->getLastName());

        return $degreesIncluded
                ? trim($this->getDegreeBefore() . ' ' . $fullName . ' ' . $this->getDegreeAfter())
                : $fullName;
    }
}
