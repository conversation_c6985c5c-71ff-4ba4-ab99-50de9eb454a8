<?php

declare(strict_types=1);

namespace App\Model\Entities\Traits;

use Doctrine\ORM\Mapping as ORM;
use Nette\Utils\Validators;

trait TContact
{
    #[ORM\Column(nullable: true)]
    protected ?string $email = null;

    #[ORM\Column(nullable: true)]
    protected ?string $phone = null;

    /**
     * @param ?string $email
     * @return void
     * @throws \InvalidArgumentException
     */
    public function setEmail(?string $email): void
    {
        if ($email && !Validators::isEmail($email)) {
            throw new \InvalidArgumentException(__METHOD__ . '(): parameter $email must be valid email string.');
        }
        $this->email = $email;
    }

    /**
     * @param ?string $phone
     * @return void
     */
    public function setPhone(?string $phone): void
    {
        if ($phone !== null) {
            if (!preg_match('/^(\+|00)[0-9]+/', $phone)) {
                throw new \Nette\InvalidArgumentException(__METHOD__ . '(): parameter $phone must include international prefix.');
            }
            //$this->phone = preg_replace(['#^\+#', '#\s+#'], ['00', ''], $phone);  // ukladat s '00420'
            $phone = preg_replace(['#^00#', '#\s+#'], ['+', ''], $phone);  // ukladat s '+420'
        }

        $this->phone = $phone;
    }

    /**
     * @return ?string
     */
    public function getEmail(): ?string
    {
        return $this->email;
    }

    public function getPhone(): ?string
    {
        return $this->phone;
    }

    public function getPhoneFormated(int $numGroupLength = 3): ?string
    {
        if ($this->phone) {
            return preg_replace('/([0-9]{'.$numGroupLength.'})/', '$1 ', $this->phone);
        } else {
            return null;
        }
    }
}
