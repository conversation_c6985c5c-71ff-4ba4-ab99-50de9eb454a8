<?php

declare(strict_types=1);

namespace App\Model\Entities\Traits;

use DateTimeImmutable;
use Doctrine\ORM\Mapping as ORM;

/**
 * Entita musi mit ORM\HasLifecycleCallbacks atribut
 */
trait TReportable
{
    #[ORM\Column(nullable: true)]
    protected ?DateTimeImmutable $createdAt = null;

    #[ORM\Column(nullable: true)]
    protected ?DateTimeImmutable $updatedAt = null;

    /**
     * @param ?string $format
     * @return DateTimeImmutable|string|null
     */
    public function getCreatedAt(?string $format = null): DateTimeImmutable|string|null
    {
        if ($this->createdAt) {
            return $format ? $this->createdAt->format($format) : $this->createdAt;
        } else {
            return null;
        }
    }

    /**
     * @param ?string $format
     * @return DateTimeImmutable|string|null
     */
    public function getUpdatedAt(?string $format = null): DateTimeImmutable|string|null
    {
        if ($this->updatedAt) {
            return $format ? $this->updatedAt->format($format) : $this->updatedAt;
        } else {
            return null;
        }
    }

    #[ORM\PrePersist]
    #[ORM\PreUpdate]
    public function report(): void
    {
        $this->updatedAt = new DateTimeImmutable('now');
        if ($this->createdAt === null) {
            $this->createdAt = new DateTimeImmutable('now');
        }
    }
}
