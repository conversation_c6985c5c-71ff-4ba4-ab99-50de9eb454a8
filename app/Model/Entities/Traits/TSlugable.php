<?php

declare(strict_types=1);

namespace App\Model\Entities\Traits;

use Doctrine\ORM\Mapping as ORM;
use Nette\Utils\Strings;

trait TSlugable
{
    #[ORM\Column(length: 30)]
    protected string $title;

    #[ORM\Column(unique: true)]
    protected string $slug;

    public function setTitle(string $title): void
    {
        $this->title = $title;
        $this->setSlug();
    }

    public function setSlug(?string $slug = null)
    {
        $this->slug = $slug ?: Strings::webalize($this->getTitle());
    }

    public function getTitle(): string
    {
        return $this->title;
    }

    public function getSlug(): string
    {
        return $this->slug;
    }
}
