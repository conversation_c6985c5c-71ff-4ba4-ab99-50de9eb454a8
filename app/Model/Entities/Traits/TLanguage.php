<?php

declare(strict_types=1);

namespace App\Model\Entities\Traits;

use App\Enums\Lang;
use Doctrine\ORM\Mapping as ORM;

trait TLanguage
{
    #[ORM\Column(type: 'string', enumType: Lang::class)]
    protected Lang $lang = Lang::CZ;

    public function setLang(Lang $lang): void
    {
        $this->lang = $lang;
    }

    public function getLang(): Lang
    {
        return $this->lang;
    }
}
