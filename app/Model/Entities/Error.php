<?php

namespace App\Model\Entities;

use App\Enums\ErrorStatus;
use App\Enums\ErrorType;
use App\Model\Repositories\Repository;
use DateTimeImmutable;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\Common\Collections\Criteria;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Doctrine\ORM\Mapping\UniqueConstraint;

#[ORM\Entity(repositoryClass: Repository::class)]
#[UniqueConstraint(name: "checksum", columns: ["checksum"])]
#[ORM\HasLifecycleCallbacks]
class Error extends AbstractEntity
{
    #[ORM\ManyToOne(targetEntity: "App", inversedBy: "errors", cascade: ["persist"])]
    protected ?App $app = null;

    #[ORM\ManyToOne(targetEntity: "Trace", inversedBy: "errors", cascade: ["persist"])]
    protected ?Trace $trace = null;

    #[ORM\Column(type: Types::INTEGER, enumType: ErrorType::class)]
    protected ErrorType $type;

    #[ORM\Column(type: Types::TEXT)]
    protected string $message;

    #[ORM\Column]
    protected string $file;

    #[ORM\Column]
    protected string $filename;

    #[ORM\Column]
    protected int $line;

    #[ORM\Column(type: Types::BLOB)]
    protected /*resource*/ $original;

    #[ORM\Column(length: 2048)]
    protected string $url;

    #[ORM\Column]
    protected string $checksum;

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    protected ?string $exception = null;

    #[ORM\Column]
    protected bool $resolved = false;

    #[ORM\ManyToOne(targetEntity: Error::class, inversedBy: "dependants")]
    #[ORM\JoinColumn(name: "dependence", referencedColumnName: "id")]
    protected ?Error $dependsOn = null;

    #[ORM\ManyToOne(targetEntity: "User", inversedBy: "errors")]
    protected ?User $user = null;

    #[ORM\OneToMany(targetEntity: "Comment", mappedBy: "error")]
    protected Collection $comments;

    #[ORM\OneToMany(targetEntity: "Occurrence", mappedBy: "error", cascade: ["persist"])]
    #[ORM\OrderBy(["date" => "ASC"])]
    protected Collection $occurrences;

    #[ORM\OneToOne(targetEntity: "Issue", mappedBy: "error")]
    protected ?Issue $issue = null;

    #[ORM\OneToMany(targetEntity: "Activity", mappedBy: "error")]
    protected Collection $activities;

    #[ORM\ManyToMany(targetEntity: "Commit", mappedBy: "errors")]
    protected Collection $commits;

    #[ORM\OneToMany(targetEntity: Error::class, mappedBy: "dependsOn")]
    protected Collection $dependants;

    #[ORM\Column(nullable: true)]
    protected ?DateTimeImmutable $closedAt = null;

    public function __construct()
    {
        $this->comments = new ArrayCollection();
        $this->occurrences = new ArrayCollection();
        $this->commits = new ArrayCollection();
        $this->dependants = new ArrayCollection();
        $this->activities = new ArrayCollection();
    }

    public function setApp(App $app): self
    {
        $this->app = $app;
        $this->bindWith($app);
        return $this;
    }

    public function getApp(): ?App
    {
        return $this->app;
    }

    public function setTrace(Trace $trace): self
    {
        $this->trace = $trace;
        $this->bindWith($trace);
        return $this;
    }

    public function getTrace(): ?Trace
    {
        return $this->trace;
    }

    public function setType(ErrorType $type): self
    {
        $this->type = $type;
        return $this;
    }

    public function getType(): ErrorType
    {
        return $this->type;
    }

    public function getTypeOrException(): string
    {
        return $this->exception ?? $this->type->getTitle();
    }

    public function setMessage(string $message): self
    {
        $this->message = $message;
        return $this;
    }

    public function getMessage(): string
    {
        return $this->message;
    }

    public function setFile(string $file): self
    {
        $this->file = $file;
        return $this;
    }

    public function getFile(): string
    {
        return $this->file;
    }

    public function setFilename(string $filename): self
    {
        $this->filename = $filename;
        return $this;
    }

    public function getFilename(): string
    {
        return $this->filename;
    }

    public function setLine(int $line): self
    {
        $this->line = $line;
        return $this;
    }

    public function getLine(): int
    {
        return $this->line;
    }

    public function setOriginal(string $original): self
    {
        $this->original = $original;
        return $this;
    }

    public function getOriginal(): string
    {
        if (is_resource($this->original)) {
            $this->original = stream_get_contents($this->original);
        }
        return $this->original;
    }

    public function setUrl(string $url): self
    {
        $this->url = $url;
        return $this;
    }

    public function getUrl(): string
    {
        return $this->url;
    }

    public function setException(?string $exception): self
    {
        $this->exception = $exception;
        return $this;
    }

    public function getException(): ?string
    {
        return $this->exception;
    }

    public function resolve(bool $resolved = true): self
    {
        $this->resolved = $resolved;
        $this->closedAt = new DateTimeImmutable();
        return $this;
    }

    public function unresolve(bool $resolved = false): self
    {
        $this->resolved = $resolved;
        $this->closedAt = null;
        return $this;
    }

    public function isResolved(): bool
    {
        return $this->resolved;
    }

    public function getStatus(): ErrorStatus
    {
        if ($this->isResolved()) {
            return ErrorStatus::RESOLVED;
        } elseif ($this->getUser() !== null) {
            return ErrorStatus::ASSIGNED;
        } else {
            return ErrorStatus::OPEN;
        }
    }

    public function addOccurrence(Occurrence $occurrence): self
    {
        $this->occurrences[] = $occurrence;
        $this->bindWith($occurrence);
        return $this;
    }

    public function getOccurrences(): Collection
    {
        return $this->occurrences;
    }

    public function getFirstOccurrence(): ?Occurrence
    {
        return $this->occurrences->first();
    }

    public function getLastOccurrence(): ?Occurrence
    {
        return $this->occurrences->last();
    }

    public function createChecksum(): self
    {
        try {
            $this->checksum = md5($this->app->getSlug() . $this->getFilename() . $this->getType()->value . $this->getException() . $this->getMessage() . $this->getFile() . $this->getLine());
            return $this;
        } catch (\Error) {
            throw new \LogicException('Unable to create checksum, some fields has not been set');
        }
    }

    public function getChecksum(): string
    {
        return $this->checksum;
    }

    public function setUser(?User $user): self
    {
        $this->user = $user;
        $this->bindWith($user);
        return $this;
    }

    public function unsetUser(): self
    {
        $this->user = null;
        return $this;
    }

    public function getUser(): ?User
    {
        return $this->user;
    }

    public function hasUser(): bool
    {
        return $this->user !== null;
    }

    public function addComment(Comment $comment): self
    {
        $this->comments[] = $comment;
        $this->bindWith($comment);
        return $this;
    }

    public function getComments(): Collection
    {
        return $this->comments->matching(
            Criteria::create()->orderBy(['createdAt' => 'DESC'])
        );
    }

    public function setIssue(Issue $issue): self
    {
        $this->issue = $issue;
        return $this;
    }

    public function getIssue(): ?Issue
    {
        return $this->issue;
    }

    public function addCommit(Commit $commit): self
    {
        $this->commits[] = $commit;
        $this->bindWith($commit);
        return $this;
    }

    public function getCommits(): Collection
    {
        return $this->commits;
    }

    public function addActivity(Activity $activity): self
    {
        $this->activities[] = $activity;
        $this->bindWith($activity);
        return $this;
    }

    public function getActivities(): Collection
    {
        return $this->activities;
    }

    public function addDependant(Error $dependant): self
    {
        $this->dependants[] = $dependant;
        $this->bindWith($dependant, "dependsOn");
        return $this;
    }

    public function getDependants(): Collection
    {
        return $this->dependants;
    }

    public function setDependsOn(Error $dependsOn): self
    {
        $this->dependsOn = $dependsOn;
        $this->bindWith($dependsOn, "dependants");
        return $this;
    }

    public function getDependsOn(): ?Error
    {
        return $this->dependsOn ?? null;
    }

    public function getClosedAt(): ?DateTimeImmutable
    {
        return $this->closedAt;
    }
}
