<?php

namespace App\Model\Entities;

use App\Enums\SSHAlgo;
use App\Model\Repositories\Repository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Doctrine\ORM\Mapping\UniqueConstraint;

#[ORM\Entity(repositoryClass: Repository::class)]
#[UniqueConstraint(name: "slug", columns: ["slug"])]
#[UniqueConstraint(name: "gitlab_id", columns: ["gitlab_id"])]
#[ORM\HasLifecycleCallbacks]
class App extends AbstractEntity
{
    #[ORM\Column]
    protected string $title;

    #[ORM\Column]
    protected string $slug;

    #[ORM\Column(nullable: true)]
    protected ?string $domain = null;

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    protected ?string $ssh = null;

    #[ORM\Column(type: Types::INTEGER, enumType: SSHAlgo::class, nullable: true)]
    protected ?SSHAlgo $sshAlgo = null;

    #[ORM\Column(nullable: true)]
    protected ?string $oauthClientId = null;

    #[ORM\Column]
    protected int $gitlabId;

    #[ORM\Column]
    protected string $gitlabLabel;

    #[ORM\Column]
    protected string $gitlabGroup;

//    #[ORM\OneToMany(targetEntity: "Log", mappedBy: "app")]
//    protected Collection $logs;

    #[ORM\OneToMany(targetEntity: "Error", mappedBy: "app")]
    protected Collection $errors;

    #[ORM\ManyToMany(targetEntity: "User", mappedBy: "hiddenApps")]
    protected Collection $userHiddenApps;

    #[ORM\ManyToMany(targetEntity: "User", mappedBy: "mailboxAlerts")]
    protected Collection $userMailboxAlerts;

    public function __construct()
    {
//        $this->logs = new ArrayCollection();
        $this->errors = new ArrayCollection();
        $this->userHiddenApps = new ArrayCollection();
        $this->userMailboxAlerts = new ArrayCollection();
    }

    public function setTitle(string $title): self
    {
        $this->title = $title;
        return $this;
    }

    public function getTitle(): string
    {
        return $this->title;
    }

    public function setSlug(string $slug): self
    {
        $this->slug = $slug;
        return $this;
    }

    public function getSlug(): string
    {
        return $this->slug;
    }

    public function setDomain(?string $domain = null): self
    {
        $this->domain = $domain;
        return $this;
    }

    public function getDomain(): ?string
    {
        return $this->domain;
    }

    public function setSsh(?string $ssh): self
    {
        $this->ssh = $ssh;
        return $this;
    }

    public function getSsh(): ?string
    {
        return $this->ssh;
    }

    public function setSshAlgo(?SSHAlgo $sshAlgo): self
    {
        $this->sshAlgo = $sshAlgo;
        return $this;
    }

    public function getSshAlgo(): ?SSHAlgo
    {
        return $this->sshAlgo;
    }

    public function getOauthClientId(): ?string
    {
        return $this->oauthClientId;
    }

    public function setOauthClientId(?string $oauthClientId): self
    {
        $this->oauthClientId = $oauthClientId;
        return $this;
    }

    public function setGitlabId(int $gitlabId): self
    {
        $this->gitlabId = $gitlabId;
        return $this;
    }

    public function getGitlabId(): int
    {
        return $this->gitlabId;
    }

    public function setGitlabLabel(string $gitlabLabel): self
    {
        $this->gitlabLabel = $gitlabLabel;
        return $this;
    }

    public function getGitlabLabel(): string
    {
        return $this->gitlabLabel;
    }

    public function setGitlabGroup(string $gitlabGroup): self
    {
        $this->gitlabGroup = $gitlabGroup;
        return $this;
    }

    public function getGitlabGroup(): string
    {
        return $this->gitlabGroup;
    }

//    public function addLog(Log $log): self
//    {
//        $this->logs[] = $log;
//        $this->bindWith($log);
//        return $this;
//    }
//
//    public function getLogs(): Collection
//    {
//        return $this->logs;
//    }

    public function addError(Error $error): self
    {
        $this->errors[] = $error;
        $this->bindWith($error);
        return $this;
    }

    public function getErrors(): Collection
    {
        return $this->errors->filter(fn (Error $error) => $error->getDependsOn() === null);
    }

    public function getUnresolvedErrors(): Collection
    {
        return $this->errors->filter(fn (Error $error) => !$error->isResolved() && $error->getDependsOn() === null);
    }

    public function addUserHiddenApp(User $user): self
    {
        $this->userHiddenApps[] = $user;
        $this->bindWith($user, 'getUserHiddenApps', 'addUserHiddenApp');
        return $this;
    }

    public function removeUserHiddenApp(User $user): self
    {
        $this->userHiddenApps->removeElement($user);
        return $this;
    }

    public function getUserHiddenApps(): Collection
    {
        return $this->userHiddenApps;
    }

    public function addMailboxAlert(User $user): self
    {
        $this->userMailboxAlerts[] = $user;
        $this->bindWith($user, 'getMailboxAlerts', 'addMailboxAlert');
        return $this;
    }

    public function removeMailboxAlert(User $user): self
    {
        $this->userMailboxAlerts->removeElement($user);
        return $this;
    }

    public function getMailboxAlerts(): Collection
    {
        return $this->userMailboxAlerts;
    }
}
