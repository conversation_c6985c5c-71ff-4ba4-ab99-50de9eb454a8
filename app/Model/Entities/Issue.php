<?php

namespace App\Model\Entities;

use App\Model\Repositories\Repository;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: Repository::class)]
#[ORM\HasLifecycleCallbacks]
class Issue extends AbstractEntity
{
    #[ORM\OneToOne(targetEntity: "Error", inversedBy: "issue")]
    protected ?Error $error = null;

    #[ORM\Column]
    protected int $iid;

    #[ORM\Column]
    protected string $label;


    public function setError(Error $error): self
    {
        $this->error = $error;
        $this->bindWith($error);
        return $this;
    }

    public function getError(): ?Error
    {
        return $this->error;
    }

    public function setIid(int $iid): self
    {
        $this->iid = $iid;
        return $this;
    }

    public function getIid(): int
    {
        return $this->iid;
    }

    public function setLabel(string $label): self
    {
        $this->label = $label;
        return $this;
    }

    public function getLabel(): string
    {
        return $this->label;
    }
}
