<?php

namespace App\Model\Facades;

use App\Enums\ErrorStatus;
use App\Model\Entities\App;
use App\Model\Entities\Comment;
use App\Model\Entities\Error;
use App\Model\Entities\Issue;
use App\Model\Entities\Occurrence;
use App\Model\Entities\User;
use Doctrine\ORM\QueryBuilder;

class ErrorFacade extends AbstractFacade
{
//    private function applyFilters($queryBuilder, array $where, ?string $query): void
//    {
//        foreach ($where as $field => $value) {
//            if ($field === 'slug') {
//                $queryBuilder->andWhere('a.slug = :slug')
//                    ->setParameter('slug', $value);
//            } elseif (strtoupper($value) === 'IS NULL') {
//                $queryBuilder->andWhere($queryBuilder->expr()->isNull('e.' . $field));
//            } elseif (strtoupper($value) === 'IS NOT NULL') {
//                $queryBuilder->andWhere($queryBuilder->expr()->isNotNull('e.' . $field));
//            } else {
//                $queryBuilder->andWhere(sprintf('e.%s = :%s', $field, $field))
//                    ->setParameter($field, $value);
//            }
//        }
//
//        if ($query) {
//            $queryBuilder->andWhere('(e.message LIKE :query OR e.file LIKE :query)')
//                ->setParameter('query', '%' . $query . '%');
//        }
//    }

//    public function getErrors(array $where = [], ?array $apps = null, ?array $hiddenApps = null, ?int $limit = null, ?int $offset = null, ?string $query = null): array
//    {
//        $appIds = array_map(fn($app) => $app->getId(), $apps ?? []);
//        $hiddenAppIds = array_map(fn($app) => $app->getId(), $hiddenApps ?? []);
//
//        $queryBuilder = $this->em->getRepository(Error::class)->createQueryBuilder('e')
//            ->where('e.dependsOn IS NULL')
//            ->join('e.occurrences', 'o')
//            ->join('e.app', 'a')
//            ->groupBy('e.id')
//            ->orderBy('MAX(o.date)', 'DESC')
//            ->setFirstResult($offset)
//            ->setMaxResults($limit);
//
//        if (!empty($appIds)) {
//            $queryBuilder->andWhere('e.app IN (:appIds)')
//                ->setParameter('appIds', array_diff($appIds, $hiddenAppIds));
//        }
//
//        $this->applyFilters($queryBuilder, $where, $query);
//
//        return $queryBuilder->getQuery()->getResult();
//
//    }


    public function getErrorsByUser(User|int $user, array $statuses, int $perPage, int $page): array
    {
        $queryBuilder = $this->createErrorsQueryBuilder(user: $user, statuses: $statuses);
        return $this->paginateErrors($queryBuilder, $perPage, $page);
    }

    public function getErrorsByApp(App|int $app, array $statuses, int $perPage, int $page): array
    {
        $queryBuilder = $this->createErrorsQueryBuilder(app: $app, statuses: $statuses);
        return $this->paginateErrors($queryBuilder, $perPage, $page);
    }

    public function searchErrors(string $phrase, array $statuses, int $perPage, int $page): array
    {
        $queryBuilder = $this->createErrorsQueryBuilder(statuses: $statuses, search: $phrase);
        return $this->paginateErrors($queryBuilder, $perPage, $page);
    }

    public function getDependantErrors(Error $error): array
    {
        $queryBuilder = $this->em->createQueryBuilder()
            ->select('e')
            ->from(Error::class, 'e')
            ->where('e.dependsOn = :error')
            ->setParameter('error', $error);

        return $queryBuilder->getQuery()->getResult();
    }

    public function countErrorsByUser(User|int $user, array $statuses): int
    {
        return $this->countErrors($user, statuses: $statuses);
    }

    public function countErrorsByApp(App|int $app, array $statuses): int
    {
        return $this->countErrors(app: $app, statuses: $statuses);
    }

    public function countFoundErrors(string $phrase, array $statuses): int
    {
        return $this->countErrors(statuses: $statuses, search: $phrase);
    }

    private function countErrors(User|int|null $user = null, App|int|null $app = null, array $statuses = [], ?string $search = null): int
    {
        $queryBuilder = $this->createErrorsQueryBuilder(user: $user, app: $app, statuses: $statuses, search: $search);
        $queryBuilder->addSelect('COUNT(e.id) AS count');
        return (int) $queryBuilder->getQuery()->getResult()[0]['count'];
    }

    public function getErrorOccurrences(App|int $app, int $perPage, int $page): array
    {
        $qb = $this->em->createQueryBuilder()
            ->select('o')
            ->from(Occurrence::class, 'o')
            ->join('o.error', 'e')
            ->where('e.app = :app')
            ->setParameter('app', $app)
            ->orderBy('o.date', 'DESC');

        return $this->paginate($qb, $perPage, $page);
    }

    private function createErrorsQueryBuilder(User|int|null $user = null, App|int|null $app = null, array $statuses = [], ?string $search = null): QueryBuilder
    {
        if ($user !== null && $app !== null) {
            throw new \InvalidArgumentException(__METHOD__ . '(): Only one of parameters $user or $app can be set.');
        }
        $qb = $this->em->createQueryBuilder()
            ->select('e AS errors, (SELECT MAX(o.date) FROM App\Model\Entities\Occurrence o WHERE o.error = e) AS date')
            ->from(Error::class, 'e')
            ->orderBy('date', 'DESC');
        if (!$search) {
            $qb->where('e.dependsOn IS NULL');
        }
        if ($app) {
            $qb->andWhere('e.app = :app')
                ->setParameter('app', $app);
        } elseif ($user) {
            $qb->andWhere('e.app NOT IN (SELECT a.id FROM App\Model\Entities\App a JOIN a.userHiddenApps u WHERE u.id = :user)')
                ->setParameter('user', $user);

        }
        if ($statuses) {
            $this->filterByStatuses($qb, $statuses);
        }
        if ($search) {
            $qb->andWhere('(e.message LIKE :phrase OR e.file LIKE :phrase OR e.filename LIKE :phrase OR e.exception LIKE :phrase)')
                ->setParameter('phrase', '%' . $search . '%');
        }

        return $qb;
    }

    protected function paginateErrors(QueryBuilder $query, int $perPage, int $page): array
    {
        return parent::paginate($query, $perPage, $page, modifier: fn ($row) => $row['errors']);
    }

    /**
     * @param QueryBuilder $qb
     * @param ErrorStatus[] $statuses
     * @return void
     */
    private function filterByStatuses(QueryBuilder $qb, array $statuses = []): void
    {
        if ($statuses === []) {
            return;
        }

        $cases = ErrorStatus::cases();
        sort($cases);
        sort($statuses);

        if ($cases === $statuses) {
            return;
        }

        $where = [];
        foreach ($statuses as $status) {
            if ($status === ErrorStatus::OPEN) {
                $where[] = 'e.resolved = false AND e.user IS NULL';
            } elseif ($status === ErrorStatus::ASSIGNED) {
                $where[] = 'e.resolved = false AND e.user IS NOT NULL';
            } elseif ($status === ErrorStatus::RESOLVED) {
                $where[] = 'e.resolved = true';
            }
        }

        if ($imploded = implode(') OR (', $where)) {
            $qb->andWhere('(' . $imploded . ')');
        }
    }

//    public function getErrorsByAppSlug(string $slug, ?int $limit = null, ?int $offset = null): array
//    {
//        $queryBuilder = $this->em->createQueryBuilder();
//        $queryBuilder->select('e')
//            ->from('App\Model\Entities\Error', 'e')
//            ->join('e.app', 'a')
//            ->where('a.slug = :slug')
//            ->setParameter('slug', $slug)
//            ->setFirstResult($offset)
//            ->setMaxResults($limit);
//
//        return $queryBuilder->getQuery()->getResult();
//    }

    public function countErrorsByApps(array $apps): array
    {
        $dql = 'SELECT a.id, COUNT(e.id) AS total, SUM(CASE WHEN e.resolved = 0 THEN 1 ELSE 0 END) AS unresolved
                FROM App\Model\Entities\App a
                LEFT JOIN a.errors e
                WHERE a.id IN (:appIds) AND e.dependsOn IS NULL
                GROUP BY a.id';

        $query = $this->em->createQuery($dql);
        $query->setParameter('appIds', array_map(fn ($app) => $app->getId(), $apps));
        $result = $query->getResult();

        $counted = [];
        foreach ($result as $row) {
            $counted[$row['id']] = ['total' => (int)$row['total'], 'unresolved' => (int)$row['unresolved']];
        }

        return $counted;
    }

    public function getErrorById(int $id): ?Error
    {
        return $this->em->getRepository(Error::class)->find($id);
    }

    public function saveError(Error $error): Error
    {
        $this->save($error);
        return $error;
    }

    public function mergeErrors(Error $error1, Error $error2): Error
    {
        if ($error1->getChecksum() !== $error2->getChecksum()) {
            throw new \InvalidArgumentException(__METHOD__ . '() merged error must have the same checksums, ' . $error1->getChecksum() . ' and ' . $error2->getChecksum() . ' not equal.');
        }

        foreach ($error2->getOccurrences() as $occurrence) {
            $newOccurrence = new Occurrence();
            $newOccurrence->setDate($occurrence->getDate());
            $error1->addOccurrence($newOccurrence);
        }

        return $error1;
    }

//    public function getErrorOccurrences(int $id): array
//    {
//        return $this->em->getRepository(Occurrence::class)->findBy(['error' => $id], ['date' => 'DESC']);
//    }

    public function saveIssue(Issue $issue): Issue
    {
        $this->save($issue);
        return $issue;
    }

    public function saveComment(Comment $comment): Comment
    {
        $this->save($comment);
        return $comment;
    }
}
