<?php

namespace App\Model\Facades;

use App\Model\Entities\App;
use Doctrine\Common\Collections\ArrayCollection;
use Illuminate\Pagination\Paginator;
use Illuminate\Support\Facades\Auth;

class AppFacade extends AbstractFacade
{
    /**
     * Get apps with optional sorting and pagination
     */
    public function getApps(?string $sort = null, int $perPage = 8, int $page = 1): array
    {
        $qb = $this->em->createQueryBuilder()
            ->select('a')
            ->from(App::class, 'a');

        // Apply sorting if provided
        if ($sort) {
            switch ($sort) {
                case 'name':
                    $qb->orderBy('a.title', 'ASC');
                    break;
                case 'appId':
                    $qb->orderBy('a.id', 'ASC');
                    break;
                case 'gitlabId':
                    $qb->orderBy('a.gitlabId', 'ASC');
                    break;
                default:
                    $qb->orderBy('a.title', 'ASC');
                    break;
            }
        } else {
            // Default sorting
            $qb->orderBy('a.title', 'ASC');
        }

        // Return paginated results
        return $this->paginate($qb, $perPage, $page);
    }

    public function getAppById(int $id): ?App
    {
        return $this->em->getRepository(App::class)->find($id);
    }

    public function getAppBySlug(string $slug): ?App
    {
        return $this->em->getRepository(App::class)->findOneBy(['slug' => $slug]);
    }

    public function getAppByGitlabId(int $gitlabId): ?App
    {
        return $this->em->getRepository(App::class)->findOneBy(['gitlabId' => $gitlabId]);
    }

    public function getAppByOAuth(string $clientId): ?App
    {
        return $this->em->getRepository(App::class)->findOneBy(['oauthClientId' => $clientId]);
    }

    public function updateAppVisibility(bool $visibility, int $appId): void
    {
        $app = $this->getAppById($appId);
        abort_if(!$app, 404);
        $user = Auth::user()->getIdentity();

        !$visibility ? $user->addUserHiddenApp($app) : $user->removeUserHiddenApp($app);
        $this->save($user);
    }

    public function updateAppMailbox(bool $mailbox, int $appId): void
    {
        $app = $this->getAppById($appId);
        abort_if(!$app, 404);
        $user = Auth::user()->getIdentity();

        $mailbox ? $user->addMailboxAlert($app) : $user->removeMailboxAlert($app);
        $this->save($user);
    }

    public function getHiddenAppsByUser(): array
    {
        $user = Auth::user()->getIdentity();
        return $user->getUserHiddenApps()->toArray();
    }

    public function saveApp(App $app): App
    {
        $this->save($app);
        return $app;
    }

    /**
     * Count errors by apps
     */
    public function countErrorsByApps(array $apps): array
    {
        $result = [];
        foreach ($apps as $app) {
            $result[$app->getId()] = [
                'unresolved' => $this->countErrorsByApp($app, [ErrorStatus::OPEN, ErrorStatus::ASSIGNED]),
                'total' => $this->countErrorsByApp($app, [])
            ];
        }
        return $result;
    }
}
