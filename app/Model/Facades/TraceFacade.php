<?php

namespace App\Model\Facades;

use App\Model\Entities\Trace;

class TraceFacade extends AbstractFacade
{
    public function getTraces(?int $errorId = null): array
    {
        $where = [];
        if ($errorId) {
            $where['error'] = $errorId;
        }
        return $this->em->getRepository(Trace::class)->findBy($where);
    }

    public function getTraceById(int $id): ?Trace
    {
        return $this->em->getRepository(Trace::class)->find($id);
    }

    public function getTraceByFilename(string $filename): ?Trace
    {
        return $this->em->getRepository(Trace::class)->findOneBy(['name' => $filename]);
    }

    public function saveTrace(Trace $trace): Trace
    {
        $this->save($trace);
        return $trace;
    }
}
