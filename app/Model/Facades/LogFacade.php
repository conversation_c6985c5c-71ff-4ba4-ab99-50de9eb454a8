<?php

namespace App\Model\Facades;

use App\Model\Entities\Log;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\OptimisticLockException;

class LogFacade extends AbstractFacade
{
    public function getLogs(): array
    {
        return $this->em->getRepository(Log::class)->findBy(['parsed' => false]);
    }

    /**
     * Get unparsed log IDs only for memory efficient processing
     */
    public function getUnparsedLogIds(int $limit = 1, int $offset = 0): array
    {
        $qb = $this->em->createQueryBuilder()
            ->select('l.id')
            ->from(Log::class, 'l')
            ->where('l.parsed = :parsed')
            ->setParameter('parsed', false)
            ->orderBy('l.id', 'ASC')
            ->setMaxResults($limit)
            ->setFirstResult($offset);

        return array_column($qb->getQuery()->getScalarResult(), 'id');
    }

    /**
     * Count total unparsed logs
     */
    public function countUnparsedLogs(): int
    {
        return $this->em->getRepository(Log::class)->count(['parsed' => false]);
    }

    public function getLogById(int $id): ?Log
    {
        return $this->em->getRepository(Log::class)->find($id);
    }

    public function saveLog(Log $log): Log
    {
        $this->save($log);
        return $log;
    }

    public function deleteParsedLogs(): self
    {
        $logs = $this->em->getRepository(Log::class)->findBy(['parsed' => 1]);
        foreach ($logs as $log) {
            $this->em->remove($log);
            $this->em->flush();
        }
        return $this;
    }
}
