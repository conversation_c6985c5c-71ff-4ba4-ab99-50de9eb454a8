<?php

namespace App\Model\Facades;

use App\Model\Entities\Log;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\OptimisticLockException;

class LogFacade extends AbstractFacade
{
    public function getLogs(): array
    {
        return $this->em->getRepository(Log::class)->findBy(['parsed' => false]);
    }

    public function getLogById(int $id): ?Log
    {
        return $this->em->getRepository(Log::class)->find($id);
    }

    public function saveLog(Log $log): Log
    {
        $this->save($log);
        return $log;
    }

    public function deleteParsedLogs(): self
    {
        $logs = $this->em->getRepository(Log::class)->findBy(['parsed' => 1]);
        foreach ($logs as $log) {
            $this->em->remove($log);
            $this->em->flush();
        }
        return $this;
    }
}
