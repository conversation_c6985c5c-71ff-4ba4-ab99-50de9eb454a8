<?php

namespace App\Model\Facades;

use App\Model\Entities\Log;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\OptimisticLockException;

class LogFacade extends AbstractFacade
{
    public function getLogs(): array
    {
        return $this->em->getRepository(Log::class)->findBy(['parsed' => false]);
    }

    /**
     * Get unparsed logs in batches to prevent memory overflow
     * Uses query builder for better memory management
     */
    public function getLogsBatch(int $limit = 100, int $offset = 0): array
    {
        $qb = $this->em->createQueryBuilder()
            ->select('l')
            ->from(Log::class, 'l')
            ->where('l.parsed = :parsed')
            ->setParameter('parsed', false)
            ->orderBy('l.id', 'ASC')
            ->setMaxResults($limit)
            ->setFirstResult($offset);

        $query = $qb->getQuery();

        // Use HYDRATE_ARRAY to reduce memory usage
        return $query->getResult();
    }

    /**
     * Get unparsed log IDs only for even more memory efficient processing
     */
    public function getUnparsedLogIds(int $limit = 100, int $offset = 0): array
    {
        $qb = $this->em->createQueryBuilder()
            ->select('l.id')
            ->from(Log::class, 'l')
            ->where('l.parsed = :parsed')
            ->setParameter('parsed', false)
            ->orderBy('l.id', 'ASC')
            ->setMaxResults($limit)
            ->setFirstResult($offset);

        return array_column($qb->getQuery()->getScalarResult(), 'id');
    }

    /**
     * Get log data as array to avoid entity hydration and BLOB loading
     */
    public function getLogDataById(int $id): ?array
    {
        $connection = $this->em->getConnection();
        $sql = 'SELECT id, filename, logger, logger_version, app_id, parsed FROM log WHERE id = ? AND parsed = 0';
        $stmt = $connection->prepare($sql);
        $stmt->bindValue(1, $id);
        $result = $stmt->executeQuery()->fetchAssociative();

        return $result ?: null;
    }

    /**
     * Get log content separately to minimize memory usage
     */
    public function getLogContent(int $id): ?string
    {
        $connection = $this->em->getConnection();
        $sql = 'SELECT content FROM log WHERE id = ?';
        $stmt = $connection->prepare($sql);
        $stmt->bindValue(1, $id);
        $result = $stmt->executeQuery()->fetchOne();

        return $result ?: null;
    }

    /**
     * Get complete log data including content as array
     */
    public function getLogDataWithContent(int $id): ?array
    {
        $connection = $this->em->getConnection();
        $sql = 'SELECT id, filename, content, logger, logger_version, app_id, parsed FROM log WHERE id = ? AND parsed = 0';
        $stmt = $connection->prepare($sql);
        $stmt->bindValue(1, $id);
        $result = $stmt->executeQuery()->fetchAssociative();

        return $result ?: null;
    }

    /**
     * Count total unparsed logs
     */
    public function countUnparsedLogs(): int
    {
        return $this->em->getRepository(Log::class)->count(['parsed' => false]);
    }

    public function getLogById(int $id): ?Log
    {
        return $this->em->getRepository(Log::class)->find($id);
    }

    public function saveLog(Log $log): Log
    {
        $this->save($log);
        return $log;
    }

    public function deleteParsedLogs(): self
    {
        $logs = $this->em->getRepository(Log::class)->findBy(['parsed' => 1]);
        foreach ($logs as $log) {
            $this->em->remove($log);
            $this->em->flush();
        }
        return $this;
    }
}
