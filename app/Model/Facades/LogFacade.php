<?php

namespace App\Model\Facades;

use App\Model\Entities\Log;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\OptimisticLockException;

class LogFacade extends AbstractFacade
{
    public function getLogs(): array
    {
        return $this->em->getRepository(Log::class)->findBy(['parsed' => false]);
    }

    /**
     * Get unparsed logs in batches to prevent memory overflow
     * Uses query builder for better memory management
     */
    public function getLogsBatch(int $limit = 100, int $offset = 0): array
    {
        $qb = $this->em->createQueryBuilder()
            ->select('l')
            ->from(Log::class, 'l')
            ->where('l.parsed = :parsed')
            ->setParameter('parsed', false)
            ->orderBy('l.id', 'ASC')
            ->setMaxResults($limit)
            ->setFirstResult($offset);

        $query = $qb->getQuery();

        // Use HYDRATE_ARRAY to reduce memory usage
        return $query->getResult();
    }

    /**
     * Get unparsed log IDs only for even more memory efficient processing
     */
    public function getUnparsedLogIds(int $limit = 100, int $offset = 0): array
    {
        $qb = $this->em->createQueryBuilder()
            ->select('l.id')
            ->from(Log::class, 'l')
            ->where('l.parsed = :parsed')
            ->setParameter('parsed', false)
            ->orderBy('l.id', 'ASC')
            ->setMaxResults($limit)
            ->setFirstResult($offset);

        return array_column($qb->getQuery()->getScalarResult(), 'id');
    }

    /**
     * Get a single log by ID with minimal memory footprint
     */
    public function getLogByIdMinimal(int $id): ?Log
    {
        $qb = $this->em->createQueryBuilder()
            ->select('l')
            ->from(Log::class, 'l')
            ->where('l.id = :id')
            ->setParameter('id', $id)
            ->setMaxResults(1);

        $result = $qb->getQuery()->getOneOrNullResult();
        return $result;
    }

    /**
     * Count total unparsed logs
     */
    public function countUnparsedLogs(): int
    {
        return $this->em->getRepository(Log::class)->count(['parsed' => false]);
    }

    public function getLogById(int $id): ?Log
    {
        return $this->em->getRepository(Log::class)->find($id);
    }

    public function saveLog(Log $log): Log
    {
        $this->save($log);
        return $log;
    }

    public function deleteParsedLogs(): self
    {
        $logs = $this->em->getRepository(Log::class)->findBy(['parsed' => 1]);
        foreach ($logs as $log) {
            $this->em->remove($log);
            $this->em->flush();
        }
        return $this;
    }
}
