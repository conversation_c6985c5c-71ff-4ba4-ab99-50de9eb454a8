<?php

namespace App\Model\Helpers;

use App\Model\Entities\AbstractEntity;
use Doctrine\ORM\Mapping as ORM;
use ReflectionClass;
use ReflectionProperty;

class EntityToArray
{
    public const string COLUMN = 'col';
    public const string ONE = 'one';
    public const string MANY = 'many';

    public function __construct(
        private readonly AbstractEntity $entity
    ) {
    }

    public function toArray(): array
    {
        $class = new ReflectionClass($this->entity);

        foreach ($class->getProperties() as $property) {
            $columnType = $this->getColumnType($property);

            if (!$columnType) {
                continue;
            }

            $value = $this->getValue($property, $columnType);
            $result[$property->getName()] = $value;
        }

        return $result ?? [];
    }

    public function getColumnType(ReflectionProperty $property): ?string
    {
        $attributes = $property->getAttributes();

        foreach ($attributes as $attribute) {
            if ($attribute->getName() === ORM\Column::class) {
                return self::COLUMN;
            } elseif ($attribute->getName() === ORM\OneToOne::class || $attribute->getName() === ORM\ManyToOne::class) {
                return self::ONE;
            } elseif ($attribute->getName() === ORM\OneToMany::class || $attribute->getName() === ORM\ManyToMany::class) {
                return self::MANY;
            }
        }

        return null;
    }

    public function getValue(ReflectionProperty $property, string $columnType): mixed
    {
        $getter = 'get' . ucfirst($property->getName());
        $is = 'is' . ucfirst($property->getName());
        if (method_exists($this->entity, $getter)) {
            $value = $this->entity->$getter();
        } elseif (method_exists($this->entity, $is)) {
            $value = $this->entity->$is();
        } else {
            $value = $property->getValue($this->entity);
        }

        if ($columnType === self::COLUMN) {
            // DateTime
            if ($value instanceof \DateTimeInterface) {
                $value = (array) $value;
            }

            // TODO Enums
        } elseif ($columnType === self::ONE) {
            $value = $value?->getId();
        } elseif ($columnType === self::MANY) {
            foreach ($value as $entity) {
                $joinValue[] = $entity->getId();
            }
            $value = $joinValue ?? [];
        }
        return $value;
    }
}
