<?php

namespace App\Model\Helpers;

use DateTime;
use DateTimeImmutable;
use DateTimeInterface;

class Helpers
{
    /**
     * Predela pole/collection tak, ze pouzije ID jako indexy
     * @param iterable $entities
     * @return array
     */
    public static function indexize(iterable $entities): array
    {
        $result = [];
        foreach ($entities as $entity) {
            $result[$entity->getId()] = $entity;
        }
        return $result;
    }

    /**
     * Vytvori z pole/collection entit jednoduche pole [column => column] pro pouziti napr. v selectboxu
     * @param iterable $entities array nebo Collection
     * @param string $columnAsValue
     * @param string $columnAsKey
     * @return array
     */
    public static function pairs(iterable $entities, string $columnAsValue, string $columnAsKey = 'id'): array
    {
        $result = [];
        $colGetter = 'get'.ucfirst($columnAsValue);
        $keyGetter = 'get'.ucfirst($columnAsKey);
        foreach ($entities as $entity) {
            $result[$entity->$keyGetter()] = $entity->$colGetter();
        }
        return $result;
    }

    /**
     * Prevede DateTime, string i timestamp na DateTimeImmutable
     * @param DateTimeInterface | string | int $date
     * @return DateTimeImmutable
     */
    public static function dateTimeImmutable(DateTimeInterface | string | int $date): DateTimeImmutable
    {
        if ($date instanceof DateTimeImmutable) {
            return $date;
        } elseif ($date instanceof DateTime) {
            return DateTimeImmutable::createFromMutable($date);
        } elseif (is_int($date)) {
            $dti = DateTimeImmutable::createFromFormat('U', (string) $date);
            if (!$dti) {
                throw new \InvalidArgumentException(__METHOD__ . '() invalid integer argument');
            }
            return $dti;
        } else {
            return new DateTimeImmutable($date);
        }
    }
}
