<?php

namespace App\Model\Repositories;

use App\Model\Entities\IEntity;
use App\Model\Entities\Traits\TDeletable;
use App\Model\Entities\Traits\TOrder;
use Doctrine\ORM\EntityRepository;

/**
 * Entita musi mit v atributu Entity repositoryClass: Repository::class
 */
class Repository extends EntityRepository
{
    public function findAll(bool $includeDeleted = false): array
    {
        if ($this->isEntityDeletable() && !$includeDeleted) {
            $criteria['deleted'] = false;
        } else {
            $criteria = [];
        }
        if ($this->isEntityOrderable()) {
            $orderBy = ['order' => 'asc'];
        } else {
            $orderBy = [];
        }
        return parent::findBy($criteria, $orderBy);
    }

    public function findBy(array $criteria, ?array $orderBy = null, $limit = null, $offset = null, bool $includeDeleted = false): array
    {
        if ($this->isEntityDeletable() && !$includeDeleted) {
            $criteria['deleted'] = false;
        }
        if ($this->isEntityOrderable() && ($orderBy === null || $orderBy === [])) {
            $orderBy = ['order' => 'asc'];
        }
        return parent::findBy($criteria, $orderBy, $limit, $offset);
    }

    public function findOneBy(array $criteria, ?array $orderBy = null, bool $includeDeleted = false): ?IEntity
    {
        if ($this->isEntityOrderable() && ($orderBy === null || $orderBy === [])) {
            $orderBy = ['order' => 'asc'];
        }

        if ($this->isEntityDeletable() && !$includeDeleted) {
            $criteria['deleted'] = false;
        }
        return parent::findOneBy($criteria, $orderBy);
    }

    private function isEntityDeletable(): bool
    {
        return in_array(TDeletable::class, class_uses_recursive(parent::getClassName()) ?: []);
    }

    private function isEntityOrderable(): bool
    {
        return in_array(TOrder::class, class_uses_recursive(parent::getClassName()) ?: []);
    }

    /**
     * Najde dle ID, nebo vytvori novou instanci s pouzitim $values v konstruktoru.
     * Pokud je zadano neexistjici ID, vrati null
     * @param ?int $id
     * @param array $values<string, mixed>
     * @return ?IEntity
     */
    public function findOrNew(?int $id, array $values = []): ?IEntity
    {
        if ($id === null || $id === 0) {
            $entityClass = parent::getClassName();
            $entity = $entityClass::create($values);
        } else {
            $entity = parent::find($id);
        }
        return $entity;
    }

    /**
     * Najde prvni zaznam dle $attributes, nebo vytvori novou instanci s pouzitim $attributes + $values v konstruktoru.
     * @param array $attributes<string, mixed>
     * @param array $values<string, mixed>
     * @return IEntity
     */
    public function firstOrNew(array $attributes = [], array $values = []): IEntity
    {
        $entity = parent::findOneBy($attributes);
        if (!$entity) {
            $args = array_merge($attributes, $values);
            $entityClass = parent::getClassName();
            $entity = $entityClass::create($args);
        }
        return $entity;
    }

    /**
     * Viz self::firstOrNew(). Navic persistuje novou instanci.
     * @param array $attributes<string, mixed>
     * @param array $values<string, mixed>
     * @return IEntity
     */
    public function firstOrPersist(array $attributes = [], array $values = []): IEntity
    {
        $entity = $this->firstOrNew($attributes, $values);
        if ($entity->getId() === null) {
            $this->getEntityManager()->persist($entity);
        }
        return $entity;
    }

    /**
     * Viz self::firstOrPersist(). Navic zedituje existujici zaznam.
     * @param array $attributes<string, mixed>
     * @param array $values<string, mixed>
     * @return IEntity
     */
    public function updateOrPersist(array $attributes = [], array $values = []): IEntity
    {
        $entity = $this->firstOrNew($attributes, $values);
        if ($entity->getId() !== null) {
            $entity->fill($values);
        } else {
            $this->getEntityManager()->persist($entity);
        }
        return $entity;
    }
}
