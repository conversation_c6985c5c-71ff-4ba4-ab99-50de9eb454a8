<?php

declare(strict_types=1);

namespace App\Model\Migrations;

use Doctrine\DBAL\Schema\Schema;
use DekApps\Dektrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250526143555 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Pridan sloupec oauth_client_id do tabulky app';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE app ADD oauth_client_id VARCHAR(255) DEFAULT NULL AFTER ssh_algo, CHANGE ssh ssh LONGTEXT DEFAULT NULL, <PERSON>AN<PERSON> ssh_algo ssh_algo INT DEFAULT NULL');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE app DROP oauth_client_id, CHANGE ssh ssh LONGTEXT NOT NULL, CHANGE ssh_algo ssh_algo INT NOT NULL');
    }
}
