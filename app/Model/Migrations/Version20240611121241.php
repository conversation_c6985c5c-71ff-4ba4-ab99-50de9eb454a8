<?php

declare(strict_types=1);

namespace App\Model\Migrations;

use Doctrine\DBAL\Schema\Schema;
use DekApps\Dektrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240611121241 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Novy sloupec closedAt pro error';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE error ADD closed_at DATETIME DEFAULT NULL');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE error DROP closed_at');
    }
}
