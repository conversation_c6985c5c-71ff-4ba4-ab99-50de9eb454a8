<?php

declare(strict_types=1);

namespace App\Model\Migrations;

use Doctrine\DBAL\Schema\Schema;
use DekApps\Dektrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240607133350 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'error.url VARCHAR 255 -> 2048';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE error CHANGE url url VARCHAR(2048) NOT NULL');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE error CHANGE url url VARCHAR(255) NOT NULL');
    }
}
