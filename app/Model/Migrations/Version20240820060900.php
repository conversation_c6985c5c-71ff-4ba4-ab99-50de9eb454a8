<?php

declare(strict_types=1);

namespace App\Model\Migrations;

use Doctrine\DBAL\Schema\Schema;
use DekA<PERSON>\Dektrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240820060900 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Upravy databaze pro mailbox a oprava bugu';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE TABLE user_hidden_apps (user_id INT NOT NULL, app_id INT NOT NULL, INDEX IDX_303CAA63A76ED395 (user_id), INDEX IDX_303CAA637987212D (app_id), PRIMARY KEY(user_id, app_id)) DEFAULT CHARACTER SET utf8mb4');
        $this->addSql('CREATE TABLE user_mailbox_alerts (user_id INT NOT NULL, app_id INT NOT NULL, INDEX IDX_711E0DAA76ED395 (user_id), INDEX IDX_711E0DA7987212D (app_id), PRIMARY KEY(user_id, app_id)) DEFAULT CHARACTER SET utf8mb4');
        $this->addSql('ALTER TABLE user_hidden_apps ADD CONSTRAINT FK_303CAA63A76ED395 FOREIGN KEY (user_id) REFERENCES user (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE user_hidden_apps ADD CONSTRAINT FK_303CAA637987212D FOREIGN KEY (app_id) REFERENCES app (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE user_mailbox_alerts ADD CONSTRAINT FK_711E0DAA76ED395 FOREIGN KEY (user_id) REFERENCES user (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE user_mailbox_alerts ADD CONSTRAINT FK_711E0DA7987212D FOREIGN KEY (app_id) REFERENCES app (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE user_app DROP FOREIGN KEY FK_227811447987212D');
        $this->addSql('ALTER TABLE user_app DROP FOREIGN KEY FK_22781144A76ED395');
        $this->addSql('DROP TABLE user_app');
        $this->addSql('ALTER TABLE activity CHANGE detail detail JSON DEFAULT NULL');
        $this->addSql('ALTER TABLE app CHANGE domain domain VARCHAR(255) DEFAULT NULL');
        $this->addSql('CREATE UNIQUE INDEX gitlab_id ON app (gitlab_id)');
        $this->addSql('ALTER TABLE trace CHANGE stack stack JSON DEFAULT NULL');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE TABLE user_app (user_id INT NOT NULL, app_id INT NOT NULL, INDEX IDX_22781144A76ED395 (user_id), INDEX IDX_227811447987212D (app_id), PRIMARY KEY(user_id, app_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_general_ci` ENGINE = InnoDB COMMENT = \'\' ');
        $this->addSql('ALTER TABLE user_app ADD CONSTRAINT FK_227811447987212D FOREIGN KEY (app_id) REFERENCES app (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE user_app ADD CONSTRAINT FK_22781144A76ED395 FOREIGN KEY (user_id) REFERENCES user (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE user_hidden_apps DROP FOREIGN KEY FK_303CAA63A76ED395');
        $this->addSql('ALTER TABLE user_hidden_apps DROP FOREIGN KEY FK_303CAA637987212D');
        $this->addSql('ALTER TABLE user_mailbox_alerts DROP FOREIGN KEY FK_711E0DAA76ED395');
        $this->addSql('ALTER TABLE user_mailbox_alerts DROP FOREIGN KEY FK_711E0DA7987212D');
        $this->addSql('DROP TABLE user_hidden_apps');
        $this->addSql('DROP TABLE user_mailbox_alerts');
        $this->addSql('ALTER TABLE activity CHANGE detail detail LONGTEXT DEFAULT NULL COLLATE `utf8mb4_bin`');
        $this->addSql('DROP INDEX gitlab_id ON app');
        $this->addSql('ALTER TABLE app CHANGE domain domain VARCHAR(255) NOT NULL');
        $this->addSql('ALTER TABLE trace CHANGE stack stack LONGTEXT DEFAULT NULL COLLATE `utf8mb4_bin`');
    }
}
