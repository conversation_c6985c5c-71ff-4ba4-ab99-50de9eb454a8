<?php

declare(strict_types=1);

namespace App\Model\Migrations;

use Doctrine\DBAL\Schema\Schema;
use DekApps\Dektrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250130140841 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Uložení filtru aplikace pro každého uživatele';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE user ADD app_filter VARCHAR(255) DEFAULT NULL');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE user DROP app_filter');
    }
}
