<?php

namespace App\Listeners;

use App\Events\ErrorCreatedEvent;
use App\Mail\NewErrorMail;
use App\Model\Facades\ErrorFacade;
use App\Model\Facades\UserFacade;
use Illuminate\Support\Facades\Mail;

readonly class NewErrorListener
{
    /**
     * Create the event listener.
     */
    public function __construct(
        private UserFacade $userFacade,
        private ErrorFacade $errorFacade,
    )
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(ErrorCreatedEvent $event): void
    {
        $id = $event->id;
        $error = $this->errorFacade->getErrorById($id);
        $app = $error->getApp();
        $mailboxUsers = $app->getMailboxAlerts()->toArray();

        foreach ($mailboxUsers as $mailboxUser) {
            if ($error->getUser() !== null) {
                continue;
            }
            $user = $this->userFacade->getUserById($mailboxUser->getId());
            Mail::to($user->getEmail())->send(new NewErrorMail($error));
        }
    }
}
