<?php

namespace App\Listeners;

use App\Events\ActivityEvent;
use App\Model\Facades\ActivityFacade;

readonly class ActivityListener
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct(
        private ActivityFacade $activityFacade
    ) {
    }

    public function handle(ActivityEvent $event): void
    {
        $activity = $event->activity;
        $this->activityFacade->saveActivity($activity);
    }
}
