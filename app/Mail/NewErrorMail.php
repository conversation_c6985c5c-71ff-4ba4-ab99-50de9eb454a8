<?php

namespace App\Mail;

use App\Model\Entities\Error;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class NewErrorMail extends Mailable
{
    use Queueable, SerializesModels;

    private Error $error;

    /**
     * Create a new message instance.
     */
    public function __construct(Error $error)
    {
        $this->error = $error;
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            subject: 'Errorlog: Nová chyba v aplikaci ' . $this->error->getApp()->getTitle(),
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            view: 'emails.new-error',
            with: ['newError' => $this->error],
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}
