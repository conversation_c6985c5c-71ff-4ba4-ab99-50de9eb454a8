{var $dateCounter = 0}
{var $gTransform = 18}
{var $rectTransform = 0}

<svg width="1250" height="180" class="uk-margin-small-bottom">
    {for $i=1; $i <= ceil(count($dates) / 7); $i++}
        <g transform="translate({$gTransform}, 18)">
            {$rectTransform = 0}
            {for $j=1; $j <= 7; $j++}
                {if (!isset($dates[$dateCounter]))}
                    <rect x="30" y="{$rectTransform}" width="15" height="15" class="occurrence-none"></rect>
                {else}
                    <rect x="30"
                          y="{$rectTransform}"
                          width="19"
                          height="19"
                          uk-tooltip="{$dates[$dateCounter]} {if $occsNumber[$dateCounter] != null}<br /> {$occsNumber[$dateCounter]} {if $occsNumber[$dateCounter] === 1}výskyt{elseif $occsNumber[$dateCounter] < 5}výskyty{else}výskytů{/if}{/if}"
                          class="{$fill_color[$dateCounter]}"
                            {if $occsNumber[$dateCounter] != null}
                        onclick="window.location.href= '{route('apps.error-log', $error->getApp()->getSlug())|noescape}?error={$error->getId()}&date={$dates[$dateCounter]}'"
                        style="cursor: pointer"
                            {/if}>
                    </rect>
                {/if}
                {$rectTransform += 21}
                {$dateCounter++}
            {/for}
        </g>
        {$gTransform += 21}
    {/for}
    <g>
        <text class="occurrence-text" text-anchor="start" x="0" y="32">Pondělí</text>
        <text class="occurrence-text" text-anchor="start" x="0" y="74">Středa</text>
        <text class="occurrence-text" text-anchor="start" x="0" y="116">Pátek</text>
        <text class="occurrence-text" text-anchor="start" x="0" y="158">Neděle</text>
    </g>
</svg>

<div class="uk-flex">
    <svg width="941" height="25">
        <g>
            <rect x="0" y="10" width="15" height="15"  class="occurrence-default" uk-tooltip="Žádné výskyty"></rect>
            <rect x="17" y="10" width="15" height="15" class="occurrence-low" uk-tooltip="1 - 2 výskyty"></rect>
            <rect x="34" y="10" width="15" height="15" class="occurrence-medium" uk-tooltip="3 - 5 výskytů"></rect>
            <rect x="51" y="10" width="15" height="15" class="occurrence-high" uk-tooltip="6 - 10 výskytů"></rect>
            <rect x="68" y="10" width="15" height="15" class="occurrence-extreme" uk-tooltip="Více než 10 výskytů"></rect>
        </g>
    </svg>
</div>
<hr>
