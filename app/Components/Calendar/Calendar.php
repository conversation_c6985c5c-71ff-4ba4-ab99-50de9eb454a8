<?php

namespace App\Components\Calendar;

use App\Components\Component;
use App\Model\Entities\Error;

class Calendar extends Component
{
    private ?iterable $occurrences = null;
    private ?Error $error = null;

    public function init(iterable $occurrences, Error $error): void
    {
        $this->occurrences = $occurrences;
        $this->error = $error;
    }

    public function render(): string
    {
        $occDates = $arrDays = $fillColor = $occsNumber = [];

        foreach ($this->occurrences as $occurrence) {
            $occDates[] = $occurrence->getDate()->format('D d.n.Y');
        }
        $sorted = array_count_values($occDates);

        $mondayYearBefore = strtotime(378 + (int)date('N').' days ago today + 1 day');
        $today = strtotime('today');
        $daysDifference = ($today - $mondayYearBefore) / 86400;

        for ($i = 0; $i <= $daysDifference; $i++) {
            $arrDays[] = date('D d.n.Y', $mondayYearBefore + ($i * 86400));
            if (in_array($arrDays[$i], array_keys($sorted))) {
                $current = $sorted[$arrDays[$i]];
                if ($current > 10) {
                    $fillColor[] = "occurrence-extreme";
                } elseif ($current > 5) {
                    $fillColor[] = "occurrence-high";
                } elseif ($current > 2) {
                    $fillColor[] = 'occurrence-medium';
                } else {
                    $fillColor[] = 'occurrence-low';
                }

                $occsNumber[] = $sorted[$arrDays[$i]];
            } else {
                $fillColor[] = 'occurrence-default';
                $occsNumber[] = "";
            }
        }

        return $this->latte->renderToString(
            __DIR__ . '/calendar.latte',
            [
            'dates' => $arrDays,
            'fill_color' => $fillColor,
            'occsNumber' => $occsNumber,
            'error' => $this->error,
            //'anchor' => $this->error->getOccurrences()->first()->getAnchor(),
            ]
        );
    }
}
