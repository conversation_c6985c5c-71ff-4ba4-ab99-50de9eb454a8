<?php

namespace App\Exceptions;

use Illuminate\Foundation\Exceptions\Handler as ExceptionHandler;
use Illuminate\Support\Str;
use Throwable;

class Handler extends ExceptionHandler
{
    /**
     * A list of exception types with their corresponding custom log levels.
     *
     * @var array<class-string<\Throwable>, \Psr\Log\LogLevel::*>
     */
    protected $levels = [
        //
    ];

    /**
     * A list of the exception types that are not reported.
     *
     * @var array<int, class-string<\Throwable>>
     */
    protected $dontReport = [
        Throwable::class  // Vypne logování všech exception laravelem
    ];

    /**
     * A list of the inputs that are never flashed to the session on validation exceptions.
     *
     * @var array<int, string>
     */
    protected $dontFlash = [
        'current_password',
        'password',
        'password_confirmation',
    ];

    /**
     * Register the exception handling callbacks for the application.
     *
     * @return void
     */
    public function register()
    {
        $this->reportable(function (Throwable $e) {
            //
        });
    }

    // Jednoduse pretizit funkci prepareResponse() tak, ze necha probublat exception
    // (krome Symfony\Component\HttpKernel\Exception\NotFoundHttpException)
    // a Tracy se o ni postara - toto vyhodí klasickou Nette Server error,
    // kterou vsak lze v Tracy konfiguraci nadefinovat.
    // NotFoundHttpException se zpracuje klasickym zpusobem
    protected function prepareResponse($request, \Throwable $e)
    {
        if (Str::startsWith($request->getPathInfo(), '/api/')) {
            return $this->prepareApiResponse($request, $e);
        } else {
            return $this->prepareFrontendResponse($request, $e);
        }
    }

    private function prepareFrontendResponse($request, \Throwable $e)
    {
        if (!$this->isHttpException($e)) {
            throw $e;
        }
        return parent::prepareResponse($request, $e);
    }

    private function prepareApiResponse($request, \Throwable $e)
    {
        if (!$this->isHttpException($e)) {
            return response()->json(['responseMessage' => $e->getMessage()], 500);
        }
        return response()->json(['responseMessage' => $e->getMessage()], $e->getStatusCode());
    }

    // Nebo

    // Pretizit funkci prepareResponse() tak, ze vsechny exception (krome
    // Symfony\Component\HttpKernel\Exception\NotFoundHttpException) v DEVELOPMENT modu
    // necha probublat, ale na produkci se chovala jako standardni laravel aplikace.
    // Toto zobrazí Laravel 500 server error, je vsak potreba exception explicitne zalogovat
    // protected function prepareResponse($request, \Throwable $e)
    // {
    //     if (!$this->isHttpException($e)) {
    //         if (config('app.debug')) {
    //             // DEVELOPMENT - vyhodit exception a nechat ji Tracy zpracovat
    //             throw $e;
    //         } else {
    //             // PRODUCTION - zalogovat exception
    //             \Tracy\Debugger::log($e, \Tracy\Debugger::EXCEPTION);
    //         }
    //     }

    //     // 404 a exception v PRODUCTION - pouzit defaultni Laravel error 500 view
    //     return parent::prepareResponse($request, $e);
    // }
}
