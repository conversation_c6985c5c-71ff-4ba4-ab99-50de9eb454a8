<?php

namespace App\Enums;

enum ErrorType: int
{
    case EXCEPTION = -1;
    case E_ERROR = 1;
    case E_WARNING = 2;
    case E_PARSE = 4;
    case E_NOTICE = 8;
    case E_CORE_ERROR = 16;
    case E_CORE_WARNING = 32;
    case E_COMPILE_ERROR = 64;
    case E_COMPILE_WARNING = 128;
    case E_USER_ERROR = 256;
    case E_USER_WARNING = 512;
    case E_USER_NOTICE = 1024;
    case E_STRICT = 2048;
    case E_RECOVERABLE_ERROR = 4096;
    case E_DEPRECATED = 8196;
    case E_USER_DEPRECATED = 16384;

    public function getTitle(): string
    {
        return match ($this) {
            self::EXCEPTION => 'Exception',
            self::E_ERROR => 'Fatal error',
            self::E_WARNING => 'Warning',
            self::E_PARSE => 'Parse error',
            self::E_NOTICE => 'Notice',
            self::E_CORE_ERROR => 'Core error',
            self::E_CORE_WARNING => 'Core warning',
            self::E_COMPILE_ERROR => 'Compile error',
            self::E_COMPILE_WARNING => 'Compile warning',
            self::E_USER_ERROR => 'User error',
            self::E_USER_WARNING => 'User warning',
            self::E_USER_NOTICE => 'User notice',
            self::E_STRICT => 'Strict warning',
            self::E_RECOVERABLE_ERROR => 'Recoverable error',
            self::E_DEPRECATED => 'Deprecated',
            self::E_USER_DEPRECATED => 'User deprecated',
        };
    }

    public static function tryFromTitle(string $title): ?ErrorType
    {
        return match ($title) {
            'Fatal error', 'Error' => self::E_ERROR,
            'Warning' => self::E_WARNING,
            'Parse error' => self::E_PARSE,
            'Notice' => self::E_NOTICE,
            'Core error' => self::E_CORE_ERROR,
            'Core warning' => self::E_CORE_WARNING,
            'Compile error' => self::E_COMPILE_ERROR,
            'Compile warning' => self::E_COMPILE_WARNING,
            'User error' => self::E_USER_ERROR,
            'User warning' => self::E_USER_WARNING,
            'User notice' => self::E_USER_NOTICE,
            'Strict warning' => self::E_STRICT,
            'Recoverable error' => self::E_RECOVERABLE_ERROR,
            'Deprecated' => self::E_DEPRECATED,
            'User deprecated' => self::E_USER_DEPRECATED,
            default => null,
        };
    }
}
