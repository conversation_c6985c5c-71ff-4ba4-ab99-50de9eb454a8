<?php

namespace App\View\Components\FlashMessage;

use <PERSON><PERSON>\LaravelLatte\IComponent;

class Control implements IComponent
{
    public function init(...$params): void
    {
        //
    }

    public function render(): string
    {
        $timeout = 5000;
        $fm = session(Message::SESS_KEY);
        $type = $fm?->getType()?->uiKitClass() ?? 'uk-alert-primary';
        if ($fm) {
            return "<script>UIkit.notification({message: '{$fm->getMessage()}', status: '$type', timeout: '$timeout'});</script>";
        }
        return '';
    }
}
