<?php

namespace App\Providers;

use App\Auth\GitlabServiceProvider;
use App\Auth\OAuthServiceProvider;
use App\Domain\Services\GuzzleService;
use App\Model\Facades\UserFacade;
use DekApps\OAuth2\Client\Provider\Dek;
use Omines\OAuth2\Client\Provider\Gitlab;
use Illuminate\Foundation\Support\Providers\AuthServiceProvider as ServiceProvider;
use Illuminate\Support\Facades\Auth;

class AuthServiceProvider extends ServiceProvider
{
    /**
     * The model to policy mappings for the application.
     *
     * @var array<class-string, class-string>
     */
    protected $policies = [
        // 'App\Models\Model' => 'App\Policies\ModelPolicy',
    ];

    /**
     * Register services.
     *
     * @return void
     */
    public function register(): void
    {
        $this->app->bind(Gitlab::class, function () {
            return new Gitlab([
                'clientId' => 'a4217d17f0f523ec4a59c260b76153249f7b46aca28d6ce505995dceca47b3d0',
                'clientSecret' => 'gloas-23bb7fb36ce3aff98b287a89d0152e9de28622f662c1b0b01ce986548e4dc0d2',
                'redirectUri' => route('authenticate-gitlab.callback'),
                'domain' => config('gitlab.homepage'),
            ]);
        });

        $this->app->bind(Dek::class, function () {
            return new Dek([
                'clientId' => config('oauth2.clientId'),
                'clientSecret' => config('oauth2.clientSecret'),
                'redirectUri' => route('authenticate.callback'),
            ]);
        });
    }

    /**
     * Register any authentication / authorization services.
     */
    public function boot(): void
    {
        Auth::provider('gitlab', function ($app) {
            return new GitlabServiceProvider($app->get(UserFacade::class));
        });
        Auth::provider('oauth', function ($app) {
            return new OAuthServiceProvider($app->get(UserFacade::class));
        });
    }
}
