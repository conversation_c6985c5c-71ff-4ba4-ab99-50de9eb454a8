<?php

namespace App\Providers;

use Dek<PERSON>pps\Dektrine\EntityManagerFactory;
use DekApps\Dektrine\Loggers\SingletonSQLLogger;
use DekApps\Dektrine\TracyPanel\Panel;
use Doctrine\ORM\EntityManagerInterface;
use Illuminate\Support\ServiceProvider;
use Tracy\Debugger;

class DoctrineServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     *
     * @return void
     */
    public function register(): void
    {
        $this->app->singleton(EntityManagerInterface::class, function () {
            $factory = new EntityManagerFactory((array) config('doctrine.orm'));
            $factory->setLogger(SingletonSQLLogger::getInstance());
            return $factory->create();
        });
    }

    /**
     * Bootstrap services.
     *
     * @return void
     */
    public function boot(): void
    {
        $bar = Debugger::getBar();
        $logger = SingletonSQLLogger::getInstance();
        $panel = new Panel($logger);
        $bar->addPanel($panel);
    }
}
