<?php

namespace App\Http\Controllers;

use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group, which
| contains the “web” middleware group. Now create something great!
|
*/

Route::get('login', [AuthController::class, 'login'])->name('login');
Route::post('authenticate', [AuthController::class, 'authenticate'])->name('authenticate');
Route::get('authenticate/callback', [AuthController::class, 'handleCallback'])->name('authenticate.callback');
Route::post('authenticate-gitlab', [AuthController::class, 'authenticateGitlab'])->name('authenticate-gitlab');
Route::get('authenticate-gitlab/callback', [AuthController::class, 'handleGitlabCallback'])->name('authenticate-gitlab.callback');
Route::get('logout', [AuthController::class, 'logout'])->name('logout');

Route::middleware('auth:oauth,gitlab')->group(function () {
    Route::get('/', [ErrorController::class, 'index'])->name('homepage');
    Route::get('errors/{error}', [ErrorDetailController::class, 'detail'])->name('detail');
    Route::get('errors/{error}/trace', [ErrorDetailController::class, 'trace'])->name('detail.trace');
    Route::get('errors/{error}/children', [ErrorController::class, 'children'])->name('detail.children');
    Route::match(['get', 'post'], '/errors/{error}/{action}', [ErrorDetailController::class, 'performAction'])->name('detail.action');

    Route::get('add-dependant', [ErrorController::class, 'addErrorDependant'])->name('error.add-dependant');

    Route::resource('apps', AppController::class)->except(['show', 'delete']);
    Route::post('apps/{app}/action', [AppController::class, 'action'])->name('apps.action');
    Route::get('apps/{appSlug}/error-log', [AppController::class, 'showErrorLog'])->name('apps.error-log');
    Route::get('apps/{appSlug}', [ErrorController::class, 'appErrors'])->name('error');

    Route::resource('users', UserController::class);
});

Route::post('api/log', [ApiController::class, 'log'])->name('api.log');
Route::post('api/touch', [ApiController::class, 'touch'])->name('api.touch');
