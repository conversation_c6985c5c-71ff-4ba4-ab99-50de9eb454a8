//
// Component: Table
//
// ========================================================================


// Variables
// ========================================================================

@table-header-cell-font-size:                   @global-small-font-size;
@table-header-cell-font-weight:                 normal;
@table-header-cell-color:                       @global-muted-color;

//
// New
//

@table-striped-border-width:                    @global-border-width;
@table-striped-border:                          @global-border;


// Component
// ========================================================================

.hook-table-header-cell() { text-transform: uppercase; }

.hook-table-cell() {}

.hook-table-footer() {  }

.hook-table-caption() {}

.hook-table-row-active() {}


// Style modifiers
// ========================================================================

.hook-table-divider() {}

.hook-table-striped() {
    border-top: @table-striped-border-width solid @table-striped-border;
    border-bottom: @table-striped-border-width solid @table-striped-border;
}

.hook-table-hover() {}


// Size modifier
// ========================================================================

.hook-table-small() {}

.hook-table-large() {}


// Miscellaneous
// ========================================================================

.hook-table-misc() {

    .uk-table tbody tr { transition: background-color 0.1s linear; }

    .uk-table-striped > tr:nth-of-type(even):last-child,
    .uk-table-striped tbody tr:nth-of-type(even):last-child { border-bottom: @table-striped-border-width solid @table-striped-border; }

}


// Inverse
// ========================================================================

.hook-inverse-table-header-cell() {}
.hook-inverse-table-caption() {}
.hook-inverse-table-row-active() {}
.hook-inverse-table-divider() {}
.hook-inverse-table-striped() {
    border-top-color: @inverse-global-border;
    border-bottom-color: @inverse-global-border;
}
.hook-inverse-table-hover() {}

.hook-inverse() {
    .uk-table-striped > tr:nth-of-type(even):last-child,
    .uk-table-striped tbody tr:nth-of-type(even):last-child { border-bottom-color: @inverse-global-border; }
}
