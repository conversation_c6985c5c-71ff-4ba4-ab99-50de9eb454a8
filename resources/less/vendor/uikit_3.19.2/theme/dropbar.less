//
// Component: Dropbar
//
// ========================================================================


// Variables
// ========================================================================

@dropbar-padding-top:                           25px;
@dropbar-background:                            @global-background;

//
// New
//

@dropbar-top-box-shadow:                        0 12px 7px -6px rgba(0, 0, 0, 0.05);
@dropbar-bottom-box-shadow:                     0 -12px 7px -6px rgba(0, 0, 0, 0.05);
@dropbar-left-box-shadow:                       12px 0 7px -6px rgba(0, 0, 0, 0.05);
@dropbar-right-box-shadow:                      -12px 0 7px -6px rgba(0, 0, 0, 0.05);


// Component
// ========================================================================

.hook-dropbar() {}


// Direction modifier
// ========================================================================

.hook-dropbar-top() { box-shadow: @dropbar-top-box-shadow; }

.hook-dropbar-bottom() { box-shadow: @dropbar-bottom-box-shadow; }

.hook-dropbar-left() { box-shadow: @dropbar-left-box-shadow; }

.hook-dropbar-right() { box-shadow: @dropbar-right-box-shadow; }


// Miscellaneous
// ========================================================================

.hook-dropbar-misc() {}
