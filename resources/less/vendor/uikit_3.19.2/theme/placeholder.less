//
// Component: Placeholder
//
// ========================================================================


// Variables
// ========================================================================

@placeholder-background:                        transparent;

//
// New
//

@placeholder-border-width:                      @global-border-width;
@placeholder-border:                            @global-border;


// Component
// ========================================================================

.hook-placeholder() { border: @placeholder-border-width dashed @placeholder-border; }


// Miscellaneous
// ========================================================================

.hook-placeholder-misc() {}
