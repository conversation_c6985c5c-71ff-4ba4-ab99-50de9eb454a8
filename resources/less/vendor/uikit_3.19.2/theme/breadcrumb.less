//
// Component: Breadcrumb
//
// ========================================================================


// Variables
// ========================================================================


// Component
// ========================================================================

.hook-breadcrumb() {}


// Items
// ========================================================================

.hook-breadcrumb-item() {}

.hook-breadcrumb-item-hover() {}

.hook-breadcrumb-item-disabled() {}

.hook-breadcrumb-item-active() {}

.hook-breadcrumb-divider() {}


// Miscellaneous
// ========================================================================

.hook-breadcrumb-misc() {}


// Inverse
// ========================================================================

.hook-inverse-breadcrumb-item() {}
.hook-inverse-breadcrumb-item-hover() {}
.hook-inverse-breadcrumb-item-disabled() {}
.hook-inverse-breadcrumb-item-active() {}

.hook-inverse-breadcrumb-divider() {}
