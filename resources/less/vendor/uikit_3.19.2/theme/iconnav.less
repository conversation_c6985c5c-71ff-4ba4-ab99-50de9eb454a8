//
// Component: Iconnav
//
// ========================================================================


// Variables
// ========================================================================

//
// New
//

@subnav-item-font-size:                         @global-small-font-size;


// Component
// ========================================================================

.hook-iconnav() {}

.hook-iconnav-item() {
    font-size: @subnav-item-font-size;
    transition: 0.1s ease-in-out;
    transition-property: color, background-color;
}

.hook-iconnav-item-hover() {}

.hook-iconnav-item-active() {}


// Miscellaneous
// ========================================================================

.hook-iconnav-misc() {}


// Inverse
// ========================================================================

.hook-inverse-iconnav-item() {}
.hook-inverse-iconnav-item-hover() {}
.hook-inverse-iconnav-item-active() {}
