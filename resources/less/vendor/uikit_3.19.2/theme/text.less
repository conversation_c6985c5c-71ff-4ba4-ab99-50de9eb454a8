//
// Component: Text
//
// ========================================================================


// Variables
// ========================================================================

//
// New
//

@text-meta-link-color:                          @text-meta-color;
@text-meta-link-hover-color:                    @global-color;

@internal-text-background-color-gradient:         linear-gradient(90deg, @text-background-color 0%, spin(@text-background-color, 40) 100%);


// Style modifiers
// ========================================================================

.hook-text-lead() {}

.hook-text-meta() {

    > a { color: @text-meta-link-color; }

    > a:hover {
        color: @text-meta-link-hover-color;
        text-decoration: none;
    }

}


// Size modifiers
// ========================================================================

.hook-text-small() {}

.hook-text-large() {}


// Background modifier
// ========================================================================

.hook-text-background() { background-image: @internal-text-background-color-gradient; }


// Miscellaneous
// ========================================================================

.hook-text-misc() {}


// Inverse
// ========================================================================

.hook-inverse-text-lead() {}
.hook-inverse-text-meta() {}
