// Name:            Width
// Description:     Utilities for widths
//
// Component:       `uk-child-width-*`
//                  `uk-width-*`
//
// ========================================================================


// Variables
// ========================================================================

@width-small-width:                             150px;
@width-medium-width:                            300px;
@width-large-width:                             450px;
@width-xlarge-width:                            600px;
@width-2xlarge-width:                           750px;


/* ========================================================================
   Component: Width
 ========================================================================== */


/* Equal child widths
 ========================================================================== */

[class*="uk-child-width"] > * {
    box-sizing: border-box;
    width: 100%;
}

.uk-child-width-1-2 > * { width: 50%; }
.uk-child-width-1-3 > * { width: ~'calc(100% / 3)'; }
.uk-child-width-1-4 > * { width: 25%; }
.uk-child-width-1-5 > * { width: 20%; }
.uk-child-width-1-6 > * { width: ~'calc(100% / 6)'; }

.uk-child-width-auto > * { width: auto; }

/*
 * 1. Reset the `min-width`, which is set to auto by default, because
 *    flex items won't shrink below their minimum intrinsic content size.
 *    Using `1px` instead of `0`, so items still wrap into the next line,
 *    if they have zero width and padding and the predecessor is 100% wide.
 */

.uk-child-width-expand > :not([class*="uk-width"]) {
    flex: 1;
    /* 1 */
    min-width: 1px;
}

/* Phone landscape and bigger */
@media (min-width: @breakpoint-small) {

    .uk-child-width-1-1\@s > * { width: 100%; }
    .uk-child-width-1-2\@s > * { width: 50%; }
    .uk-child-width-1-3\@s > * { width: ~'calc(100% / 3)'; }
    .uk-child-width-1-4\@s > * { width: 25%; }
    .uk-child-width-1-5\@s > * { width: 20%; }
    .uk-child-width-1-6\@s > * { width: ~'calc(100% / 6)'; }

    .uk-child-width-auto\@s > * { width: auto; }
    .uk-child-width-expand\@s > :not([class*="uk-width"]) {
        flex: 1;
        min-width: 1px;
    }

    /* Reset expand */
    .uk-child-width-1-1\@s > :not([class*="uk-width"]),
    .uk-child-width-1-2\@s > :not([class*="uk-width"]),
    .uk-child-width-1-3\@s > :not([class*="uk-width"]),
    .uk-child-width-1-4\@s > :not([class*="uk-width"]),
    .uk-child-width-1-5\@s > :not([class*="uk-width"]),
    .uk-child-width-1-6\@s > :not([class*="uk-width"]),
    .uk-child-width-auto\@s > :not([class*="uk-width"]) { flex: initial; }

}

/* Tablet landscape and bigger */
@media (min-width: @breakpoint-medium) {

    .uk-child-width-1-1\@m > * { width: 100%; }
    .uk-child-width-1-2\@m > * { width: 50%; }
    .uk-child-width-1-3\@m > * { width: ~'calc(100% / 3)'; }
    .uk-child-width-1-4\@m > * { width: 25%; }
    .uk-child-width-1-5\@m > * { width: 20%; }
    .uk-child-width-1-6\@m > * { width: ~'calc(100% / 6)'; }

    .uk-child-width-auto\@m > * { width: auto; }
    .uk-child-width-expand\@m > :not([class*="uk-width"]) {
        flex: 1;
        min-width: 1px;
    }

    /* Reset expand */
    .uk-child-width-1-1\@m > :not([class*="uk-width"]),
    .uk-child-width-1-2\@m > :not([class*="uk-width"]),
    .uk-child-width-1-3\@m > :not([class*="uk-width"]),
    .uk-child-width-1-4\@m > :not([class*="uk-width"]),
    .uk-child-width-1-5\@m > :not([class*="uk-width"]),
    .uk-child-width-1-6\@m > :not([class*="uk-width"]),
    .uk-child-width-auto\@m > :not([class*="uk-width"]) { flex: initial; }

}

/* Desktop and bigger */
@media (min-width: @breakpoint-large) {

    .uk-child-width-1-1\@l > * { width: 100%; }
    .uk-child-width-1-2\@l > * { width: 50%; }
    .uk-child-width-1-3\@l > * { width: ~'calc(100% / 3)'; }
    .uk-child-width-1-4\@l > * { width: 25%; }
    .uk-child-width-1-5\@l > * { width: 20%; }
    .uk-child-width-1-6\@l > * { width: ~'calc(100% / 6)'; }

    .uk-child-width-auto\@l > * { width: auto; }
    .uk-child-width-expand\@l > :not([class*="uk-width"]) {
        flex: 1;
        min-width: 1px;
    }

    /* Reset expand */
    .uk-child-width-1-1\@l > :not([class*="uk-width"]),
    .uk-child-width-1-2\@l > :not([class*="uk-width"]),
    .uk-child-width-1-3\@l > :not([class*="uk-width"]),
    .uk-child-width-1-4\@l > :not([class*="uk-width"]),
    .uk-child-width-1-5\@l > :not([class*="uk-width"]),
    .uk-child-width-1-6\@l > :not([class*="uk-width"]),
    .uk-child-width-auto\@l > :not([class*="uk-width"]) { flex: initial; }

}

/* Large screen and bigger */
@media (min-width: @breakpoint-xlarge) {

    .uk-child-width-1-1\@xl > * { width: 100%; }
    .uk-child-width-1-2\@xl > * { width: 50%; }
    .uk-child-width-1-3\@xl > * { width: ~'calc(100% / 3)'; }
    .uk-child-width-1-4\@xl > * { width: 25%; }
    .uk-child-width-1-5\@xl > * { width: 20%; }
    .uk-child-width-1-6\@xl > * { width: ~'calc(100% / 6)'; }

    .uk-child-width-auto\@xl > * { width: auto; }
    .uk-child-width-expand\@xl > :not([class*="uk-width"]) {
        flex: 1;
        min-width: 1px;
    }

    /* Reset expand */
    .uk-child-width-1-1\@xl > :not([class*="uk-width"]),
    .uk-child-width-1-2\@xl > :not([class*="uk-width"]),
    .uk-child-width-1-3\@xl > :not([class*="uk-width"]),
    .uk-child-width-1-4\@xl > :not([class*="uk-width"]),
    .uk-child-width-1-5\@xl > :not([class*="uk-width"]),
    .uk-child-width-1-6\@xl > :not([class*="uk-width"]),
    .uk-child-width-auto\@xl > :not([class*="uk-width"]) { flex: initial; }

}


/* Single Widths
 ========================================================================== */

/*
 * 1. `max-width` is needed for the pixel-based classes
 */

[class*="uk-width"] {
    box-sizing: border-box;
    width: 100%;
    /* 1 */
    max-width: 100%;
}

/* Halves */
.uk-width-1-2 { width: 50%; }

/* Thirds */
.uk-width-1-3 { width: ~'calc(100% / 3)'; }
.uk-width-2-3 { width: ~'calc(200% / 3)'; }

/* Quarters */
.uk-width-1-4 { width: 25%; }
.uk-width-3-4 { width: 75%; }

/* Fifths */
.uk-width-1-5 { width: 20%; }
.uk-width-2-5 { width: 40%; }
.uk-width-3-5 { width: 60%; }
.uk-width-4-5 { width: 80%; }

/* Sixths */
.uk-width-1-6 { width: ~'calc(100% / 6)'; }
.uk-width-5-6 { width: ~'calc(500% / 6)'; }

/* Pixel */
.uk-width-small { width: @width-small-width; }
.uk-width-medium { width: @width-medium-width; }
.uk-width-large { width: @width-large-width; }
.uk-width-xlarge { width: @width-xlarge-width; }
.uk-width-2xlarge { width: @width-2xlarge-width; }
.uk-width-xxlarge when (@deprecated = true) { width: @width-2xlarge-width; }

/* Auto */
.uk-width-auto { width: auto; }

/* Expand */
.uk-width-expand {
    flex: 1;
    min-width: 1px;
}

/* Phone landscape and bigger */
@media (min-width: @breakpoint-small) {

    /* Whole */
    .uk-width-1-1\@s { width: 100%; }

    /* Halves */
    .uk-width-1-2\@s { width: 50%; }

    /* Thirds */
    .uk-width-1-3\@s { width: ~'calc(100% / 3)'; }
    .uk-width-2-3\@s { width: ~'calc(200% / 3)'; }

    /* Quarters */
    .uk-width-1-4\@s { width: 25%; }
    .uk-width-3-4\@s { width: 75%; }

    /* Fifths */
    .uk-width-1-5\@s { width: 20%; }
    .uk-width-2-5\@s { width: 40%; }
    .uk-width-3-5\@s { width: 60%; }
    .uk-width-4-5\@s { width: 80%; }

    /* Sixths */
    .uk-width-1-6\@s { width: ~'calc(100% / 6)'; }
    .uk-width-5-6\@s { width: ~'calc(500% / 6)'; }

    /* Pixel */
    .uk-width-small\@s { width: @width-small-width; }
    .uk-width-medium\@s { width: @width-medium-width; }
    .uk-width-large\@s { width: @width-large-width; }
    .uk-width-xlarge\@s { width: @width-xlarge-width; }
    .uk-width-2xlarge\@s { width: @width-2xlarge-width; }
    .uk-width-xxlarge\@s when (@deprecated = true) { width: @width-2xlarge-width; }

    /* Auto */
    .uk-width-auto\@s { width: auto; }

    /* Expand */
    .uk-width-expand\@s {
        flex: 1;
        min-width: 1px;
    }

    /* Reset expand */
    .uk-width-1-1\@s,
    .uk-width-1-2\@s,
    .uk-width-1-3\@s,
    .uk-width-2-3\@s,
    .uk-width-1-4\@s,
    .uk-width-3-4\@s,
    .uk-width-1-5\@s,
    .uk-width-2-5\@s,
    .uk-width-3-5\@s,
    .uk-width-4-5\@s,
    .uk-width-1-6\@s,
    .uk-width-5-6\@s,
    .uk-width-small\@s,
    .uk-width-medium\@s,
    .uk-width-large\@s,
    .uk-width-xlarge\@s,
    .uk-width-2xlarge\@s,
    .uk-width-auto\@s { flex: initial; }

}

/* Tablet landscape and bigger */
@media (min-width: @breakpoint-medium) {

    /* Whole */
    .uk-width-1-1\@m { width: 100%; }

    /* Halves */
    .uk-width-1-2\@m { width: 50%; }

    /* Thirds */
    .uk-width-1-3\@m { width: ~'calc(100% / 3)'; }
    .uk-width-2-3\@m { width: ~'calc(200% / 3)'; }

    /* Quarters */
    .uk-width-1-4\@m { width: 25%; }
    .uk-width-3-4\@m { width: 75%; }

    /* Fifths */
    .uk-width-1-5\@m { width: 20%; }
    .uk-width-2-5\@m { width: 40%; }
    .uk-width-3-5\@m { width: 60%; }
    .uk-width-4-5\@m { width: 80%; }

    /* Sixths */
    .uk-width-1-6\@m { width: ~'calc(100% / 6)'; }
    .uk-width-5-6\@m { width: ~'calc(500% / 6)'; }

    /* Pixel */
    .uk-width-small\@m { width: @width-small-width; }
    .uk-width-medium\@m { width: @width-medium-width; }
    .uk-width-large\@m { width: @width-large-width; }
    .uk-width-xlarge\@m { width: @width-xlarge-width; }
    .uk-width-2xlarge\@m { width: @width-2xlarge-width; }
    .uk-width-xxlarge\@m when (@deprecated = true) { width: @width-2xlarge-width; }

     /* Auto */
    .uk-width-auto\@m { width: auto; }

    /* Expand */
    .uk-width-expand\@m {
        flex: 1;
        min-width: 1px;
    }

    /* Reset expand */
    .uk-width-1-1\@m,
    .uk-width-1-2\@m,
    .uk-width-1-3\@m,
    .uk-width-2-3\@m,
    .uk-width-1-4\@m,
    .uk-width-3-4\@m,
    .uk-width-1-5\@m,
    .uk-width-2-5\@m,
    .uk-width-3-5\@m,
    .uk-width-4-5\@m,
    .uk-width-1-6\@m,
    .uk-width-5-6\@m,
    .uk-width-small\@m,
    .uk-width-medium\@m,
    .uk-width-large\@m,
    .uk-width-xlarge\@m,
    .uk-width-2xlarge\@m,
    .uk-width-auto\@m { flex: initial; }

}

/* Desktop and bigger */
@media (min-width: @breakpoint-large) {

    /* Whole */
    .uk-width-1-1\@l { width: 100%; }

    /* Halves */
    .uk-width-1-2\@l { width: 50%; }

    /* Thirds */
    .uk-width-1-3\@l { width: ~'calc(100% / 3)'; }
    .uk-width-2-3\@l { width: ~'calc(200% / 3)'; }

    /* Quarters */
    .uk-width-1-4\@l { width: 25%; }
    .uk-width-3-4\@l { width: 75%; }

    /* Fifths */
    .uk-width-1-5\@l { width: 20%; }
    .uk-width-2-5\@l { width: 40%; }
    .uk-width-3-5\@l { width: 60%; }
    .uk-width-4-5\@l { width: 80%; }

    /* Sixths */
    .uk-width-1-6\@l { width: ~'calc(100% / 6)'; }
    .uk-width-5-6\@l { width: ~'calc(500% / 6)'; }

    /* Pixel */
    .uk-width-small\@l { width: @width-small-width; }
    .uk-width-medium\@l { width: @width-medium-width; }
    .uk-width-large\@l { width: @width-large-width; }
    .uk-width-xlarge\@l { width: @width-xlarge-width; }
    .uk-width-2xlarge\@l { width: @width-2xlarge-width; }
    .uk-width-xxlarge\@l when (@deprecated = true) { width: @width-2xlarge-width; }

    /* Auto */
    .uk-width-auto\@l { width: auto; }

    /* Expand */
    .uk-width-expand\@l {
        flex: 1;
        min-width: 1px;
    }

    /* Reset expand */
    .uk-width-1-1\@l,
    .uk-width-1-2\@l,
    .uk-width-1-3\@l,
    .uk-width-2-3\@l,
    .uk-width-1-4\@l,
    .uk-width-3-4\@l,
    .uk-width-1-5\@l,
    .uk-width-2-5\@l,
    .uk-width-3-5\@l,
    .uk-width-4-5\@l,
    .uk-width-1-6\@l,
    .uk-width-5-6\@l,
    .uk-width-small\@l,
    .uk-width-medium\@l,
    .uk-width-large\@l,
    .uk-width-xlarge\@l,
    .uk-width-2xlarge\@l,
    .uk-width-auto\@l { flex: initial; }

}

/* Large screen and bigger */
@media (min-width: @breakpoint-xlarge) {

    /* Whole */
    .uk-width-1-1\@xl { width: 100%; }

    /* Halves */
    .uk-width-1-2\@xl { width: 50%; }

    /* Thirds */
    .uk-width-1-3\@xl { width: ~'calc(100% / 3)'; }
    .uk-width-2-3\@xl { width: ~'calc(200% / 3)'; }

    /* Quarters */
    .uk-width-1-4\@xl { width: 25%; }
    .uk-width-3-4\@xl { width: 75%; }

    /* Fifths */
    .uk-width-1-5\@xl { width: 20%; }
    .uk-width-2-5\@xl { width: 40%; }
    .uk-width-3-5\@xl { width: 60%; }
    .uk-width-4-5\@xl { width: 80%; }

    /* Sixths */
    .uk-width-1-6\@xl { width: ~'calc(100% / 6)'; }
    .uk-width-5-6\@xl { width: ~'calc(500% / 6)'; }

    /* Pixel */
    .uk-width-small\@xl { width: @width-small-width; }
    .uk-width-medium\@xl { width: @width-medium-width; }
    .uk-width-large\@xl { width: @width-large-width; }
    .uk-width-xlarge\@xl { width: @width-xlarge-width; }
    .uk-width-2xlarge\@xl { width: @width-2xlarge-width; }
    .uk-width-xxlarge\@xl when (@deprecated = true) { width: @width-2xlarge-width; }

    /* Auto */
    .uk-width-auto\@xl { width: auto; }

    /* Expand */
    .uk-width-expand\@xl {
        flex: 1;
        min-width: 1px;
    }

    /* Reset expand */
    .uk-width-1-1\@xl,
    .uk-width-1-2\@xl,
    .uk-width-1-3\@xl,
    .uk-width-2-3\@xl,
    .uk-width-1-4\@xl,
    .uk-width-3-4\@xl,
    .uk-width-1-5\@xl,
    .uk-width-2-5\@xl,
    .uk-width-3-5\@xl,
    .uk-width-4-5\@xl,
    .uk-width-1-6\@xl,
    .uk-width-5-6\@xl,
    .uk-width-small\@xl,
    .uk-width-medium\@xl,
    .uk-width-large\@xl,
    .uk-width-xlarge\@xl,
    .uk-width-2xlarge\@xl,
    .uk-width-auto\@xl { flex: initial; }

}

/* Intrinsic Widths
 ========================================================================== */

.uk-width-max-content { width: max-content; }

.uk-width-min-content { width: min-content; }


// Hooks
// ========================================================================

.hook-width-misc();

.hook-width-misc() {}
