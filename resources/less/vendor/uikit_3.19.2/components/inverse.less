// Name:            Inverse
// Description:     Inverse component style for light or dark backgrounds
//
// Component:       `uk-light`
//                  `uk-dark`
//
// ========================================================================


// Variables
// ========================================================================

@inverse-global-color-mode:                     light;

@inverse-global-color:                          fade(@global-inverse-color, 70%);
@inverse-global-emphasis-color:                 @global-inverse-color;
@inverse-global-muted-color:                    fade(@global-inverse-color, 50%);
@inverse-global-inverse-color:                  @global-color;

@inverse-global-primary-background:             @global-inverse-color;
@inverse-global-muted-background:               fade(@global-inverse-color, 10%);

@inverse-global-border:                         fade(@global-inverse-color, 20%);


/* ========================================================================
   Component: Inverse
 ========================================================================== */

/*
 * Implemented class depends on the general theme color
 * `uk-light` is for light colors on dark backgrounds
 * `uk-dark` is or dark colors on light backgrounds
 */

.uk-light when (@inverse-global-color-mode = light) {

    .hook-inverse();

}

.uk-dark when (@inverse-global-color-mode = dark) {

    .hook-inverse();

}

/*
 * Pass dropbar behind color to JS
 */

* { --uk-inverse: initial; }

.uk-light { --uk-inverse: light; }
.uk-dark { --uk-inverse: dark; }

.uk-inverse-light { --uk-inverse: light !important; }
.uk-inverse-dark { --uk-inverse: dark !important; }


// Hooks
// ========================================================================

.hook-inverse() {}
