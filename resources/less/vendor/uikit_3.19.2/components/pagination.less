// Name:            Pagination
// Description:     Component to create a page navigation
//
// Component:       `uk-pagination`
//
// Adopted:         `uk-pagination-next`
//                  `uk-pagination-previous`
//
// States:          `uk-active`
//                  `uk-disabled`
//
// ========================================================================


// Variables
// ========================================================================

@pagination-margin-horizontal:                  0;

@pagination-item-padding-vertical:              5px;
@pagination-item-padding-horizontal:            10px;
@pagination-item-color:                         @global-muted-color;
@pagination-item-hover-color:                   @global-color;
@pagination-item-hover-text-decoration:         none;
@pagination-item-active-color:                  @global-color;
@pagination-item-disabled-color:                @global-muted-color;


/* ========================================================================
   Component: Pagination
 ========================================================================== */

/*
 * 1. Allow items to wrap into the next line
 * 2. Center items vertically if they have a different height
 * 3. Gutter
 * 4. Reset list
 */

.uk-pagination {
    display: flex;
    /* 1 */
    flex-wrap: wrap;
    /* 2 */
    align-items: center;
    /* 3 */
    margin-left: -@pagination-margin-horizontal;
    /* 4 */
    padding: 0;
    list-style: none;
    .hook-pagination();
}

/*
 * 1. Space is allocated solely based on content dimensions: 0 0 auto
 * 2. Gutter
 * 3. Create position context for dropdowns
 */

.uk-pagination > * {
    /* 1 */
    flex: none;
    /* 2 */
    padding-left: @pagination-margin-horizontal;
    /* 3 */
    position: relative;
}


/* Items
 ========================================================================== */

/*
 * 1. Center content vertically, e.g. an icon
 * 2. Imitate white space gap when using flexbox
 * 3. Style
 */

.uk-pagination > * > * {
    /* 1 */
    display: flex;
    align-items: center;
    /* 2 */
    column-gap: 0.25em;
    /* 3 */
    padding: @pagination-item-padding-vertical @pagination-item-padding-horizontal;
    color: @pagination-item-color;
    .hook-pagination-item();
}

/* Hover */
.uk-pagination > * > :hover {
    color: @pagination-item-hover-color;
    text-decoration: @pagination-item-hover-text-decoration;
    .hook-pagination-item-hover();
}

/* Active */
.uk-pagination > .uk-active > * {
    color: @pagination-item-active-color;
    .hook-pagination-item-active();
}

/* Disabled */
.uk-pagination > .uk-disabled > * {
    color: @pagination-item-disabled-color;
    .hook-pagination-item-disabled();
}


// Hooks
// ========================================================================

.hook-pagination-misc();

.hook-pagination() {}
.hook-pagination-item() {}
.hook-pagination-item-hover() {}
.hook-pagination-item-active() {}
.hook-pagination-item-disabled() {}
.hook-pagination-misc() {}


// Inverse
// ========================================================================

@inverse-pagination-item-color:                @inverse-global-muted-color;
@inverse-pagination-item-hover-color:          @inverse-global-color;
@inverse-pagination-item-active-color:         @inverse-global-color;
@inverse-pagination-item-disabled-color:       @inverse-global-muted-color;

.hook-inverse() {

    .uk-pagination > * > * {
        color: @inverse-pagination-item-color;
        .hook-inverse-pagination-item();
    }

    .uk-pagination > * > :hover {
        color: @inverse-pagination-item-hover-color;
        .hook-inverse-pagination-item-hover();
    }

    .uk-pagination > .uk-active > * {
        color: @inverse-pagination-item-active-color;
        .hook-inverse-pagination-item-active();
    }

    .uk-pagination > .uk-disabled > * {
        color: @inverse-pagination-item-disabled-color;
        .hook-inverse-pagination-item-disabled();
    }

}

.hook-inverse-pagination-item() {}
.hook-inverse-pagination-item-hover() {}
.hook-inverse-pagination-item-active() {}
.hook-inverse-pagination-item-disabled() {}
