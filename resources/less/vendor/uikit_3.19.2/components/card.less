// Name:            Card
// Description:     Component to create boxed content containers
//
// Component:       `uk-card`
//
// Sub-objects:     `uk-card-body`
//                  `uk-card-header`
//                  `uk-card-footer`
//                  `uk-card-media-*`
//                  `uk-card-title`
//                  `uk-card-badge`
//
// Modifiers:       `uk-card-hover`
//                  `uk-card-default`
//                  `uk-card-primary`
//                  `uk-card-secondary`
//                  `uk-card-small`
//                  `uk-card-large`
//
// Uses:            `uk-grid-stack`
//
// ========================================================================


// Variables
// ========================================================================

@card-body-padding-horizontal:                 @global-gutter;
@card-body-padding-vertical:                   @global-gutter;

@card-body-padding-horizontal-l:               @global-medium-gutter;
@card-body-padding-vertical-l:                 @global-medium-gutter;

@card-header-padding-horizontal:               @global-gutter;
@card-header-padding-vertical:                 round((@global-gutter / 2));

@card-header-padding-horizontal-l:             @global-medium-gutter;
@card-header-padding-vertical-l:               round((@global-medium-gutter / 2));

@card-footer-padding-horizontal:               @global-gutter;
@card-footer-padding-vertical:                 (@global-gutter / 2);

@card-footer-padding-horizontal-l:             @global-medium-gutter;
@card-footer-padding-vertical-l:               round((@global-medium-gutter / 2));

@card-title-font-size:                         @global-large-font-size;
@card-title-line-height:                       1.4;

@card-badge-top:                               15px;
@card-badge-right:                             15px;
@card-badge-height:                            22px;
@card-badge-padding-horizontal:                10px;
@card-badge-background:                        @global-primary-background;
@card-badge-color:                             @global-inverse-color;
@card-badge-font-size:                         @global-small-font-size;

@card-hover-background:                        @global-muted-background;

@card-default-background:                      @global-muted-background;
@card-default-color:                           @global-color;
@card-default-title-color:                     @global-emphasis-color;
@card-default-hover-background:                darken(@card-default-background, 5%);
@card-default-color-mode:                      dark;

@card-primary-background:                      @global-primary-background;
@card-primary-color:                           @global-inverse-color;
@card-primary-title-color:                     @card-primary-color;
@card-primary-hover-background:                darken(@card-primary-background, 5%);
@card-primary-color-mode:                      light;

@card-secondary-background:                    @global-secondary-background;
@card-secondary-color:                         @global-inverse-color;
@card-secondary-title-color:                   @card-secondary-color;
@card-secondary-hover-background:              darken(@card-secondary-background, 5%);
@card-secondary-color-mode:                    light;

@card-small-body-padding-horizontal:           @global-margin;
@card-small-body-padding-vertical:             @global-margin;
@card-small-header-padding-horizontal:         @global-margin;
@card-small-header-padding-vertical:           round((@global-margin / 1.5));
@card-small-footer-padding-horizontal:         @global-margin;
@card-small-footer-padding-vertical:           round((@global-margin / 1.5));

@card-large-body-padding-horizontal-l:         @global-large-gutter;
@card-large-body-padding-vertical-l:           @global-large-gutter;
@card-large-header-padding-horizontal-l:       @global-large-gutter;
@card-large-header-padding-vertical-l:         round((@global-large-gutter / 2));
@card-large-footer-padding-horizontal-l:       @global-large-gutter;
@card-large-footer-padding-vertical-l:         round((@global-large-gutter / 2));


/* ========================================================================
   Component: Card
 ========================================================================== */

.uk-card {
    position: relative;
    box-sizing: border-box;
    .hook-card();
}


/* Sections
 ========================================================================== */

.uk-card-body {
    display: flow-root;
    padding: @card-body-padding-vertical @card-body-padding-horizontal;
    .hook-card-body();
}

.uk-card-header {
    display: flow-root;
    padding: @card-header-padding-vertical @card-header-padding-horizontal;
    .hook-card-header();
}

.uk-card-footer {
    display: flow-root;
    padding: @card-footer-padding-vertical @card-footer-padding-horizontal;
    .hook-card-footer();
}

/* Desktop and bigger */
@media (min-width: @breakpoint-large) {

    .uk-card-body { padding: @card-body-padding-vertical-l @card-body-padding-horizontal-l; }

    .uk-card-header { padding: @card-header-padding-vertical-l @card-header-padding-horizontal-l; }

    .uk-card-footer { padding: @card-footer-padding-vertical-l @card-footer-padding-horizontal-l; }

}

/*
 * Remove margin from the last-child
 */

.uk-card-body > :last-child,
.uk-card-header > :last-child,
.uk-card-footer > :last-child { margin-bottom: 0; }


/* Media
 ========================================================================== */

/*
 * Reserved alignment modifier to style the media element, e.g. with `border-radius`
 * Implemented by the theme
 */

[class*="uk-card-media"] {
    .hook-card-media();
}

.uk-card-media-top,
.uk-grid-stack > .uk-card-media-left,
.uk-grid-stack > .uk-card-media-right {
    .hook-card-media-top();
}

.uk-card-media-bottom {
    .hook-card-media-bottom();
}

:not(.uk-grid-stack) > .uk-card-media-left {
    .hook-card-media-left();
}

:not(.uk-grid-stack) > .uk-card-media-right {
    .hook-card-media-right();
}


/* Title
 ========================================================================== */

.uk-card-title {
    font-size: @card-title-font-size;
    line-height: @card-title-line-height;
    .hook-card-title();
}


/* Badge
 ========================================================================== */

/*
 * 1. Position
 * 2. Size
 * 3. Style
 * 4. Center child vertically
 */

.uk-card-badge {
    /* 1 */
    position: absolute;
    top: @card-badge-top;
    right: @card-badge-right;
    z-index: 1;
    /* 2 */
    height: @card-badge-height;
    padding: 0 @card-badge-padding-horizontal;
    /* 3 */
    background: @card-badge-background;
    color: @card-badge-color;
    font-size: @card-badge-font-size;
    /* 4 */
    display: flex;
    justify-content: center;
    align-items: center;
    line-height: 0;
    .hook-card-badge();
}

/*
 * Remove margin from adjacent element
 */

.uk-card-badge:first-child + * { margin-top: 0; }


/* Hover modifier
 ========================================================================== */

.uk-card-hover:not(.uk-card-default):not(.uk-card-primary):not(.uk-card-secondary):hover {
    background-color: @card-hover-background;
    .hook-card-hover();
}


/* Style modifiers
 ========================================================================== */

/*
 * Default
 * Note: Header and Footer are only implemented for the default style
 */

.uk-card-default {
    background-color: @card-default-background;
    color: @card-default-color;
    .hook-card-default();
}

.uk-card-default .uk-card-title {
    color: @card-default-title-color;
    .hook-card-default-title();
}

.uk-card-default.uk-card-hover:hover {
    background-color: @card-default-hover-background;
    .hook-card-default-hover();
}

.uk-card-default .uk-card-header {
    .hook-card-default-header();
}

.uk-card-default .uk-card-footer {
    .hook-card-default-footer();
}

// Color Mode
.uk-card-default.uk-card-body:extend(.uk-light all) when (@card-default-color-mode = light) {}
.uk-card-default > :not([class*="uk-card-media"]):extend(.uk-light all) when (@card-default-color-mode = light) {}
.uk-card-default.uk-card-body:extend(.uk-dark all) when (@card-default-color-mode = dark) {}
.uk-card-default > :not([class*="uk-card-media"]):extend(.uk-dark all) when (@card-default-color-mode = dark) {}

/*
 * Primary
 */

.uk-card-primary {
    background-color: @card-primary-background;
    color: @card-primary-color;
    .hook-card-primary();
}

.uk-card-primary .uk-card-title {
    color: @card-primary-title-color;
    .hook-card-primary-title();
}

.uk-card-primary.uk-card-hover:hover {
    background-color: @card-primary-hover-background;
    .hook-card-primary-hover();
}

// Color Mode
.uk-card-primary.uk-card-body:extend(.uk-light all) when (@card-primary-color-mode = light) {}
.uk-card-primary > :not([class*="uk-card-media"]):extend(.uk-light all) when (@card-primary-color-mode = light) {}
.uk-card-primary.uk-card-body:extend(.uk-dark all) when (@card-primary-color-mode = dark) {}
.uk-card-primary > :not([class*="uk-card-media"]):extend(.uk-dark all) when (@card-primary-color-mode = dark) {}

/*
 * Secondary
 */

.uk-card-secondary {
    background-color: @card-secondary-background;
    color: @card-secondary-color;
    .hook-card-secondary();
}

.uk-card-secondary .uk-card-title {
    color: @card-secondary-title-color;
    .hook-card-secondary-title();
}

.uk-card-secondary.uk-card-hover:hover {
    background-color: @card-secondary-hover-background;
    .hook-card-secondary-hover();
}

// Color Mode
.uk-card-secondary.uk-card-body:extend(.uk-light all) when (@card-secondary-color-mode = light) {}
.uk-card-secondary > :not([class*="uk-card-media"]):extend(.uk-light all) when (@card-secondary-color-mode = light) {}
.uk-card-secondary.uk-card-body:extend(.uk-dark all) when (@card-secondary-color-mode = dark) {}
.uk-card-secondary > :not([class*="uk-card-media"]):extend(.uk-dark all) when (@card-secondary-color-mode = dark) {}


/* Size modifier
 ========================================================================== */

/*
 * Small
 */

.uk-card-small.uk-card-body,
.uk-card-small .uk-card-body { padding: @card-small-body-padding-vertical @card-small-body-padding-horizontal; }

.uk-card-small .uk-card-header { padding: @card-small-header-padding-vertical @card-small-header-padding-horizontal; }
.uk-card-small .uk-card-footer { padding: @card-small-footer-padding-vertical @card-small-footer-padding-horizontal; }

/*
 * Large
 */

/* Desktop and bigger */
@media (min-width: @breakpoint-large) {

    .uk-card-large.uk-card-body,
    .uk-card-large .uk-card-body { padding: @card-large-body-padding-vertical-l @card-large-body-padding-horizontal-l; }

    .uk-card-large .uk-card-header { padding: @card-large-header-padding-vertical-l @card-large-header-padding-horizontal-l; }
    .uk-card-large .uk-card-footer { padding: @card-large-footer-padding-vertical-l @card-large-footer-padding-horizontal-l; }

}


// Hooks
// ========================================================================

.hook-card-misc();

.hook-card() {}
.hook-card-body() {}
.hook-card-header() {}
.hook-card-footer() {}
.hook-card-media() {}
.hook-card-media-top() {}
.hook-card-media-bottom() {}
.hook-card-media-left() {}
.hook-card-media-right() {}
.hook-card-title() {}
.hook-card-badge() {}
.hook-card-hover() {}
.hook-card-default() {}
.hook-card-default-title() {}
.hook-card-default-hover() {}
.hook-card-default-header() {}
.hook-card-default-footer() {}
.hook-card-primary() {}
.hook-card-primary-title() {}
.hook-card-primary-hover() {}
.hook-card-secondary() {}
.hook-card-secondary-title() {}
.hook-card-secondary-hover() {}
.hook-card-misc() {}


// Inverse
// ========================================================================

@inverse-card-badge-background:                     @inverse-global-primary-background;
@inverse-card-badge-color:                          @inverse-global-inverse-color;

.hook-inverse() {

    &.uk-card-badge {
        background-color: @inverse-card-badge-background;
        color: @inverse-card-badge-color;
        .hook-inverse-card-badge();
    }

}

.hook-inverse-card-badge() {}
