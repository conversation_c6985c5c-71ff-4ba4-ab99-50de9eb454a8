// Name:            Link
// Description:     Styles for links
//
// Component:       `uk-link-muted`
//                  `uk-link-text`
//                  `uk-link-heading`
//                  `uk-link-reset`
//
// Sub-objects:     `uk-link-toggle`
//
// ========================================================================


// Variables
// ========================================================================

@link-muted-color:                              @global-muted-color;
@link-muted-hover-color:                        @global-color;

@link-text-hover-color:                         @global-muted-color;

@link-heading-hover-color:                      @global-primary-background;
@link-heading-hover-text-decoration:            none;


/* ========================================================================
   Component: Link
 ========================================================================== */


/* Muted
 ========================================================================== */

a.uk-link-muted,
.uk-link-muted a,
.uk-link-toggle .uk-link-muted {
    color: @link-muted-color;
    .hook-link-muted();
}

a.uk-link-muted:hover,
.uk-link-muted a:hover,
.uk-link-toggle:hover .uk-link-muted {
    color: @link-muted-hover-color;
    .hook-link-muted-hover();
}


/* Text
 ========================================================================== */

a.uk-link-text,
.uk-link-text a,
.uk-link-toggle .uk-link-text {
    color: inherit;
    .hook-link-text();
}

a.uk-link-text:hover,
.uk-link-text a:hover,
.uk-link-toggle:hover .uk-link-text {
    color: @link-text-hover-color;
    .hook-link-text-hover();
}


/* Heading
 ========================================================================== */

a.uk-link-heading,
.uk-link-heading a,
.uk-link-toggle .uk-link-heading {
    color: inherit;
    .hook-link-heading();
}

a.uk-link-heading:hover,
.uk-link-heading a:hover,
.uk-link-toggle:hover .uk-link-heading {
    color: @link-heading-hover-color;
    text-decoration: @link-heading-hover-text-decoration;
    .hook-link-heading-hover();
}


/* Reset
 ========================================================================== */

/*
 * `!important` needed to override inverse component
 */

a.uk-link-reset,
.uk-link-reset a {
    color: inherit !important;
    text-decoration: none !important;
    .hook-link-reset();
}


/* Toggle
 ========================================================================== */

.uk-link-toggle {
    color: inherit !important;
    text-decoration: none !important;
}


// Hooks
// ========================================================================

.hook-link-misc();

.hook-link-muted() {}
.hook-link-muted-hover() {}
.hook-link-text() {}
.hook-link-text-hover() {}
.hook-link-heading() {}
.hook-link-heading-hover() {}
.hook-link-reset() {}
.hook-link-misc() {}


// Inverse
// ========================================================================

@inverse-link-muted-color:                        @inverse-global-muted-color;
@inverse-link-muted-hover-color:                  @inverse-global-color;
@inverse-link-text-hover-color:                   @inverse-global-muted-color;
@inverse-link-heading-hover-color:                @inverse-global-primary-background;

.hook-inverse() {

    a.uk-link-muted,
    .uk-link-muted a {
        color: @inverse-link-muted-color;
        .hook-inverse-link-muted();
    }

    a.uk-link-muted:hover,
    .uk-link-muted a:hover,
    .uk-link-toggle:hover .uk-link-muted {
        color: @inverse-link-muted-hover-color;
        .hook-inverse-link-muted-hover();
    }

    a.uk-link-text:hover,
    .uk-link-text a:hover,
    .uk-link-toggle:hover .uk-link-text {
        color: @inverse-link-text-hover-color;
        .hook-inverse-link-text-hover();
    }

    a.uk-link-heading:hover,
    .uk-link-heading a:hover,
    .uk-link-toggle:hover .uk-link-heading {
        color: @inverse-link-heading-hover-color;
        .hook-inverse-link-heading-hover();
    }

}

.hook-inverse-link-muted() {}
.hook-inverse-link-muted-hover() {}
.hook-inverse-link-text-hover() {}
.hook-inverse-link-heading-hover() {}
