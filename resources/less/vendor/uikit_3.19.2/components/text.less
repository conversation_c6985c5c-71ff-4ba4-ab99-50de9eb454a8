// Name:            Text
// Description:     Utilities for text
//
// Component:       `uk-text-*`
//
// ========================================================================


// Variables
// ========================================================================

@text-lead-font-size:                           @global-large-font-size;
@text-lead-line-height:                         1.5;
@text-lead-color:                               @global-emphasis-color;

@text-meta-font-size:                           @global-small-font-size;
@text-meta-line-height:                         1.4;
@text-meta-color:                               @global-muted-color;

@text-small-font-size:                          @global-small-font-size;
@text-small-line-height:                        1.5;

@text-large-font-size:                          @global-large-font-size;
@text-large-line-height:                        1.5;

@text-muted-color:                              @global-muted-color;
@text-emphasis-color:                           @global-emphasis-color;
@text-primary-color:                            @global-primary-background;
@text-secondary-color:                          @global-secondary-background;
@text-success-color:                            @global-success-background;
@text-warning-color:                            @global-warning-background;
@text-danger-color:                             @global-danger-background;

@text-background-color:                         @global-primary-background;


/* ========================================================================
   Component: Text
 ========================================================================== */


/* Style modifiers
 ========================================================================== */

.uk-text-lead {
    font-size: @text-lead-font-size;
    line-height: @text-lead-line-height;
    color: @text-lead-color;
    .hook-text-lead();
}

.uk-text-meta {
    font-size: @text-meta-font-size;
    line-height: @text-meta-line-height;
    color: @text-meta-color;
    .hook-text-meta();
}


/* Size modifiers
 ========================================================================== */

.uk-text-small {
    font-size: @text-small-font-size;
    line-height: @text-small-line-height;
    .hook-text-small();
}

.uk-text-large {
    font-size: @text-large-font-size;
    line-height: @text-large-line-height;
    .hook-text-large();
}

.uk-text-default {
    font-size: @global-font-size;
    line-height: @global-line-height;
}


/* Weight modifier
 ========================================================================== */

.uk-text-light { font-weight: 300; }
.uk-text-normal { font-weight: 400; }
.uk-text-bold { font-weight: 700; }

.uk-text-lighter { font-weight: lighter; }
.uk-text-bolder { font-weight: bolder; }


/* Style modifier
 ========================================================================== */

.uk-text-italic { font-style: italic; }


/* Transform modifier
 ========================================================================== */

.uk-text-capitalize { text-transform: capitalize !important; }
.uk-text-uppercase { text-transform: uppercase !important; }
.uk-text-lowercase { text-transform: lowercase !important; }


/* Decoration modifier
 ========================================================================== */

.uk-text-decoration-none { text-decoration: none !important; }


/* Color modifiers
 ========================================================================== */

.uk-text-muted { color: @text-muted-color !important; }
.uk-text-emphasis { color: @text-emphasis-color !important; }
.uk-text-primary { color: @text-primary-color !important; }
.uk-text-secondary { color: @text-secondary-color !important; }
.uk-text-success { color: @text-success-color !important; }
.uk-text-warning { color: @text-warning-color !important; }
.uk-text-danger { color: @text-danger-color !important; }


/* Background modifier
 ========================================================================== */

/*
 * 1. The background clips to the foreground text. Works in all browsers.
 * 2. Default color is set to transparent.
 * 3. Container fits the text
 * 4. Style
 */

.uk-text-background {
    /* 1 */
    -webkit-background-clip: text;
    /* 2 */
    color: transparent !important;
    /* 3 */
    display: inline-block;
    /* 4 */
    background-color: @text-background-color;
    .hook-text-background();
}


/* Alignment modifiers
 ========================================================================== */

.uk-text-left { text-align: left !important; }
.uk-text-right { text-align: right !important; }
.uk-text-center { text-align: center !important; }
.uk-text-justify { text-align: justify !important; }

/* Phone landscape and bigger */
@media (min-width: @breakpoint-small) {

    .uk-text-left\@s { text-align: left !important; }
    .uk-text-right\@s { text-align: right !important; }
    .uk-text-center\@s { text-align: center !important; }

}

/* Tablet landscape and bigger */
@media (min-width: @breakpoint-medium) {

    .uk-text-left\@m { text-align: left !important; }
    .uk-text-right\@m { text-align: right !important; }
    .uk-text-center\@m { text-align: center !important; }

}

/* Desktop and bigger */
@media (min-width: @breakpoint-large) {

    .uk-text-left\@l { text-align: left !important; }
    .uk-text-right\@l { text-align: right !important; }
    .uk-text-center\@l { text-align: center !important; }

}

/* Large screen and bigger */
@media (min-width: @breakpoint-xlarge) {

    .uk-text-left\@xl { text-align: left !important; }
    .uk-text-right\@xl { text-align: right !important; }
    .uk-text-center\@xl { text-align: center !important; }

}

/*
 * Vertical
 */

.uk-text-top { vertical-align: top !important; }
.uk-text-middle { vertical-align: middle !important; }
.uk-text-bottom { vertical-align: bottom !important; }
.uk-text-baseline { vertical-align: baseline !important; }


/* Wrap modifiers
 ========================================================================== */

/*
 * Prevent text from wrapping onto multiple lines
 */

.uk-text-nowrap { white-space: nowrap; }

/*
 * 1. Make sure a max-width is set after which truncation can occur
 * 2. Prevent text from wrapping onto multiple lines, and truncate with an ellipsis
 * 3. Fix for table cells
 */

.uk-text-truncate {
    /* 1 */
    max-width: 100%;
    /* 2 */
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* 2 */
th.uk-text-truncate,
td.uk-text-truncate { max-width: 0; }

/*
 * Wrap long words onto the next line and break them if they are too long to fit.
 * 1. Make it work with table cells in all browsers.
 * Note: Not using `hyphens: auto` because it hyphenates text even if not needed.
 */

 .uk-text-break { overflow-wrap: break-word; }

 /* 1 */
 th.uk-text-break,
 td.uk-text-break { word-break: break-word; }


// Hooks
// ========================================================================

.hook-text-misc();

.hook-text-lead() {}
.hook-text-meta() {}
.hook-text-small() {}
.hook-text-large() {}
.hook-text-background() {}
.hook-text-misc() {}


// Inverse
// ========================================================================

@inverse-text-lead-color:                      @inverse-global-color;
@inverse-text-meta-color:                      @inverse-global-muted-color;
@inverse-text-muted-color:                     @inverse-global-muted-color;
@inverse-text-emphasis-color:                  @inverse-global-emphasis-color;
@inverse-text-primary-color:                   @inverse-global-primary-background;
@inverse-text-secondary-color:                 @inverse-global-primary-background;

.hook-inverse() {

    .uk-text-lead {
        color: @inverse-text-lead-color;
        .hook-inverse-text-lead();
    }

    .uk-text-meta {
        color: @inverse-text-meta-color;
        .hook-inverse-text-meta();
    }

    .uk-text-muted { color: @inverse-text-muted-color !important; }
    .uk-text-emphasis { color: @inverse-text-emphasis-color !important; }
    .uk-text-primary { color: @inverse-text-primary-color !important; }
    .uk-text-secondary { color: @inverse-text-secondary-color !important; }

}

.hook-inverse-text-lead() {}
.hook-inverse-text-meta() {}
