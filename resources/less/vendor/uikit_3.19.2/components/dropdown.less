// Name:            Dropdown
// Description:     Component to create a dropdown based on Drop component
//
// Component:       `uk-dropdown`
//
// Adopted:         `uk-dropdown-nav`
//
// Modifiers:       `uk-dropdown-large`
//
// States:          `uk-active`
//
// ========================================================================


// Variables
// ========================================================================

@dropdown-margin:                               @global-small-margin;
@dropdown-viewport-margin:                      15px;
@dropdown-min-width:                            200px;
@dropdown-padding:                              15px;
@dropdown-background:                           @global-muted-background;
@dropdown-color:                                @global-color;
@dropdown-color-mode:                           dark;
@dropdown-focus-outline:                        @base-focus-outline;

@dropdown-large-padding:                        40px;

@dropdown-dropbar-padding-top:                  @dropdown-padding;
@dropdown-dropbar-padding-bottom:               @dropdown-padding;
@dropdown-dropbar-viewport-margin:              15px;
@dropdown-dropbar-viewport-margin-s:            @global-gutter;
@dropdown-dropbar-viewport-margin-m:            @global-medium-gutter;

@dropdown-dropbar-large-padding-top:            @dropdown-large-padding;
@dropdown-dropbar-large-padding-bottom:         @dropdown-dropbar-large-padding-top;

@dropdown-nav-item-color:                       @global-muted-color;
@dropdown-nav-item-hover-color:                 @global-color;
@dropdown-nav-subtitle-font-size:               @global-small-font-size;
@dropdown-nav-header-color:                     @global-emphasis-color;
@dropdown-nav-divider-border-width:             @global-border-width;
@dropdown-nav-divider-border:                   @global-border;
@dropdown-nav-sublist-item-color:               @global-muted-color;
@dropdown-nav-sublist-item-hover-color:         @global-color;


/* ========================================================================
   Component: Dropdown
 ========================================================================== */

/*
 * Adopts `uk-drop`
 * 1. Reset drop and let text expand the width instead of wrapping
 * 2. Set a default width
 * 3. Style
 */

.uk-dropdown {
    --uk-position-offset: @dropdown-margin;
    --uk-position-viewport-offset: @dropdown-viewport-margin;
    /* 1 */
    width: auto;
    /* 2 */
    min-width: @dropdown-min-width;
    /* 3 */
    padding: @dropdown-padding;
    background: @dropdown-background;
    color: @dropdown-color;
    .hook-dropdown();
}

/*
 * Remove margin from the last-child
 */

.uk-dropdown > :last-child { margin-bottom: 0; }

// Color Mode
.uk-dropdown:extend(.uk-light all) when (@dropdown-color-mode = light) {}
.uk-dropdown:extend(.uk-dark all) when (@dropdown-color-mode = dark) {}

.uk-dropdown :focus-visible when not (@dropdown-color-mode = @inverse-global-color-mode) {
    outline-color: @dropdown-focus-outline !important;
}


/* Size modifier
 ========================================================================== */

.uk-dropdown-large { padding: @dropdown-large-padding; }


/* Dropbar modifier
 ========================================================================== */

/*
 * 1. Reset dropdown width to prevent to early shifting
 * 2. Reset style
 * 3. Padding
 */

.uk-dropdown-dropbar {
    /* 1 */
    width: auto;
    /* 2 */
    background: transparent;
    /* 3 */
    padding: @dropdown-dropbar-padding-top 0 @dropdown-dropbar-padding-bottom 0;
    --uk-position-viewport-offset: @dropdown-dropbar-viewport-margin;
    .hook-dropdown-dropbar();
}

/* Phone landscape and bigger */
@media (min-width: @breakpoint-small) {

    .uk-dropdown-dropbar { --uk-position-viewport-offset: @dropdown-dropbar-viewport-margin-s; }

}

/* Tablet landscape and bigger */
@media (min-width: @breakpoint-medium) {

    .uk-dropdown-dropbar { --uk-position-viewport-offset: @dropdown-dropbar-viewport-margin-m; }

}

.uk-dropdown-dropbar-large {
    padding-top: @dropdown-dropbar-large-padding-top;
    padding-bottom: @dropdown-dropbar-large-padding-bottom;
    .hook-dropdown-dropbar-large();
}


/* Nav
 * Adopts `uk-nav`
 ========================================================================== */

.uk-dropdown-nav {
    .hook-dropdown-nav();
}

/*
 * Items
 */

.uk-dropdown-nav > li > a {
    color: @dropdown-nav-item-color;
    .hook-dropdown-nav-item();
}

/* Hover + Active */
.uk-dropdown-nav > li > a:hover,
.uk-dropdown-nav > li.uk-active > a {
    color: @dropdown-nav-item-hover-color;
    .hook-dropdown-nav-item-hover();
}

/*
 * Subtitle
 */

.uk-dropdown-nav .uk-nav-subtitle {
    font-size: @dropdown-nav-subtitle-font-size;
    .hook-dropdown-nav-subtitle();
}

/*
 * Header
 */

.uk-dropdown-nav .uk-nav-header {
    color: @dropdown-nav-header-color;
    .hook-dropdown-nav-header();
}

/*
 * Divider
 */

.uk-dropdown-nav .uk-nav-divider {
    border-top: @dropdown-nav-divider-border-width solid @dropdown-nav-divider-border;
    .hook-dropdown-nav-divider();
}

/*
 * Sublists
 */

.uk-dropdown-nav .uk-nav-sub a { color: @dropdown-nav-sublist-item-color; }

.uk-dropdown-nav .uk-nav-sub a:hover,
.uk-dropdown-nav .uk-nav-sub li.uk-active > a { color: @dropdown-nav-sublist-item-hover-color; }


// Hooks
// ========================================================================

.hook-dropdown-misc();

.hook-dropdown() {}
.hook-dropdown-dropbar() {}
.hook-dropdown-dropbar-large() {}
.hook-dropdown-nav() {}
.hook-dropdown-nav-item() {}
.hook-dropdown-nav-item-hover() {}
.hook-dropdown-nav-subtitle() {}
.hook-dropdown-nav-header() {}
.hook-dropdown-nav-divider() {}
.hook-dropdown-misc() {}
