// Name:            Section
// Description:     Component to create horizontal layout section
//
// Component:       `uk-section`
//
// Modifiers:       `uk-section-xsmall`
//                  `uk-section-small`
//                  `uk-section-large`
//                  `uk-section-xlarge`
//                  `uk-section-default`
//                  `uk-section-muted`
//                  `uk-section-primary`
//                  `uk-section-secondary`
//                  `uk-section-overlap`
//
// States:          `uk-preserve-color`
//
// ========================================================================


// Variables
// ========================================================================

@section-padding-vertical:                        @global-medium-margin;
@section-padding-vertical-m:                      @global-large-margin;

@section-xsmall-padding-vertical:                 @global-margin;

@section-small-padding-vertical:                  @global-medium-margin;

@section-large-padding-vertical:                  @global-large-margin;
@section-large-padding-vertical-m:                @global-xlarge-margin;

@section-xlarge-padding-vertical:                 @global-xlarge-margin;
@section-xlarge-padding-vertical-m:               (@global-large-margin + @global-xlarge-margin);

@section-default-background:                      @global-background;
@section-default-color-mode:                      dark;

@section-muted-background:                        @global-muted-background;
@section-muted-color-mode:                        dark;

@section-primary-background:                      @global-primary-background;
@section-primary-color-mode:                      light;

@section-secondary-background:                    @global-secondary-background;
@section-secondary-color-mode:                    light;


/* ========================================================================
   Component: Section
 ========================================================================== */

/*
 * 1. Make it work with `100vh` and height in general
 */

.uk-section {
    display: flow-root;
    box-sizing: border-box; /* 1 */
    padding-top: @section-padding-vertical;
    padding-bottom: @section-padding-vertical;
    .hook-section();
}

/* Desktop and bigger */
@media (min-width: @breakpoint-medium) {

    .uk-section {
        padding-top: @section-padding-vertical-m;
        padding-bottom: @section-padding-vertical-m;
    }

}

/*
 * Remove margin from the last-child
 */

.uk-section > :last-child { margin-bottom: 0; }


/* Size modifiers
 ========================================================================== */

/*
 * XSmall
 */

.uk-section-xsmall {
    padding-top: @section-xsmall-padding-vertical;
    padding-bottom: @section-xsmall-padding-vertical;
}

/*
 * Small
 */

.uk-section-small {
    padding-top: @section-small-padding-vertical;
    padding-bottom: @section-small-padding-vertical;
}

/*
 * Large
 */

.uk-section-large {
    padding-top: @section-large-padding-vertical;
    padding-bottom: @section-large-padding-vertical;
}

/* Tablet landscape and bigger */
@media (min-width: @breakpoint-medium) {

    .uk-section-large {
        padding-top: @section-large-padding-vertical-m;
        padding-bottom: @section-large-padding-vertical-m;
    }

}

/*
 * XLarge
 */

.uk-section-xlarge {
    padding-top: @section-xlarge-padding-vertical;
    padding-bottom: @section-xlarge-padding-vertical;
}

/* Tablet landscape and bigger */
@media (min-width: @breakpoint-medium) {

    .uk-section-xlarge {
        padding-top: @section-xlarge-padding-vertical-m;
        padding-bottom: @section-xlarge-padding-vertical-m;
    }

}


/* Style modifiers
 ========================================================================== */

/*
 * Default
 */

.uk-section-default {
    background: @section-default-background;
    .hook-section-default();
}

.uk-section-default:not(.uk-preserve-color):extend(.uk-light all) when (@section-default-color-mode = light) {}
.uk-section-default:not(.uk-preserve-color):extend(.uk-dark all) when (@section-default-color-mode = dark) {}

/*
 * Muted
 */

.uk-section-muted {
    background: @section-muted-background;
    .hook-section-muted();
}

.uk-section-muted:not(.uk-preserve-color):extend(.uk-light all) when (@section-muted-color-mode = light) {}
.uk-section-muted:not(.uk-preserve-color):extend(.uk-dark all) when (@section-muted-color-mode = dark) {}

/*
 * Primary
 */

.uk-section-primary {
    background: @section-primary-background;
    .hook-section-primary();
}

.uk-section-primary:not(.uk-preserve-color):extend(.uk-light all) when (@section-primary-color-mode = light) {}
.uk-section-primary:not(.uk-preserve-color):extend(.uk-dark all) when (@section-primary-color-mode = dark) {}

/*
 * Secondary
 */

.uk-section-secondary {
    background: @section-secondary-background;
    .hook-section-secondary();
}

.uk-section-secondary:not(.uk-preserve-color):extend(.uk-light all) when (@section-secondary-color-mode = light) {}
.uk-section-secondary:not(.uk-preserve-color):extend(.uk-dark all) when (@section-secondary-color-mode = dark) {}


/* Overlap modifier
 ========================================================================== */

/*
 * Reserved modifier to make a section overlap another section with an border image
 * Implemented by the theme
 */

.uk-section-overlap {
    .hook-section-overlap();
}


// Hooks
// ========================================================================

.hook-section-misc();

.hook-section() {}
.hook-section-default() {}
.hook-section-muted() {}
.hook-section-secondary() {}
.hook-section-primary() {}
.hook-section-overlap() {}
.hook-section-misc() {}
