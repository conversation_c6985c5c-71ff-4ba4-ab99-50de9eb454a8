// Name:            Alert
// Description:     Component to create alert messages
//
// Component:       `uk-alert`
//
// Adopted:         `uk-alert-close`
//
// Modifiers:       `uk-alert-primary`
//                  `uk-alert-success`
//                  `uk-alert-warning`
//                  `uk-alert-danger`
//
// ========================================================================


// Variables
// ========================================================================

@alert-margin-vertical:                         @global-margin;
@alert-padding:                                 15px;
@alert-padding-right:                           @alert-padding + 14px;
@alert-background:                              @global-muted-background;
@alert-color:                                   @global-color;

@alert-close-top:                               @alert-padding + 5px;
@alert-close-right:                             @alert-padding;

@alert-primary-background:                      lighten(tint(@global-primary-background, 40%), 20%);
@alert-primary-color:                           @global-primary-background;

@alert-success-background:                      lighten(tint(@global-success-background, 40%), 25%);
@alert-success-color:                           @global-success-background;

@alert-warning-background:                      lighten(tint(@global-warning-background, 45%), 15%);
@alert-warning-color:                           @global-warning-background;

@alert-danger-background:                       lighten(tint(@global-danger-background, 40%), 20%);
@alert-danger-color:                            @global-danger-background;


/* ========================================================================
   Component: Alert
 ========================================================================== */

.uk-alert {
    position: relative;
    margin-bottom: @alert-margin-vertical;
    padding: @alert-padding @alert-padding-right @alert-padding @alert-padding;
    background: @alert-background;
    color: @alert-color;
    .hook-alert();
}

/* Add margin if adjacent element */
* + .uk-alert { margin-top: @alert-margin-vertical; }

/*
 * Remove margin from the last-child
 */

.uk-alert > :last-child { margin-bottom: 0; }


/* Close
 * Adopts `uk-close`
 ========================================================================== */

.uk-alert-close {
    position: absolute;
    top: @alert-close-top;
    right: @alert-close-right;
    .hook-alert-close();
}

/*
 * Remove margin from adjacent element
 */

.uk-alert-close:first-child + * { margin-top: 0; }

/*
 * Hover
 */

.uk-alert-close:hover {
    .hook-alert-close-hover();
}


/* Style modifiers
 ========================================================================== */

/*
 * Primary
 */

.uk-alert-primary {
    background: @alert-primary-background;
    color: @alert-primary-color;
    .hook-alert-primary();
}

/*
 * Success
 */

.uk-alert-success {
    background: @alert-success-background;
    color: @alert-success-color;
    .hook-alert-success();
}

/*
 * Warning
 */

.uk-alert-warning {
    background: @alert-warning-background;
    color: @alert-warning-color;
    .hook-alert-warning();
}

/*
 * Danger
 */

.uk-alert-danger {
    background: @alert-danger-background;
    color: @alert-danger-color;
    .hook-alert-danger();
}


// Hooks
// ========================================================================

.hook-alert-misc();

.hook-alert() {}
.hook-alert-close() {}
.hook-alert-close-hover() {}
.hook-alert-primary() {}
.hook-alert-success() {}
.hook-alert-warning() {}
.hook-alert-danger() {}
.hook-alert-misc() {}
