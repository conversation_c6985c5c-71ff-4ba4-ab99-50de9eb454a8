// Name:            List
// Description:     Styles for lists
//
// Component:       `uk-list`
//
// Modifiers:       `uk-list-disc`
//                  `uk-list-circle`
//                  `uk-list-square`
//                  `uk-list-decimal`
//                  `uk-list-hyphen`
//                  `uk-list-muted`
//                  `uk-list-emphasis`
//                  `uk-list-primary`
//                  `uk-list-secondary`
//                  `uk-list-bullet`
//                  `uk-list-divider`
//                  `uk-list-striped`
//                  `uk-list-large`
//                  `uk-list-collapse`
//
// ========================================================================


// Variables
// ========================================================================

@list-margin-top:                               @global-small-margin;

@list-padding-left:                             30px;

@list-marker-height:                            (@global-line-height * 1em);

@list-muted-color:                              @global-muted-color;
@list-emphasis-color:                           @global-emphasis-color;
@list-primary-color:                            @global-primary-background;
@list-secondary-color:                          @global-secondary-background;

@list-bullet-icon-color:                        @global-color;

@list-divider-margin-top:                       @global-small-margin;
@list-divider-border-width:                     @global-border-width;
@list-divider-border:                           @global-border;

@list-striped-padding-vertical:                 @global-small-margin;
@list-striped-padding-horizontal:               @global-small-margin;
@list-striped-background:                       @global-muted-background;

@list-large-margin-top:                         @global-margin;
@list-large-divider-margin-top:                 @global-margin;
@list-large-striped-padding-vertical:           @global-margin;
@list-large-striped-padding-horizontal:         @global-small-margin;

@internal-list-bullet-image:                    "../../resources/images/backgrounds/list-bullet.svg";


/* ========================================================================
   Component: List
 ========================================================================== */

.uk-list {
    padding: 0;
    list-style: none;
}

/*
 * Avoid column break within the list item, when using `column-count`
 */

.uk-list > * { break-inside: avoid-column; }

/*
 * Remove margin from the last-child
 */

.uk-list > * > :last-child { margin-bottom: 0; }

/*
 * Style
 */

.uk-list > :nth-child(n+2),
.uk-list > * > ul { margin-top: @list-margin-top; }


/* Marker modifiers
 * Moving `::marker` inside `::before` to style it differently
 * To style the `::marker` is currently only supported in Firefox and Safari
 ========================================================================== */

.uk-list-disc > *,
.uk-list-circle > *,
.uk-list-square > *,
.uk-list-decimal > *,
.uk-list-hyphen > * { padding-left: @list-padding-left; }

/*
 * Type modifiers
 */

.uk-list-decimal { counter-reset: decimal; }
.uk-list-decimal > * { counter-increment: decimal; }

.uk-list-disc > ::before,
.uk-list-circle > ::before,
.uk-list-square > ::before,
.uk-list-decimal > ::before,
.uk-list-hyphen > ::before {
    content: "";
    position: relative;
    left: -@list-padding-left;
    width: @list-padding-left;
    height: @list-marker-height;
    margin-bottom: -@list-marker-height;
    display: list-item;
    list-style-position: inside;
    text-align: right;
}

.uk-list-disc > ::before { list-style-type: disc; }
.uk-list-circle > ::before { list-style-type: circle; }
.uk-list-square > ::before { list-style-type: square; }
.uk-list-decimal > ::before { content: counter(decimal, decimal) '\200A.\00A0'; }
.uk-list-hyphen > ::before { content: '–\00A0\00A0'; }

/*
 * Color modifiers
 */

.uk-list-muted > ::before { color: @list-muted-color !important; }
.uk-list-emphasis > ::before { color: @list-emphasis-color !important; }
.uk-list-primary > ::before { color: @list-primary-color !important; }
.uk-list-secondary > ::before { color: @list-secondary-color !important; }


/* Image bullet modifier
 ========================================================================== */

.uk-list-bullet > * { padding-left: @list-padding-left; }

.uk-list-bullet > ::before {
    content: "";
    display: list-item;
    position: relative;
    left: -@list-padding-left;
    width: @list-padding-left;
    height: @list-marker-height;
    margin-bottom: -@list-marker-height;
    .svg-fill(@internal-list-bullet-image, "#000", @list-bullet-icon-color);
    background-repeat: no-repeat;
    background-position: 50% 50%;
}


/* Style modifiers
 ========================================================================== */

/*
 * Divider
 */

.uk-list-divider > :nth-child(n+2) {
    margin-top: @list-divider-margin-top;
    padding-top: @list-divider-margin-top;
    border-top: @list-divider-border-width solid @list-divider-border;
    .hook-list-divider();
}

/*
 * Striped
 */

.uk-list-striped > * {
    padding: @list-striped-padding-vertical @list-striped-padding-horizontal;
    .hook-list-striped();
}

.uk-list-striped > :nth-of-type(odd) { background: @list-striped-background; }

.uk-list-striped > :nth-child(n+2) { margin-top: 0; }


/* Size modifier
 ========================================================================== */

.uk-list-large > :nth-child(n+2),
.uk-list-large > * > ul { margin-top: @list-large-margin-top; }

.uk-list-collapse > :nth-child(n+2),
.uk-list-collapse > * > ul { margin-top: 0; }

/*
 * Divider
 */

.uk-list-large.uk-list-divider > :nth-child(n+2) {
    margin-top: @list-large-divider-margin-top;
    padding-top: @list-large-divider-margin-top;
}

.uk-list-collapse.uk-list-divider > :nth-child(n+2) {
    margin-top: 0;
    padding-top: 0;
}

/*
 * Striped
 */

.uk-list-large.uk-list-striped > * { padding: @list-large-striped-padding-vertical @list-large-striped-padding-horizontal; }

.uk-list-collapse.uk-list-striped > * {
    padding-top: 0;
    padding-bottom: 0;
}

.uk-list-large.uk-list-striped > :nth-child(n+2),
.uk-list-collapse.uk-list-striped > :nth-child(n+2) { margin-top: 0; }


// Hooks
// ========================================================================

.hook-list-misc();

.hook-list-divider() {}
.hook-list-striped() {}
.hook-list-misc() {}


// Inverse
// ========================================================================

@inverse-list-muted-color:                        @inverse-global-muted-color;
@inverse-list-emphasis-color:                     @inverse-global-emphasis-color;
@inverse-list-primary-color:                      @inverse-global-primary-background;
@inverse-list-secondary-color:                    @inverse-global-primary-background;

@inverse-list-divider-border:                     @inverse-global-border;
@inverse-list-striped-background:                 @inverse-global-muted-background;

@inverse-list-bullet-icon-color:                  @inverse-global-color;

.hook-inverse() {

    .uk-list-muted > ::before { color: @inverse-list-muted-color !important; }
    .uk-list-emphasis > ::before { color: @inverse-list-emphasis-color !important; }
    .uk-list-primary > ::before { color: @inverse-list-primary-color !important; }
    .uk-list-secondary > ::before { color: @inverse-list-secondary-color !important; }

    .uk-list-bullet > ::before {
        .svg-fill(@internal-list-bullet-image, "#000", @inverse-list-bullet-icon-color);
    }

    .uk-list-divider > :nth-child(n+2) {
        border-top-color: @inverse-list-divider-border;
        .hook-inverse-list-divider();
    }

    .uk-list-striped > * {
        .hook-inverse-list-striped();
    }

    .uk-list-striped > :nth-of-type(odd) { background-color: @inverse-list-striped-background; }

}

.hook-inverse-list-divider() {}
.hook-inverse-list-striped() {}
