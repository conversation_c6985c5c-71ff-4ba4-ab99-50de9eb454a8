// Name:            Cover
// Description:     Utilities to let embedded content cover their container in a centered position
//
// Component:       `uk-cover`
//
// Sub-object:      `uk-cover-container`
//
// ========================================================================


/* ========================================================================
   Component: Cover
 ========================================================================== */

/*
 * Works with iframes and embedded content
 * 1. Use attribute to apply transform instantly. Needed if transform is transitioned.
 * 2. Reset responsiveness for embedded content
 * 3. Center object
 * Note: Percent values on the `top` property only works if this element
 *       is absolute positioned or if the container has a height
 */

/* 1 */
[uk-cover]:where(canvas, iframe, svg),
[data-uk-cover]:where(canvas, iframe, svg) {
    /* 2 */
    max-width: none;
    /* 3 */
    position: absolute;
    left: 50%;
    top: 50%;
    --uk-position-translate-x: -50%;
    --uk-position-translate-y: -50%;
    transform: translate(var(--uk-position-translate-x), var(--uk-position-translate-y));
}

iframe[uk-cover],
iframe[data-uk-cover] { pointer-events: none; }

[uk-cover]:where(img, video),
[data-uk-cover]:where(img, video) {
    /* 3 */
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    box-sizing: border-box;
    object-fit: cover;
    object-position: center;
}


/* Container
 ========================================================================== */

/*
 * 1. Parent container which clips resized object
 * 2. Needed if the child is positioned absolute. See note above
 */

.uk-cover-container {
    /* 1 */
    overflow: hidden;
    /* 2 */
    position: relative;
}


// Hooks
// ========================================================================

.hook-cover-misc();

.hook-cover-misc() {}
