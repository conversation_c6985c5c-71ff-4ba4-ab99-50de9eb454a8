// Name:            Heading
// Description:     Styles for headings
//
// Component:       `uk-heading-primary`
//                  `uk-heading-hero`
//                  `uk-heading-divider`
//                  `uk-heading-bullet`
//                  `uk-heading-line`
//
// ========================================================================


// Variables
// ========================================================================

@heading-small-font-size:                       @heading-small-font-size-m * 0.8;       // 38px 0.73
@heading-medium-font-size:                      @heading-medium-font-size-m * 0.825;    // 40px 0.714
@heading-large-font-size:                       @heading-large-font-size-m * 0.85;      // 50px 0.78
@heading-xlarge-font-size:                      @heading-large-font-size-m;             // 4rem / 64px
@heading-2xlarge-font-size:                     @heading-xlarge-font-size-m;            // 6rem / 96px
@heading-3xlarge-font-size:                     @heading-2xlarge-font-size-m;           // 8rem / 128px

@heading-small-font-size-m:                     @heading-medium-font-size-l * 0.8125;   // 3.25rem / 52px
@heading-medium-font-size-m:                    @heading-medium-font-size-l * 0.875;    // 3.5rem / 56px
@heading-large-font-size-m:                     @heading-medium-font-size-l;            // 4rem / 64px
@heading-xlarge-font-size-m:                    @heading-large-font-size-l;             // 6rem / 96px
@heading-2xlarge-font-size-m:                   @heading-xlarge-font-size-l;            // 8rem / 128px
@heading-3xlarge-font-size-m:                   @heading-2xlarge-font-size-l;           // 11rem / 176px

@heading-medium-font-size-l:                    4rem;                                   // 64px
@heading-large-font-size-l:                     6rem;                                   // 96px
@heading-xlarge-font-size-l:                    8rem;                                   // 128px
@heading-2xlarge-font-size-l:                   11rem;                                  // 176px
@heading-3xlarge-font-size-l:                   15rem;                                  // 240px

@heading-small-line-height:                     1.2;
@heading-medium-line-height:                    1.1;
@heading-large-line-height:                     1.1;
@heading-xlarge-line-height:                    1;
@heading-2xlarge-line-height:                   1;
@heading-3xlarge-line-height:                   1;

@heading-divider-padding-bottom:                ~'calc(5px + 0.1em)';
@heading-divider-border-width:                  ~'calc(0.2px + 0.05em)';
@heading-divider-border:                        @global-border;

@heading-bullet-top:                            ~'calc(-0.1 * 1em)';
@heading-bullet-height:                         ~'calc(4px + 0.7em)';
@heading-bullet-margin-right:                   ~'calc(5px + 0.2em)';
@heading-bullet-border-width:                   ~'calc(5px + 0.1em)';
@heading-bullet-border:                         @global-border;

@heading-line-top:                              50%;
@heading-line-height:                           @heading-line-border-width;
@heading-line-width:                            2000px;
@heading-line-border-width:                     ~'calc(0.2px + 0.05em)';
@heading-line-border:                           @global-border;
@heading-line-margin-horizontal:                ~'calc(5px + 0.3em)';


/* ========================================================================
   Component: Heading
 ========================================================================== */

.uk-heading-small {
    font-size: @heading-small-font-size;
    line-height: @heading-small-line-height;
    .hook-heading-small();
}

.uk-heading-medium {
    font-size: @heading-medium-font-size;
    line-height: @heading-medium-line-height;
    .hook-heading-medium();
}

.uk-heading-large {
    font-size: @heading-large-font-size;
    line-height: @heading-large-line-height;
    .hook-heading-large();
}

.uk-heading-xlarge {
    font-size: @heading-xlarge-font-size;
    line-height: @heading-xlarge-line-height;
    .hook-heading-xlarge();
}

.uk-heading-2xlarge {
    font-size: @heading-2xlarge-font-size;
    line-height: @heading-2xlarge-line-height;
    .hook-heading-2xlarge();
}

.uk-heading-3xlarge {
    font-size: @heading-3xlarge-font-size;
    line-height: @heading-3xlarge-line-height;
    .hook-heading-3xlarge();
}

/* Tablet Landscape and bigger */
@media (min-width: @breakpoint-medium) {

    .uk-heading-small { font-size: @heading-small-font-size-m; }
    .uk-heading-medium { font-size: @heading-medium-font-size-m; }
    .uk-heading-large { font-size: @heading-large-font-size-m; }
    .uk-heading-xlarge { font-size: @heading-xlarge-font-size-m; }
    .uk-heading-2xlarge { font-size: @heading-2xlarge-font-size-m; }
    .uk-heading-3xlarge { font-size: @heading-3xlarge-font-size-m; }

}

/* Laptop and bigger */
@media (min-width: @breakpoint-large) {

    .uk-heading-medium { font-size: @heading-medium-font-size-l; }
    .uk-heading-large { font-size: @heading-large-font-size-l; }
    .uk-heading-xlarge { font-size: @heading-xlarge-font-size-l; }
    .uk-heading-2xlarge { font-size: @heading-2xlarge-font-size-l; }
    .uk-heading-3xlarge { font-size: @heading-3xlarge-font-size-l; }

}


/* Primary
   Deprecated: Use `uk-heading-medium` instead
 ========================================================================== */

@heading-primary-font-size-l:                   3.75rem;  // 60px
@heading-primary-line-height-l:                 1.1;

@heading-primary-font-size-m:                   @heading-primary-font-size-l * 0.9; // 54px

@heading-primary-font-size:                     @heading-primary-font-size-l * 0.8; // 48px
@heading-primary-line-height:                   1.2;

.uk-heading-primary when (@deprecated = true) {
    font-size: @heading-primary-font-size;
    line-height: @heading-primary-line-height;
    .hook-heading-primary();
}

/* Tablet landscape and bigger */
@media (min-width: @breakpoint-medium) {

    .uk-heading-primary when (@deprecated = true) { font-size: @heading-primary-font-size-m; }

}

/* Desktop and bigger */
@media (min-width: @breakpoint-large) {

    .uk-heading-primary when (@deprecated = true) {
        font-size: @heading-primary-font-size-l;
        line-height: @heading-primary-line-height-l;
    }

}


/* Hero
   Deprecated: Use `uk-heading-xlarge` instead
 ========================================================================== */

@heading-hero-font-size-l:                      8rem; // 128px
@heading-hero-line-height-l:                    1;

@heading-hero-font-size-m:                      @heading-hero-font-size-l * 0.75; // 96px
@heading-hero-line-height-m:                    1;

@heading-hero-font-size:                        @heading-hero-font-size-l * 0.5; // 64px
@heading-hero-line-height:                      1.1;

.uk-heading-hero when (@deprecated = true) {
    font-size: @heading-hero-font-size;
    line-height: @heading-hero-line-height;
    .hook-heading-hero();
}

/* Tablet landscape and bigger */
@media (min-width: @breakpoint-medium) {

    .uk-heading-hero when (@deprecated = true) {
        font-size: @heading-hero-font-size-m;
        line-height: @heading-hero-line-height-m;
    }

}

/* Desktop and bigger */
@media (min-width: @breakpoint-large) {

    .uk-heading-hero when (@deprecated = true) {
        font-size: @heading-hero-font-size-l;
        line-height: @heading-hero-line-height-l;
    }

}


/* Divider
 ========================================================================== */

.uk-heading-divider {
    padding-bottom: @heading-divider-padding-bottom;
    border-bottom: @heading-divider-border-width solid @heading-divider-border;
    .hook-heading-divider();
}


/* Bullet
 ========================================================================== */

.uk-heading-bullet { position: relative; }

/*
 * 1. Using `inline-block` to make it work with text alignment
 * 2. Center vertically
 * 3. Style
 */

.uk-heading-bullet::before {
    content: "";
    /* 1 */
    display: inline-block;
    /* 2 */
    position: relative;
    top: @heading-bullet-top;
    vertical-align: middle;
    /* 3 */
    height: @heading-bullet-height;
    margin-right: @heading-bullet-margin-right;
    border-left: @heading-bullet-border-width solid @heading-bullet-border;
    .hook-heading-bullet();
}


/* Line
 ========================================================================== */

/*
 * Clip the child element
 */

.uk-heading-line { overflow: hidden; }

/*
 * Extra markup is needed to make it work with text align
 */

.uk-heading-line > * {
    display: inline-block;
    position: relative;
}

/*
 * 1. Center vertically
 * 2. Make the element as large as possible. It's clipped by the container.
 * 3. Style
 */

.uk-heading-line > ::before,
.uk-heading-line > ::after {
    content: "";
    /* 1 */
    position: absolute;
    top: ~'calc(@{heading-line-top} - (@{heading-line-height} / 2))';
    /* 2 */
    width: @heading-line-width;
    /* 3 */
    border-bottom: @heading-line-border-width solid @heading-line-border;
    .hook-heading-line();
}

.uk-heading-line > ::before {
    right: 100%;
    margin-right: @heading-line-margin-horizontal;
}
.uk-heading-line > ::after {
    left: 100%;
    margin-left: @heading-line-margin-horizontal;
}


// Hooks
// ========================================================================

.hook-heading-misc();

.hook-heading-small() {}
.hook-heading-medium() {}
.hook-heading-large() {}
.hook-heading-xlarge() {}
.hook-heading-2xlarge() {}
.hook-heading-3xlarge() {}
.hook-heading-primary() {}
.hook-heading-hero() {}
.hook-heading-divider() {}
.hook-heading-bullet() {}
.hook-heading-line() {}
.hook-heading-misc() {}


// Inverse
// ========================================================================

@inverse-heading-divider-border:                  @inverse-global-border;
@inverse-heading-bullet-border:                   @inverse-global-border;
@inverse-heading-line-border:                     @inverse-global-border;

.hook-inverse() {

    .uk-heading-small {
        .hook-inverse-heading-small();
    }

    .uk-heading-medium {
        .hook-inverse-heading-medium();
    }

    .uk-heading-large {
        .hook-inverse-heading-large();
    }

    .uk-heading-xlarge {
        .hook-inverse-heading-xlarge();
    }

    .uk-heading-2xlarge {
        .hook-inverse-heading-2xlarge();
    }

    .uk-heading-3xlarge {
        .hook-inverse-heading-3xlarge();
    }

    .uk-heading-primary when (@deprecated = true) {
        .hook-inverse-heading-primary();
    }

    .uk-heading-hero when (@deprecated = true) {
        .hook-inverse-heading-hero();
    }

    .uk-heading-divider {
        border-bottom-color: @inverse-heading-divider-border;
        .hook-inverse-heading-divider();
    }

    .uk-heading-bullet::before {
        border-left-color: @inverse-heading-bullet-border;
        .hook-inverse-heading-bullet();
    }

    .uk-heading-line > ::before,
    .uk-heading-line > ::after {
        border-bottom-color: @inverse-heading-line-border;
        .hook-inverse-heading-line();
    }

}

.hook-inverse-heading-small() {}
.hook-inverse-heading-medium() {}
.hook-inverse-heading-large() {}
.hook-inverse-heading-xlarge() {}
.hook-inverse-heading-2xlarge() {}
.hook-inverse-heading-3xlarge() {}
.hook-inverse-heading-primary() {}
.hook-inverse-heading-hero() {}
.hook-inverse-heading-divider() {}
.hook-inverse-heading-bullet() {}
.hook-inverse-heading-line() {}
