// Name:            Iconnav
// Description:     Component to create icon navigations
//
// Component:       `uk-iconnav`
//
// Modifier:        `uk-iconnav-vertical`
//
// States:          `uk-active`
//
// ========================================================================


// Variables
// ========================================================================

@iconnav-margin-horizontal:                      @global-small-margin;
@iconnav-margin-vertical:                        @iconnav-margin-horizontal;

@iconnav-item-color:                             @global-muted-color;

@iconnav-item-hover-color:                       @global-color;

@iconnav-item-active-color:                      @global-color;


/* ========================================================================
   Component: Iconnav
 ========================================================================== */

/*
 * 1. Allow items to wrap into the next line
 * 2. Reset list
 * 3. Gutter
 */

.uk-iconnav {
    display: flex;
    /* 1 */
    flex-wrap: wrap;
    /* 2 */
    margin: 0;
    padding: 0;
    list-style: none;
    /* 3 */
    margin-left: -@iconnav-margin-horizontal;
    .hook-iconnav();
}

/*
 * Space is allocated based on content dimensions, but shrinks: 0 1 auto
 * 1. Gutter
 */

.uk-iconnav > * {
    /* 1 */
    padding-left: @iconnav-margin-horizontal;
}


/* Items
 ========================================================================== */

/*
 * Items must target `a` elements to exclude other elements (e.g. dropdowns)
 * 1. Center content vertically if there is still some text
 * 2. Imitate white space gap when using flexbox
 * 3. Force text not to affect item height
 * 4. Style
 * 5. Required for `a` if there is still some text
 */

.uk-iconnav > * > a {
    /* 1 */
    display: flex;
    align-items: center;
    /* 2 */
    column-gap: 0.25em;
    /* 3 */
    line-height: 0;
    /* 4 */
    color: @iconnav-item-color;
    /* 5 */
    text-decoration: none;
    .hook-iconnav-item();
}

/* Hover */
.uk-iconnav > * > a:hover {
    color: @iconnav-item-hover-color;
    .hook-iconnav-item-hover();
}

/* Active */
.uk-iconnav > .uk-active > a {
    color: @iconnav-item-active-color;
    .hook-iconnav-item-active();
}


/* Modifier: 'uk-iconnav-vertical'
 ========================================================================== */

/*
 * 1. Change direction
 * 2. Gutter
 */

.uk-iconnav-vertical {
    /* 1 */
    flex-direction: column;
    /* 2 */
    margin-left: 0;
    margin-top: -@iconnav-margin-vertical;
}

/* 2 */
.uk-iconnav-vertical > * {
    padding-left: 0;
    padding-top: @iconnav-margin-vertical;
}


// Hooks
// ========================================================================

.hook-iconnav-misc();

.hook-iconnav() {}
.hook-iconnav-item() {}
.hook-iconnav-item-hover() {}
.hook-iconnav-item-active() {}
.hook-iconnav-misc() {}


// Inverse
// ========================================================================

@inverse-iconnav-item-color:               @inverse-global-muted-color;
@inverse-iconnav-item-hover-color:         @inverse-global-color;
@inverse-iconnav-item-active-color:        @inverse-global-color;

.hook-inverse() {

    .uk-iconnav > * > a {
        color: @inverse-iconnav-item-color;
        .hook-inverse-iconnav-item();
    }

    .uk-iconnav > * > a:hover {
        color: @inverse-iconnav-item-hover-color;
        .hook-inverse-iconnav-item-hover();
    }

    .uk-iconnav > .uk-active > a {
        color: @inverse-iconnav-item-active-color;
        .hook-inverse-iconnav-item-active();
    }

}

.hook-inverse-iconnav-item() {}
.hook-inverse-iconnav-item-hover() {}
.hook-inverse-iconnav-item-active() {}
