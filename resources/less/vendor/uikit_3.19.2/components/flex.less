// Name:            Flex
// Description:     Utilities for layouts based on flexbox
//
// Component:       `uk-flex-*`
//
// ========================================================================


/* ========================================================================
   Component: Flex
 ========================================================================== */

.uk-flex { display: flex; }
.uk-flex-inline { display: inline-flex; }


/* Alignment
 ========================================================================== */

/*
 * Align items along the main axis of the current line of the flex container
 * Row: Horizontal
 */

// Default
.uk-flex-left { justify-content: flex-start; }
.uk-flex-center { justify-content: center; }
.uk-flex-right { justify-content: flex-end; }
.uk-flex-between { justify-content: space-between; }
.uk-flex-around { justify-content: space-around; }

/* Phone landscape and bigger */
@media (min-width: @breakpoint-small) {

    .uk-flex-left\@s { justify-content: flex-start; }
    .uk-flex-center\@s { justify-content: center; }
    .uk-flex-right\@s { justify-content: flex-end; }
    .uk-flex-between\@s { justify-content: space-between; }
    .uk-flex-around\@s { justify-content: space-around; }

}

/* Tablet landscape and bigger */
@media (min-width: @breakpoint-medium) {

    .uk-flex-left\@m { justify-content: flex-start; }
    .uk-flex-center\@m { justify-content: center; }
    .uk-flex-right\@m { justify-content: flex-end; }
    .uk-flex-between\@m { justify-content: space-between; }
    .uk-flex-around\@m { justify-content: space-around; }

}

/* Desktop and bigger */
@media (min-width: @breakpoint-large) {

    .uk-flex-left\@l { justify-content: flex-start; }
    .uk-flex-center\@l { justify-content: center; }
    .uk-flex-right\@l { justify-content: flex-end; }
    .uk-flex-between\@l { justify-content: space-between; }
    .uk-flex-around\@l { justify-content: space-around; }

}

/* Large screen and bigger */
@media (min-width: @breakpoint-xlarge) {

    .uk-flex-left\@xl { justify-content: flex-start; }
    .uk-flex-center\@xl { justify-content: center; }
    .uk-flex-right\@xl { justify-content: flex-end; }
    .uk-flex-between\@xl { justify-content: space-between; }
    .uk-flex-around\@xl { justify-content: space-around; }

}

/*
 * Align items in the cross axis of the current line of the flex container
 * Row: Vertical
 */

// Default
.uk-flex-stretch { align-items: stretch; }
.uk-flex-top { align-items: flex-start; }
.uk-flex-middle { align-items: center; }
.uk-flex-bottom { align-items: flex-end; }

/* Phone landscape and bigger */
@media (min-width: @breakpoint-small) {

    .uk-flex-stretch\@s { align-items: stretch; }
    .uk-flex-top\@s { align-items: flex-start; }
    .uk-flex-middle\@s { align-items: center; }
    .uk-flex-bottom\@s { align-items: flex-end; }

}

/* Tablet landscape and bigger */
@media (min-width: @breakpoint-medium) {

    .uk-flex-stretch\@m { align-items: stretch; }
    .uk-flex-top\@m { align-items: flex-start; }
    .uk-flex-middle\@m { align-items: center; }
    .uk-flex-bottom\@m { align-items: flex-end; }

}

/* Desktop and bigger */
@media (min-width: @breakpoint-large) {

    .uk-flex-stretch\@l { align-items: stretch; }
    .uk-flex-top\@l { align-items: flex-start; }
    .uk-flex-middle\@l { align-items: center; }
    .uk-flex-bottom\@l { align-items: flex-end; }

}

/* Large screen and bigger */
@media (min-width: @breakpoint-xlarge) {

    .uk-flex-stretch\@xl { align-items: stretch; }
    .uk-flex-top\@xl { align-items: flex-start; }
    .uk-flex-middle\@xl { align-items: center; }
    .uk-flex-bottom\@xl { align-items: flex-end; }

}


/* Direction
 ========================================================================== */

// Default
.uk-flex-row { flex-direction: row; }
.uk-flex-row-reverse { flex-direction: row-reverse; }
.uk-flex-column { flex-direction: column; }
.uk-flex-column-reverse { flex-direction: column-reverse; }

/* Phone landscape and bigger */
@media (min-width: @breakpoint-small) {

    .uk-flex-row\@s { flex-direction: row; }
    .uk-flex-column\@s { flex-direction: column; }

}

/* Tablet landscape and bigger */
@media (min-width: @breakpoint-medium) {

    .uk-flex-row\@m { flex-direction: row; }
    .uk-flex-column\@m { flex-direction: column; }

}

/* Desktop and bigger */
@media (min-width: @breakpoint-large) {

    .uk-flex-row\@l { flex-direction: row; }
    .uk-flex-column\@l { flex-direction: column; }

}

/* Large screen and bigger */
@media (min-width: @breakpoint-xlarge) {

    .uk-flex-row\@xl { flex-direction: row; }
    .uk-flex-column\@xl { flex-direction: column; }

}


/* Wrap
 ========================================================================== */

// Default
.uk-flex-nowrap { flex-wrap: nowrap; }
.uk-flex-wrap { flex-wrap: wrap; }
.uk-flex-wrap-reverse { flex-wrap: wrap-reverse; }

/*
 * Aligns items within the flex container when there is extra space in the cross-axis
 * Only works if there is more than one line of flex items
 */

// Default
.uk-flex-wrap-stretch { align-content: stretch; }
.uk-flex-wrap-top { align-content: flex-start; }
.uk-flex-wrap-middle { align-content: center; }
.uk-flex-wrap-bottom { align-content: flex-end; }
.uk-flex-wrap-between { align-content: space-between; }
.uk-flex-wrap-around { align-content: space-around; }


/* Item ordering
 ========================================================================== */

/*
 * Default is 0
 */

.uk-flex-first { order: -1;}
.uk-flex-last { order: 99;}

/* Phone landscape and bigger */
@media (min-width: @breakpoint-small) {

    .uk-flex-first\@s { order: -1; }
    .uk-flex-last\@s { order: 99; }

}

/* Tablet landscape and bigger */
@media (min-width: @breakpoint-medium) {

    .uk-flex-first\@m { order: -1; }
    .uk-flex-last\@m { order: 99; }

}

/* Desktop and bigger */
@media (min-width: @breakpoint-large) {

    .uk-flex-first\@l { order: -1; }
    .uk-flex-last\@l { order: 99; }

}

/* Large screen and bigger */
@media (min-width: @breakpoint-xlarge) {

    .uk-flex-first\@xl { order: -1; }
    .uk-flex-last\@xl { order: 99; }

}


/* Item dimensions
 ========================================================================== */

/*
 * Initial: 0 1 auto
 * Content dimensions, but shrinks
 */

.uk-flex-initial { flex: initial; }

/*
 * No Flex: 0 0 auto
 * Content dimensions
 */

.uk-flex-none { flex: none; }

/*
 * Relative Flex: 1 1 auto
 * Space is allocated considering content
 */

.uk-flex-auto { flex: auto; }

/*
 * Absolute Flex: 1 1 0%
 * Space is allocated solely based on flex
 */

.uk-flex-1 { flex: 1; }

/* Phone landscape and bigger */
@media (min-width: @breakpoint-small) {

    .uk-flex-initial\@s { flex: initial; }
    .uk-flex-none\@s { flex: none; }
    .uk-flex-1\@s { flex: 1; }

}

/* Tablet landscape and bigger */
@media (min-width: @breakpoint-medium) {

    .uk-flex-initial\@m { flex: initial; }
    .uk-flex-none\@m { flex: none; }
    .uk-flex-1\@m { flex: 1; }

}

/* Desktop and bigger */
@media (min-width: @breakpoint-large) {

    .uk-flex-initial\@l { flex: initial; }
    .uk-flex-none\@l { flex: none; }
    .uk-flex-1\@l { flex: 1; }

}

/* Large screen and bigger */
@media (min-width: @breakpoint-xlarge) {

    .uk-flex-initial\@xl { flex: initial; }
    .uk-flex-none\@xl { flex: none; }
    .uk-flex-1\@xl { flex: 1; }

}


// Hooks
// ========================================================================

.hook-flex-misc();

.hook-flex-misc() {}
