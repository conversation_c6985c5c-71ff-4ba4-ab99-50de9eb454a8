// Name:            Position
// Description:     Utilities to position content
//
// Component:       `uk-position-absolute`
//                  `uk-position-relative`
//                  `uk-position-z-index`
//                  `uk-position-top`
//                  `uk-position-bottom`
//                  `uk-position-left`
//                  `uk-position-right`
//                  `uk-position-top-left`
//                  `uk-position-top-center`
//                  `uk-position-top-right`
//                  `uk-position-bottom-left`
//                  `uk-position-bottom-center`
//                  `uk-position-bottom-right`
//                  `uk-position-center`
//                  `uk-position-center-left`
//                  `uk-position-center-right`
//                  `uk-position-cover`
//
// Modifiers:       `uk-position-small`
//                  `uk-position-medium`
//                  `uk-position-large`
//
// ========================================================================


// Variables
// ========================================================================

@position-small-margin:                                @global-small-gutter;
@position-medium-margin:                               @global-gutter;
@position-large-margin:                                @global-gutter;
@position-large-margin-l:                              50px;


/* ========================================================================
   Component: Position
 ========================================================================== */

:root { --uk-position-margin-offset: 0px; }


/* Directions
 ========================================================================== */

/*
 * 1. Prevent content overflow.
 */

[class*="uk-position-top"],
[class*="uk-position-bottom"],
[class*="uk-position-left"],
[class*="uk-position-right"],
[class*="uk-position-center"] {
    position: absolute !important;
    /* 1 */
    max-width: ~'calc(100% - (var(--uk-position-margin-offset) * 2))';
    box-sizing: border-box;
}

/*
 * Edges
 * Don't use `width: 100%` because it's wrong if the parent has padding.
 */

.uk-position-top {
    top: 0;
    left: 0;
    right: 0;
}

.uk-position-bottom {
    bottom: 0;
    left: 0;
    right: 0;
}

.uk-position-left {
    top: 0;
    bottom: 0;
    left: 0;
}

.uk-position-right {
    top: 0;
    bottom: 0;
    right: 0;
}

/*
 * Corners
 */

.uk-position-top-left {
    top: 0;
    left: 0;
}

.uk-position-top-right {
    top: 0;
    right: 0;
}

.uk-position-bottom-left {
    bottom: 0;
    left: 0;
}

.uk-position-bottom-right {
    bottom: 0;
    right: 0;
}

/*
 * Center
 * 1. Fix text wrapping if content is larger than 50% of the container.
 *    Using `max-content` requires `max-width` of 100% which is set generally.
 */

.uk-position-center {
    top: ~'calc(50% - var(--uk-position-margin-offset))';
    left: ~'calc(50% - var(--uk-position-margin-offset))';
    --uk-position-translate-x: -50%;
    --uk-position-translate-y: -50%;
    transform: translate(var(--uk-position-translate-x), var(--uk-position-translate-y));
    /* 1 */
    width: max-content;
}

/* Vertical */
[class*="uk-position-center-left"],
[class*="uk-position-center-right"],
.uk-position-center-vertical {
    top: ~'calc(50% - var(--uk-position-margin-offset))';
    --uk-position-translate-y: -50%;
    transform: translate(0, var(--uk-position-translate-y));
}

.uk-position-center-left { left: 0; }
.uk-position-center-right { right: 0; }
.uk-position-center-vertical {
    left: 0;
    right: 0;
}

.uk-position-center-left-out {
    right: 100%;
    width: max-content;
}

.uk-position-center-right-out {
    left: 100%;
    width: max-content;
}

/* Horizontal */
.uk-position-top-center,
.uk-position-bottom-center,
.uk-position-center-horizontal {
    left: ~'calc(50% - var(--uk-position-margin-offset))';
    --uk-position-translate-x: -50%;
    transform: translate(var(--uk-position-translate-x), 0);
    /* 1 */
    width: max-content;
}

.uk-position-top-center { top: 0; }
.uk-position-bottom-center { bottom: 0; }
.uk-position-center-horizontal {
    top: 0;
    bottom: 0;
}

/*
 * Cover
 */

.uk-position-cover {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
}


/* Margin
 ========================================================================== */

.uk-position-small {
    margin: @position-small-margin;
    --uk-position-margin-offset: @position-small-margin;
}

.uk-position-medium {
    margin: @position-medium-margin;
    --uk-position-margin-offset: @position-medium-margin;
}

.uk-position-large {
    margin: @position-large-margin;
    --uk-position-margin-offset: @position-large-margin;
}

/* Desktop and bigger */
@media (min-width: @breakpoint-large) {

    .uk-position-large {
        margin: @position-large-margin-l;
        --uk-position-margin-offset: @position-large-margin-l;
    }

}


/* Schemes
 ========================================================================== */

.uk-position-relative { position: relative  !important; }
.uk-position-absolute { position: absolute  !important; }
.uk-position-fixed { position: fixed  !important; }
.uk-position-sticky { position: sticky  !important; }


/* Layer
 ========================================================================== */

.uk-position-z-index { z-index: 1; }
.uk-position-z-index-zero { z-index: 0; }
.uk-position-z-index-negative { z-index: -1; }
.uk-position-z-index-high { z-index: @global-z-index - 10; } // Higher than dropbar so it is behind


// Hooks
// ========================================================================

.hook-position-misc();

.hook-position-misc() {}
