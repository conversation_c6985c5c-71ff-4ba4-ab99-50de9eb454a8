// Name:            Dotnav
// Description:     Component to create dot navigations
//
// Component:       `uk-dotnav`
//
// Modifier:        `uk-dotnav-vertical`
//
// States:          `uk-active`
//
// ========================================================================


// Variables
// ========================================================================

@dotnav-margin-horizontal:                      12px;
@dotnav-margin-vertical:                        @dotnav-margin-horizontal;

@dotnav-item-width:                             10px;
@dotnav-item-height:                            @dotnav-item-width;
@dotnav-item-border-radius:                     50%;

@dotnav-item-background:                        fade(@global-color, 20%);
@dotnav-item-hover-background:                  fade(@global-color, 60%);
@dotnav-item-onclick-background:                fade(@global-color, 20%);
@dotnav-item-active-background:                 fade(@global-color, 60%);


/* ========================================================================
   Component: Dotnav
 ========================================================================== */

/*
 * 1. Allow items to wrap into the next line
 * 2. Reset list
 * 3. Gutter
 */

.uk-dotnav {
    display: flex;
    /* 1 */
    flex-wrap: wrap;
    /* 2 */
    margin: 0;
    padding: 0;
    list-style: none;
    /* 3 */
    margin-left: -@dotnav-margin-horizontal;
    .hook-dotnav();
}

/*
 * 1. Space is allocated solely based on content dimensions: 0 0 auto
 * 2. Gutter
 */

.uk-dotnav > * {
    /* 1 */
    flex: none;
    /* 2 */
    padding-left: @dotnav-margin-horizontal;
}


/* Items
 ========================================================================== */

/*
 * Items
 * 1. Hide text if present
 */

.uk-dotnav > * > * {
    display: block;
    box-sizing: border-box;
    width: @dotnav-item-width;
    height: @dotnav-item-height;
    border-radius: @dotnav-item-border-radius;
    background: @dotnav-item-background;
    /* 1 */
    text-indent: 100%;
    overflow: hidden;
    white-space: nowrap;
    .hook-dotnav-item();
}

/* Hover */
.uk-dotnav > * > :hover {
    background-color: @dotnav-item-hover-background;
    .hook-dotnav-item-hover();
}

/* OnClick */
.uk-dotnav > * > :active {
    background-color: @dotnav-item-onclick-background;
    .hook-dotnav-item-onclick();
}

/* Active */
.uk-dotnav > .uk-active > * {
    background-color: @dotnav-item-active-background;
    .hook-dotnav-item-active();
}


/* Modifier: 'uk-dotnav-vertical'
 ========================================================================== */

/*
 * 1. Change direction
 * 2. Gutter
 */

.uk-dotnav-vertical {
    /* 1 */
    flex-direction: column;
    /* 2 */
    margin-left: 0;
    margin-top: -@dotnav-margin-vertical;
}

/* 2 */
.uk-dotnav-vertical > * {
    padding-left: 0;
    padding-top: @dotnav-margin-vertical;
}


// Hooks
// ========================================================================

.hook-dotnav-misc();

.hook-dotnav() {}
.hook-dotnav-item() {}
.hook-dotnav-item-hover() {}
.hook-dotnav-item-onclick() {}
.hook-dotnav-item-active() {}
.hook-dotnav-misc() {}


// Inverse
// ========================================================================

@inverse-dotnav-item-background:               fade(@inverse-global-color, 50%);
@inverse-dotnav-item-hover-background:         fade(@inverse-global-color, 90%);
@inverse-dotnav-item-onclick-background:       fade(@inverse-global-color, 50%);
@inverse-dotnav-item-active-background:        fade(@inverse-global-color, 90%);

.hook-inverse() {

    .uk-dotnav > * > * {
        background-color: @inverse-dotnav-item-background;
        .hook-inverse-dotnav-item();
    }

    .uk-dotnav > * > :hover {
        background-color: @inverse-dotnav-item-hover-background;
        .hook-inverse-dotnav-item-hover();
    }

    .uk-dotnav > * > :active {
        background-color: @inverse-dotnav-item-onclick-background;
        .hook-inverse-dotnav-item-onclick();
    }

    .uk-dotnav > .uk-active > * {
        background-color: @inverse-dotnav-item-active-background;
        .hook-inverse-dotnav-item-active();
    }

}

.hook-inverse-dotnav-item() {}
.hook-inverse-dotnav-item-hover() {}
.hook-inverse-dotnav-item-onclick() {}
.hook-inverse-dotnav-item-active() {}
