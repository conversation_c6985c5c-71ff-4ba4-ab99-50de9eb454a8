// Name:            Tile
// Description:     Component to create tiled boxes
//
// Component:       `uk-tile`
//
// Modifiers:       `uk-tile-xsmall`
//                  `uk-tile-small`
//                  `uk-tile-large`
//                  `uk-tile-xlarge`
//                  `uk-tile-default`
//                  `uk-tile-muted`
//                  `uk-tile-primary`
//                  `uk-tile-secondary`
//
// States:          `uk-preserve-color`
//
// ========================================================================


// Variables
// ========================================================================

@tile-padding-horizontal:                      15px;
@tile-padding-horizontal-s:                    @global-gutter;
@tile-padding-horizontal-m:                    @global-medium-gutter;
@tile-padding-vertical:                        @global-medium-margin;
@tile-padding-vertical-m:                      @global-large-margin;

@tile-xsmall-padding-vertical:                 @global-margin;

@tile-small-padding-vertical:                  @global-medium-margin;

@tile-large-padding-vertical:                  @global-large-margin;
@tile-large-padding-vertical-m:                @global-xlarge-margin;

@tile-xlarge-padding-vertical:                 @global-xlarge-margin;
@tile-xlarge-padding-vertical-m:               (@global-large-margin + @global-xlarge-margin);

@tile-default-background:                      @global-background;
@tile-default-color-mode:                      dark;

@tile-muted-background:                        @global-muted-background;
@tile-muted-color-mode:                        dark;

@tile-primary-background:                      @global-primary-background;
@tile-primary-color-mode:                      light;

@tile-secondary-background:                    @global-secondary-background;
@tile-secondary-color-mode:                    light;


/* ========================================================================
   Component: Tile
 ========================================================================== */

.uk-tile {
    display: flow-root;
    position: relative;
    box-sizing: border-box;
    padding-left: @tile-padding-horizontal;
    padding-right: @tile-padding-horizontal;
    padding-top: @tile-padding-vertical;
    padding-bottom: @tile-padding-vertical;
    .hook-tile();
}

/* Phone landscape and bigger */
@media (min-width: @breakpoint-small) {

    .uk-tile {
        padding-left: @tile-padding-horizontal-s;
        padding-right: @tile-padding-horizontal-s;
    }

}

/* Tablet landscape and bigger */
@media (min-width: @breakpoint-medium) {

    .uk-tile {
        padding-left: @tile-padding-horizontal-m;
        padding-right: @tile-padding-horizontal-m;
        padding-top: @tile-padding-vertical-m;
        padding-bottom: @tile-padding-vertical-m;
    }

}

/*
 * Remove margin from the last-child
 */

.uk-tile > :last-child { margin-bottom: 0; }


/* Size modifiers
 ========================================================================== */

/*
 * XSmall
 */

.uk-tile-xsmall {
    padding-top: @tile-xsmall-padding-vertical;
    padding-bottom: @tile-xsmall-padding-vertical;
}

/*
 * Small
 */

.uk-tile-small {
    padding-top: @tile-small-padding-vertical;
    padding-bottom: @tile-small-padding-vertical;
}

/*
 * Large
 */

.uk-tile-large {
    padding-top: @tile-large-padding-vertical;
    padding-bottom: @tile-large-padding-vertical;
}

/* Tablet landscape and bigger */
@media (min-width: @breakpoint-medium) {

    .uk-tile-large {
        padding-top: @tile-large-padding-vertical-m;
        padding-bottom: @tile-large-padding-vertical-m;
    }

}


/*
 * XLarge
 */

.uk-tile-xlarge {
    padding-top: @tile-xlarge-padding-vertical;
    padding-bottom: @tile-xlarge-padding-vertical;
}

/* Tablet landscape and bigger */
@media (min-width: @breakpoint-medium) {

    .uk-tile-xlarge {
        padding-top: @tile-xlarge-padding-vertical-m;
        padding-bottom: @tile-xlarge-padding-vertical-m;
    }

}


/* Style modifiers
 ========================================================================== */

/*
 * Default
 */

.uk-tile-default {
    background-color: @tile-default-background;
    .hook-tile-default();
}

.uk-tile-default.uk-tile-hover:hover {
    .hook-tile-default-hover();
}

// Color Mode
.uk-tile-default:not(.uk-preserve-color):extend(.uk-light all) when (@tile-default-color-mode = light) {}
.uk-tile-default:not(.uk-preserve-color):extend(.uk-dark all) when (@tile-default-color-mode = dark) {}

/*
 * Muted
 */

.uk-tile-muted {
    background-color: @tile-muted-background;
    .hook-tile-muted();
}

.uk-tile-muted.uk-tile-hover:hover {
    .hook-tile-muted-hover();
}

// Color Mode
.uk-tile-muted:not(.uk-preserve-color):extend(.uk-light all) when (@tile-muted-color-mode = light) {}
.uk-tile-muted:not(.uk-preserve-color):extend(.uk-dark all) when (@tile-muted-color-mode = dark) {}

/*
 * Primary
 */

.uk-tile-primary {
    background-color: @tile-primary-background;
    .hook-tile-primary();
}

.uk-tile-primary.uk-tile-hover:hover {
    .hook-tile-primary-hover();
}

// Color Mode
.uk-tile-primary:not(.uk-preserve-color):extend(.uk-light all) when (@tile-primary-color-mode = light) {}
.uk-tile-primary:not(.uk-preserve-color):extend(.uk-dark all) when (@tile-primary-color-mode = dark) {}

/*
 * Secondary
 */

.uk-tile-secondary {
    background-color: @tile-secondary-background;
    .hook-tile-secondary();
}

.uk-tile-secondary.uk-tile-hover:hover {
    .hook-tile-secondary-hover();
}

// Color Mode
.uk-tile-secondary:not(.uk-preserve-color):extend(.uk-light all) when (@tile-secondary-color-mode = light) {}
.uk-tile-secondary:not(.uk-preserve-color):extend(.uk-dark all) when (@tile-secondary-color-mode = dark) {}


// Hooks
// ========================================================================

.hook-tile-misc();

.hook-tile() {}
.hook-tile-default() {}
.hook-tile-default-hover() {}
.hook-tile-muted() {}
.hook-tile-muted-hover() {}
.hook-tile-primary() {}
.hook-tile-primary-hover() {}
.hook-tile-secondary() {}
.hook-tile-secondary-hover() {}
.hook-tile-misc() {}
