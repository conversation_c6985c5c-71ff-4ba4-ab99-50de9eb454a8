// Name:            Modal
// Description:     Component to create modal dialogs
//
// Component:       `uk-modal`
//
// Sub-objects:     `uk-modal-page`
//                  `uk-modal-dialog`
//                  `uk-modal-header`
//                  `uk-modal-body`
//                  `uk-modal-footer`
//                  `uk-modal-title`
//                  `uk-modal-close`
//
// Adopted:         `uk-modal-close-default`
//                  `uk-modal-close-outside`
//                  `uk-modal-close-full`
//
// Modifiers:       `uk-modal-container`
//                  `uk-modal-full`
//
// States:          `uk-open`
//
// ========================================================================


// Variables
// ========================================================================

@modal-z-index:                                 @global-z-index + 10;
@modal-background:                              rgba(0,0,0,0.6);

@modal-padding-horizontal:                      15px;
@modal-padding-horizontal-s:                    @global-gutter;
@modal-padding-horizontal-m:                    @global-medium-gutter;
@modal-padding-vertical:                        @modal-padding-horizontal;
@modal-padding-vertical-s:                      50px;

@modal-dialog-width:                            600px;
@modal-dialog-background:                       @global-background;

@modal-container-width:                         1200px;

@modal-body-padding-horizontal:                 20px;
@modal-body-padding-vertical:                   20px;
@modal-body-padding-horizontal-s:               @global-gutter;
@modal-body-padding-vertical-s:                 @global-gutter;

@modal-header-padding-horizontal:               20px;
@modal-header-padding-vertical:                 (@modal-header-padding-horizontal / 2);
@modal-header-padding-horizontal-s:             @global-gutter;
@modal-header-padding-vertical-s:               (@modal-header-padding-horizontal-s / 2);
@modal-header-background:                       @global-muted-background;

@modal-footer-padding-horizontal:               20px;
@modal-footer-padding-vertical:                 (@modal-footer-padding-horizontal / 2);
@modal-footer-padding-horizontal-s:             @global-gutter;
@modal-footer-padding-vertical-s:               (@modal-footer-padding-horizontal-s / 2);
@modal-footer-background:                       @global-muted-background;

@modal-title-font-size:                         @global-xlarge-font-size;
@modal-title-line-height:                       1.3;

@modal-close-position:                          @global-small-margin;
@modal-close-padding:                           5px;

@modal-close-outside-position:                  0;
@modal-close-outside-translate:                 100%;
@modal-close-outside-color:                     lighten(@global-inverse-color, 20%);
@modal-close-outside-hover-color:               @global-inverse-color;


/* ========================================================================
   Component: Modal
 ========================================================================== */

/*
 * 1. Hide by default
 * 2. Set position
 * 3. Allow scrolling for the modal dialog
 * 4. Horizontal padding
 * 5. Mask the background page
 * 6. Fade-in transition
 */

.uk-modal {
    /* 1 */
    display: none;
    /* 2 */
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: @modal-z-index;
    /* 3 */
    overflow-y: auto;
    /* 4 */
    padding: @modal-padding-vertical @modal-padding-horizontal;
    /* 5 */
    background: @modal-background;
    /* 6 */
    opacity: 0;
    transition: opacity 0.15s linear;
    .hook-modal();
}

/* Phone landscape and bigger */
@media (min-width: @breakpoint-small) {

    .uk-modal { padding: @modal-padding-vertical-s @modal-padding-horizontal-s; }

}

/* Tablet landscape and bigger */
@media (min-width: @breakpoint-medium) {

    .uk-modal {
        padding-left: @modal-padding-horizontal-m;
        padding-right: @modal-padding-horizontal-m;
    }

}

/*
 * Open
 */

.uk-modal.uk-open { opacity: 1; }


/* Page
 ========================================================================== */

/*
 * Prevent scrollbars
 */

.uk-modal-page { overflow: hidden; }


/* Dialog
 ========================================================================== */

/*
 * 1. Create position context for spinner and close button
 * 2. Dimensions
 * 3. `!important` is needed to overwrite `uk-width-auto`. See `#modal-media-image` in tests
 * 4. Style
 * 5. Slide-in transition
 */

.uk-modal-dialog {
    /* 1 */
    position: relative;
    /* 2 */
    box-sizing: border-box;
    margin: 0 auto;
    width: @modal-dialog-width;
    /* 3 */
    max-width: 100% !important;
    /* 4 */
    background: @modal-dialog-background;
    /* 5 */
    opacity: 0;
    transform: translateY(-100px);
    transition: 0.3s linear;
    transition-property: opacity, transform;
    .hook-modal-dialog();
}

/*
 * Open
 */

.uk-open > .uk-modal-dialog {
    opacity: 1;
    transform: translateY(0);
}


/* Size modifier
 ========================================================================== */

/*
 * Container size
 * Take the same size as the Container component
 */

.uk-modal-container .uk-modal-dialog { width: @modal-container-width; }

/*
 * Full size
 * 1. Remove padding and background from modal
 * 2. Reset all default declarations from modal dialog
 */

/* 1 */
.uk-modal-full {
    padding: 0;
    background: none;
}

/* 2 */
.uk-modal-full .uk-modal-dialog {
    margin: 0;
    width: 100%;
    max-width: 100%;
    transform: translateY(0);
    .hook-modal-full();
}


/* Sections
 ========================================================================== */

.uk-modal-body {
    display: flow-root;
    padding: @modal-body-padding-vertical @modal-body-padding-horizontal;
    .hook-modal-body();
}

.uk-modal-header {
    display: flow-root;
    padding: @modal-header-padding-vertical @modal-header-padding-horizontal;
    background: @modal-header-background;
    .hook-modal-header();
}

.uk-modal-footer {
    display: flow-root;
    padding: @modal-footer-padding-vertical @modal-footer-padding-horizontal;
    background: @modal-footer-background;
    .hook-modal-footer();
}

/* Phone landscape and bigger */
@media (min-width: @breakpoint-small) {

    .uk-modal-body { padding: @modal-body-padding-vertical-s @modal-body-padding-horizontal-s; }
    .uk-modal-header { padding: @modal-header-padding-vertical-s @modal-header-padding-horizontal-s; }
    .uk-modal-footer { padding: @modal-footer-padding-vertical-s @modal-footer-padding-horizontal-s; }

}

/*
 * Remove margin from the last-child
 */

.uk-modal-body > :last-child,
.uk-modal-header > :last-child,
.uk-modal-footer > :last-child { margin-bottom: 0; }


/* Title
 ========================================================================== */

.uk-modal-title {
    font-size: @modal-title-font-size;
    line-height: @modal-title-line-height;
    .hook-modal-title();
}


/* Close
 * Adopts `uk-close`
 ========================================================================== */

[class*="uk-modal-close-"] {
    position: absolute;
    z-index: @modal-z-index;
    top: @modal-close-position;
    right: @modal-close-position;
    padding: @modal-close-padding;
    .hook-modal-close();
}

/*
 * Remove margin from adjacent element
 */

[class*="uk-modal-close-"]:first-child + * { margin-top: 0; }

/*
 * Hover
 */

[class*="uk-modal-close-"]:hover {
    .hook-modal-close-hover();
}

/*
 * Default
 */

.uk-modal-close-default {
    .hook-modal-close-default();
}

.uk-modal-close-default:hover {
    .hook-modal-close-default-hover();
}

/*
 * Outside
 * 1. Prevent scrollbar on small devices
 */

.uk-modal-close-outside {
    top: @modal-close-outside-position;
    /* 1 */
    right: -@modal-close-padding;
    transform: translate(0, -(@modal-close-outside-translate));
    color: @modal-close-outside-color;
    .hook-modal-close-outside();
}

.uk-modal-close-outside:hover {
    color: @modal-close-outside-hover-color;
    .hook-modal-close-outside-hover();
}

/* Tablet landscape and bigger */
@media (min-width: @breakpoint-medium) {

    /* 1 */
    .uk-modal-close-outside {
        right: @modal-close-outside-position;
        transform: translate(@modal-close-outside-translate, -(@modal-close-outside-translate));
    }

}

/*
 * Full
 */

.uk-modal-close-full {
    .hook-modal-close-full();
}

.uk-modal-close-full:hover {
    .hook-modal-close-full-hover();
}


// Hooks
// ========================================================================

.hook-modal-misc();

.hook-modal() {}
.hook-modal-dialog() {}
.hook-modal-full() {}
.hook-modal-header() {}
.hook-modal-body() {}
.hook-modal-footer() {}
.hook-modal-title() {}
.hook-modal-close() {}
.hook-modal-close-hover() {}
.hook-modal-close-default() {}
.hook-modal-close-default-hover() {}
.hook-modal-close-outside() {}
.hook-modal-close-outside-hover() {}
.hook-modal-close-full() {}
.hook-modal-close-full-hover() {}
.hook-modal-misc() {}
