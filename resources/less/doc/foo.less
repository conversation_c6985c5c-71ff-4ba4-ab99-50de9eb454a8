//
// Foo
// 
// Priklad less souboru v adresari site. Soubory v site neprepisuji UIKit 
// soubory, jsou to nové styly pro konkretni stranku.
//
// ========================================================================


// Variables
// ========================================================================

@foo-something-background:                          @global-background;
@foo-something-color:                               @link-color;
@foo-something-hover-color:                         @link-hover-color;
@foo-something-height:                              @menu-height;
@foo-something-width:                               @container-width / 2;

@foo-something-else-margin-bottom:                  20px;
@foo-something-else-margin-bottom-l:                30px;
@foo-something-else-background:                     #ffaaaa;


// Something
// ========================================================================

.something {
    background: @foo-something-background;
    color: @foo-something-color;
    height: @foo-something-height;
    width: @foo-something-width;
    display: inline-block;
    padding: 0;
    margin: 0;
}

.something:hover {
    color: @foo-something-hover-color;
}

.something ul {
    list-style: none;
    padding: 0;
}


// Something else
// ========================================================================

.something-else {
    margin-bottom: @foo-something-else-margin-bottom;
    background: @foo-something-else-background;
}

/* Desktop and bigger */
@media (min-width: @breakpoint-large) {
    .something-else {
        margin-bottom: @foo-something-else-margin-bottom-l;
    }
}
