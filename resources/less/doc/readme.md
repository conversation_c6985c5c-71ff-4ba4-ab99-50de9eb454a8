Tvorba CSS z UI Kit pomocí Less
===============================

Je špatně vzít celý CSS z UIKitu (např. přes CDN) a všechny styly přetěžovat v dalším CSS pomocí `!important`. Lepší je upravit rovnou generovaný CSS, tzn. přetěžovat less soubory a ne výsledná CSS. Např, komponenta [Button](https://getuikit.com/docs/button):

**Špatně**  

V UIKit css je defaultně `uk-button-primary` barva `#1e87f0`:
```css
.uk-button-primary {
  background-color: #1e87f0;
  color: #fff;
  border: 1px solid transparent;
}
```

<PERSON><PERSON> chci červené tlačítko, takže si vytvořim `style.css` a   
```css
.uk-button-primary {
  background-color: #ff0000 !important;
}
``
Pak mám   
```html
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/uikit@3.15.7/dist/css/uikit.min.css" />
<link rel="stylesheet" href="/css/style.css" /> <!-- ten je pravdepodobně plnej !important a není to nic hezkého a jsou to bajty navíc -->
```

**Správně**

1. kouknu do UIkit less souborů, najdu si `.uk-button-primary` a zjistím, že je barva v proměnné `@button-primary-background`
2. vytvořím si vlastní `button.less` a v něm nadefinuji proměnnou `@button-primary-background: #ff0000`;
3. zkompiluju. Výsledný CSS má:
```css
.uk-button-primary {
  background-color: #ff0000;
  color: #fff;
  border: 1px solid transparent;
}
```
Více viz níže [Tvorba less souborů](#tvorba-souboru).   

Vytvoření vlastního theme
-------------------------
viz [UIkit dokumentace](https://getuikit.com/docs/less#create-a-uikit-theme)  

UIkit dokumentace předpokládá instalaci `yarn`em a úpravu přímo v nainstalovaném uikit adresáři, 
možná se k tomu taky časem dopracujem, ale momentálně to tvoříme trochu jinak:

Stáhneme source soubory z [githubu](https://github.com/uikit/uikit)

### Adresářová struktura

```
less/

    <!-- kompilacni soubory -->
    bin/

    <!-- napoveda (toto readme) a priklady -->
    doc/

    <!-- zde budeme editovat -->
    site/

        <!-- styly pro konkretni stranku -->
        site/
            layout.less
            vymysly-marketingu.less

        <!-- uprava uikitu -->
        uikit_theme/

            <!-- one file per customized component -->
            accordion.less
            alert.less
            …

        _import.less

        paths.less

        variables.less

    <!-- originalni source UIkit soubory - needitovat -->
    vendor/

        <!-- uvadejme verzi -->
        uikit_x.y.z/

            <!-- obsah adresare je totozny, jako obsah adresare /src/less ze stahnuteho zipu z githubu-->
            components/
            theme/
            uikit.less
            uikit.theme.less

    <!-- entry file pro Less compiler -->
    site.less
```

Entry point pro Less compiler, `/less/site.less`:
```less
// Core
@import "vendor/uikit_x.y.z/uikit.less";

// Theme
@import "site/_import.less";
```
> Naimportují se komponenty z vendoru, tam nic neměnit!  
> Naimpoertují se soubory, kde měníme uikit komponenty a tvoříme vlastní styly  


Adresář `less/site` má soubor importující všechny jednotlivé custom less soubory `less/site/_import.less`:
```less
@import "variables.less";
@import "paths.less";

// =====================================
// UI Kit
// =====================================

// Common
@import "uikit_theme/alerts.less";

// JavaScript
@import "uikit_theme/accordion.less";

...

// =====================================
// This site
// =====================================

@import "site/layout.less";
@import "site/vymysly-marketingu.less";

```
> Dodržuj pořadí souborů viz `less/vendor/uikit_x.y.z/components/_import.less`

Shrnuto: ve vendoru nic neměním, veškeré směny v site. V `site/site` mám vlastní styly, v `site/uikit_themes` upravuji uikit komponenty.   
Ještě je třeba zmínit 2 soubory:
- `less/site/variables.less`
- `less/site/paths.less`

`less/site/variables.less`  
Globální proměnné jsou v `less/vendor/uikit_x.y.z/components/variables.less`, podle logiky výše bych měl vytvořit
`less/site/uikit_themes/variables.less`, ale protože chci mít 1 soubor s globálními proměnnými jak pro uikit, tak pro vlastní styly, umístím si ho do `less/site/variables.less`, nemusím ho potom hledat někde v themes a mám ho jako jeden z hlavních souborů.

`less/site/paths.less`   
Zde jsou upraveny cesty k obrázkům. Obrázky ze staženého zipu z gitlabu (`/src/images`) je třeba umístit někam do public adresáře a v `paths.less` tyto cesty upravit.

<a name="tvorba-souboru"></a>
### Tvorba less souborů

#### Obecná pravidla

- Rozměry v pixelech, ne `em`, nebo `rem`, uikit má všechny rozměry v px a i marketing v zadání měří vše na px. Vyhneme se spoustě problémů.
- V media query používat zásadne proměnné `@breakpoint-*` viz `less/vendor/uikit_x.y.z/components/variables.less`

#### `site/site`
Příklad viz `foo.less`  
Soubory v `site` nepřepisují UIKit soubory, jsou to nové styly pro konkrétni stránku.   
Budou tam pravděpodobně styly definující layout, rozměry a vzhled různých prvků dle zadání od marketingu.  

1. Nadpis - vypadá to dobře, když soubor začíná nadpisem
2. Popis - je dobré sdělit kolegům, k čemu tento soubor slouží. Nikoho nezabije těch pár slov tam napsat
3. Variables - na začátku souboru by měly být nadefinované všechny proměnné použity dále v souboru. Název proměné by měl být prefixovaný názvem souboru (komponenty), mezery mezi názvem proměnné a hodnotou odtabuj, ať to vychází na 4 mezery. Proměná může mít vlastní hodnotu, hodnotu proměnné z jiného souboru i vypočtenou hodnotu, případně výsledek nějaké less funkce https://lesscss.org/functions/
4. Následují vlastní styly. Opět je dobré oddělit to komentářem pro lepší přehlednost. Zde se nesmí objevit proměnná z jiného souboru, pouze proměnné nadefinované v sekci Variables!! Vyjímkou jsou proměnné `@breakpoint-*`, které se používají v media query. Ne každý styl musí mít hodnotu z proměnné, `display` bude mít pravděpodobně hodnotu natvrdo, nebo pokud chci v nějakém divu seznam bez odrážek, zapíšu to zde přímo. Pamatuj, pokud nepoužiju proměnnou, usnadním si práci, ale přidělám jí kolegovi, nebo sobě do budoucna!

#### `site/uikit_theme`
Příklad viz `bar.less`   
Soubory v `uikit_theme` prepisuji UIKit soubory (`less/vendor/uikit_x.y.z/components/*`). 
Co jde, mělo by být změněno na úrovni proměnných. Pokud to není možné, použijou se [hooky](https://getuikit.com/docs/less#use-hooks).
Všechny styly by měly být zapsané uvnitř nějakého hooku, pokud mi chybí vhodný hook, použiju `.hook-card-misc()` na konci souboru.
Best practise je nakopírovat soubor z `less/vendor/uikit_x.y.z/theme` do `less/site/uikit_theme`, vymazat nepotřebné styly (ale nechat hooky) a tento soubor potom upravit. Samozřejmě zapsat do importu `less/site/_import.less`

1. Nadpis - zde bude jen Component: <název komponenty>
2. Popis je zde zbytečný, každý ví, že soubor upravuje UIKit komponentu
3. Variables má podsekci New. Nad ní by měly být upravené existující proměnné ze souboru koponenty (`less/vendor/uikit_x.y.z/components/*`), pod New by pak měly být nové proměnné, které vznikly kvůli novým stylům v tomto souboru.
4. Existující proměnné bude pravděpodobně stačit jen nadefinovat
   Nové proměnné jsou použity v hookách v tomto souboru
   Pokud nakopíruju soubor s `less/vendor/uikit_x.y.z/theme`, nechám tam prázdné hooky viz příklad `.hook-bar-hover() {}` 
   Pokud potřebuju přidat styl a nemám hook, použiju `.hook-bar-misc() {}` na konci souboru viz příklad
