//
// Component: Bar
//
// ========================================================================


// Variables
// ========================================================================

@bar-background:                                    #af8;
@bar-color:                                         @link-color;
@bar-hover-color:                                   @link-hover-color;
@bar-height:                                        60px;
@bar-width:                                         @container-width / 2;

//
// New
//

@bar-something-margin-bottom:                  20px;
@bar-something-margin-bottom-l:                30px;
@bar-something-background:                     #ffaaaa;


// Component
// ========================================================================

.hook-bar() {
    display: inline-block;
    padding: 0;
    margin: 0;
}

.hook-bar-hover() {}

// Something
// ========================================================================

.hook-bar-something {
    margin-bottom: @bar-something-margin-bottom;
    background: @bar-something-ackground;
}

.hook-bar-misc() {

    /* Desktop and bigger */
    @media (min-width: @breakpoint-large) {
        .bar-something {
            margin-bottom: @bar-something-margin-bottom-l;
        }
    }

}
