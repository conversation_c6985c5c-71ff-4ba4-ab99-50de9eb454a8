//
// error/detail
//
// Styly tykajici se detailu chyby
//
// ========================================================================


// Variables
// ========================================================================

@error-detail-status-top:                     0;
@error-detail-status-right:                   0;

@error-detail-message-font-size:                 @global-font-size;
@error-detail-message-padding:                   2px 5px 4px;
@error-detail-message-border-width:              2px;
@error-detail-message-border-radius:             3px;
@error-detail-message-header:                    @global-inverse-color;

@error-detail-message-notice:                    #3399ff;
@error-detail-message-warning:                   darkorange;
@error-detail-message-danger:                    red;


@error-detail-occurrence-blank:                  @global-inverse-color;
@error-detail-occurrence-default:                #dbdbdb;
@error-detail-occurrence-low:                    #ffbaba;
@error-detail-occurrence-medium:                 #ff5252;
@error-detail-occurrence-high:                   #ff0000;
@error-detail-occurrence-extreme:                #a70000;


// Site
// ========================================================================

.error-detail {
    position: relative;

    & .status {
        position: absolute;
        top: @error-detail-status-top;
        right: @error-detail-status-right;
    }

    & .message {
        font-size: @error-detail-message-font-size;
        border-width: @error-detail-message-border-width;
        border-style: solid;
        border-radius: @error-detail-message-border-radius;

        &.message-notice {
            border-color: @error-detail-message-notice;
        }

        &.message-warning {
            border-color: @error-detail-message-warning;
        }

        &.message-danger {
            border-color: @error-detail-message-danger;
        }

        & .message-header {
            color: @error-detail-message-header;
            padding: @error-detail-message-padding;
        }

        & .message-header-notice {
            background-color: @error-detail-message-notice;
        }

        & .message-header-warning {
            background-color: @error-detail-message-warning;
        }

        & .message-header-danger {
            background-color: @error-detail-message-danger;
        }

        & pre {
            margin: 0;
            border: none;
            font-size: @error-detail-message-font-size;
            white-space: pre-line
        }
    }
}

.occurrence-none {
    fill: @error-detail-occurrence-blank;
}

.occurrence-default {
    fill: @error-detail-occurrence-default;
}

.occurrence-low {
    fill: @error-detail-occurrence-low;
}

.occurrence-medium {
    fill: @error-detail-occurrence-medium;
}

.occurrence-high {
    fill: @error-detail-occurrence-high;
}

.occurrence-extreme {
    fill: @error-detail-occurrence-extreme;
}

.occurrence-text {
    font-size: 12px;
    fill: darken(@error-detail-occurrence-default, 40%);
}
