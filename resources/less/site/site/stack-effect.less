@stack-effect-inner-color:    @card-default-background; // pozadi karet by melo mit svoji promennou
@stack-effect-outer-color:   @card-border;  // ramecek karet by melo mit svoji promennou

.stack-effect-1 {
    box-shadow: -2px 2px 0 -1px @stack-effect-inner-color, -2px 2px @stack-effect-outer-color;
}

.stack-effect-2 {
    box-shadow: -2px 2px 0 -1px @stack-effect-inner-color, -2px 2px @stack-effect-outer-color, -4px 4px 0 -1px @stack-effect-inner-color, -4px 4px @stack-effect-outer-color;
}
.stack-effect-3 {
    box-shadow: -2px 2px 0 -1px @stack-effect-inner-color, -2px 2px @stack-effect-outer-color, -4px 4px 0 -1px @stack-effect-inner-color, -4px 4px @stack-effect-outer-color, -6px 6px 0 -1px @stack-effect-inner-color, -6px 6px @stack-effect-outer-color;
}
.stack-effect-4 {
    box-shadow: -2px 2px 0 -1px @stack-effect-inner-color, -2px 2px @stack-effect-outer-color, -4px 4px 0 -1px @stack-effect-inner-color, -4px 4px @stack-effect-outer-color, -6px 6px 0 -1px @stack-effect-inner-color, -6px 6px @stack-effect-outer-color, -8px 8px 0 -1px @stack-effect-inner-color, -8px 8px @stack-effect-outer-color;
}
.stack-effect-5 {
    box-shadow: -2px 2px 0 -1px @stack-effect-inner-color, -2px 2px @stack-effect-outer-color, -4px 4px 0 -1px @stack-effect-inner-color, -4px 4px @stack-effect-outer-color, -6px 6px 0 -1px @stack-effect-inner-color, -6px 6px @stack-effect-outer-color, -8px 8px 0 -1px @stack-effect-inner-color, -8px 8px @stack-effect-outer-color, -10px 10px 0 -1px @stack-effect-inner-color, -10px 10px @stack-effect-outer-color;
}
.stack-effect-6 {
    box-shadow: -2px 2px 0 -1px @stack-effect-inner-color, -2px 2px @stack-effect-outer-color, -4px 4px 0 -1px @stack-effect-inner-color, -4px 4px @stack-effect-outer-color, -6px 6px 0 -1px @stack-effect-inner-color, -6px 6px @stack-effect-outer-color, -8px 8px 0 -1px @stack-effect-inner-color, -8px 8px @stack-effect-outer-color, -10px 10px 0 -1px @stack-effect-inner-color, -10px 10px @stack-effect-outer-color, -12px 12px 0 -1px @stack-effect-inner-color, -12px 12px @stack-effect-outer-color;
}
.stack-effect-7 {
    box-shadow: -2px 2px 0 -1px @stack-effect-inner-color, -2px 2px @stack-effect-outer-color, -4px 4px 0 -1px @stack-effect-inner-color, -4px 4px @stack-effect-outer-color, -6px 6px 0 -1px @stack-effect-inner-color, -6px 6px @stack-effect-outer-color, -8px 8px 0 -1px @stack-effect-inner-color, -8px 8px @stack-effect-outer-color, -10px 10px 0 -1px @stack-effect-inner-color, -10px 10px @stack-effect-outer-color, -12px 12px 0 -1px @stack-effect-inner-color, -12px 12px @stack-effect-outer-color, -14px 14px 0 -1px @stack-effect-inner-color, -14px 14px @stack-effect-outer-color;
}
.stack-effect-8 {
    box-shadow: -2px 2px 0 -1px @stack-effect-inner-color, -2px 2px @stack-effect-outer-color, -4px 4px 0 -1px @stack-effect-inner-color, -4px 4px @stack-effect-outer-color, -6px 6px 0 -1px @stack-effect-inner-color, -6px 6px @stack-effect-outer-color, -8px 8px 0 -1px @stack-effect-inner-color, -8px 8px @stack-effect-outer-color, -10px 10px 0 -1px @stack-effect-inner-color, -10px 10px @stack-effect-outer-color, -12px 12px 0 -1px @stack-effect-inner-color, -12px 12px @stack-effect-outer-color, -14px 14px 0 -1px @stack-effect-inner-color, -14px 14px @stack-effect-outer-color, -16px 16px 0 -1px @stack-effect-inner-color, -16px 16px @stack-effect-outer-color;
}
.stack-effect-9 {
    box-shadow: -2px 2px 0 -1px @stack-effect-inner-color, -2px 2px @stack-effect-outer-color, -4px 4px 0 -1px @stack-effect-inner-color, -4px 4px @stack-effect-outer-color, -6px 6px 0 -1px @stack-effect-inner-color, -6px 6px @stack-effect-outer-color, -8px 8px 0 -1px @stack-effect-inner-color, -8px 8px @stack-effect-outer-color, -10px 10px 0 -1px @stack-effect-inner-color, -10px 10px @stack-effect-outer-color, -12px 12px 0 -1px @stack-effect-inner-color, -12px 12px @stack-effect-outer-color, -14px 14px 0 -1px @stack-effect-inner-color, -14px 14px @stack-effect-outer-color, -16px 16px 0 -1px @stack-effect-inner-color, -16px 16px @stack-effect-outer-color, -18px 18px 0 -1px @stack-effect-inner-color, -18px 18px @stack-effect-outer-color;
}
