//
// Component:       Variables
// Description:     Defines common values which are used across all components
//
// ========================================================================


// Load deprecated components
// ========================================================================

@deprecated:                                   false;


// Breakpoints
// ========================================================================

// Phone Portrait:   Galaxy (360x640), iPhone 6 (375x667), iPhone 6+ (414x736)
// Phone Landscape:  Galaxy (640x360), iPhone 6 (667x375), iPhone 6+ (736x414)
// Tablet Portrait:  iPad (768x1024), Galaxy Tab (800x1280),
// Tablet Landscape: iPad (1024x768), iPad Pro (1024x1366),
// Desktop:          Galaxy Tab (1280x800), iPad Pro (1366x1024)

@breakpoint-small:                             640px;  // Phone landscape
@breakpoint-medium:                            960px;  // Tablet Landscape
@breakpoint-large:                             1200px; // Desktop
@breakpoint-xlarge:                            1600px; // Large Screens

@breakpoint-xsmall-max:                        (@breakpoint-small - 1);
@breakpoint-small-max:                         (@breakpoint-medium - 1);
@breakpoint-medium-max:                        (@breakpoint-large - 1);
@breakpoint-large-max:                         (@breakpoint-xlarge - 1);


// Global variables
// ========================================================================
@global-gutter:                                 20px;
@global-small-gutter:                           10px;
@global-medium-gutter:                          30px;
@global-large-gutter:                           50px;
//
// Typography
//

@global-font-family:                            'IBM Plex Sans', sans-serif;
@global-font-size:                              16px;
@global-line-height:                            1.5;      // 24px

@global-2xlarge-font-size:                      2.625rem; // 42px
@global-xlarge-font-size:                       2rem;     // 32px
@global-large-font-size:                        1.5rem;   // 24px
@global-medium-font-size:                       1.25rem;  // 20px
@global-small-font-size:                        0.875rem; // 14px

//
// Colors
//

@global-color:                                  #666;
@global-emphasis-color:                         #333;
@global-muted-color:                            #999;

@global-link-color:                             #1e87f0;
@global-link-hover-color:                       #0f6ecd;

@global-inverse-color:                          #fff;

//
// Backgrounds
//

@global-background:                             #fff;

@global-muted-background:                       #f8f8f8;
@global-primary-background:                     #1e87f0;
@global-secondary-background:                   darkorange;
@global-default-background:                     #222;

@global-success-background:                     #32d296;
@global-warning-background:                     #faa05a;
@global-danger-background:                      #db3535;

//
// Borders
//

@global-border-width:                           1px;
@global-border:                                 #e5e5e5;

//
// Box-Shadows
//

@global-small-box-shadow:                       0 2px 8px rgba(0,0,0,0.08);
@global-medium-box-shadow:                      0 5px 15px rgba(0,0,0,0.08);
@global-large-box-shadow:                       0 14px 25px rgba(0,0,0,0.16);
@global-xlarge-box-shadow:                      0 28px 50px rgba(0,0,0,0.16);

//
// Spacings
//

// Used in margin, section, list
@global-margin:                                 20px;
@global-small-margin:                           10px;
@global-medium-margin:                          40px;
@global-large-margin:                           70px;
@global-xlarge-margin:                          140px;

// Used in grid, column, container, align, card, padding
@global-gutter:                                 20px;
@global-xsmall-gutter:                          5px;
@global-small-gutter:                           10px;
@global-medium-gutter:                          30px;
@global-large-gutter:                           50px;

//
// Controls
//

@global-control-height:                         40px;
@global-control-small-height:                   30px;
@global-control-large-height:                   55px;

//
// Z-index
//

@global-z-index:                                1000;
