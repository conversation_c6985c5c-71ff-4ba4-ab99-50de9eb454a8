//
// Component: Notification
//
// ========================================================================


// Variables
// ========================================================================

@notification-width:                                  800px;

@notification-message-primary-color:                  @alert-primary-color;
@notification-message-success-color:                  @alert-success-color;
@notification-message-warning-color:                  @alert-warning-color;
@notification-message-danger-color:                   @alert-danger-color;

// New

@notification-message-primary-background:             @alert-primary-background;
@notification-message-success-background:             @alert-success-background;
@notification-message-warning-background:             @alert-warning-background;
@notification-message-danger-background:              @alert-danger-background;

// Component
// ========================================================================

.hook-notification() {
    max-width: 100%;
}


// Message
// ========================================================================

.hook-notification-message() {
    border: @global-border-width solid @global-border;
    padding-right: @global-medium-gutter;
}


// Close
// ========================================================================

.hook-notification-close() {}


// Style modifiers
// ========================================================================

.hook-notification-message-primary() {
    background: @notification-message-primary-background;
    border: @global-border-width solid @notification-message-primary-color;
}

.hook-notification-message-success() {
    background: @notification-message-success-background;
    border: @global-border-width solid @notification-message-success-color;
}

.hook-notification-message-warning() {
    background: @notification-message-warning-background;
    border: @global-border-width solid @notification-message-warning-color;
}

.hook-notification-message-danger() {
    background: @notification-message-danger-background;
    border: @global-border-width solid @notification-message-danger-color;
}


// Miscellaneous
// ========================================================================

.hook-notification-misc() {}
