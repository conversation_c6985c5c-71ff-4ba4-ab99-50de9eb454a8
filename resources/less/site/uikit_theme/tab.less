//
// Component: Tab
//
// ========================================================================


// Variables
// ========================================================================
@tab-margin-horizontal:                         0px;

//
// New
//

@tab-border-width:                              @global-border-width;
@tab-border:                                    @global-border;

@tab-item-border-width:                         @global-border-width;
@tab-item-font-size:                            @global-small-font-size;
@tab-item-text-transform:                       uppercase;

@tab-item-active-border:                        @global-primary-background;


// Component
// ========================================================================

.hook-tab() {

    position: relative;

    &::before {
        content: "";
        position: absolute;
        bottom: 0;
        left: @tab-margin-horizontal;
        right: 0;
        border-bottom: @tab-border-width solid @tab-border;
    }

}


// Items
// ========================================================================

.hook-tab-item() {
    border-bottom: @tab-item-border-width solid transparent;
    font-size: @tab-item-font-size;
    transition: color 0.1s ease-in-out;
}

.hook-tab-item-hover() {}

.hook-tab-item-active() { border-color: @tab-item-active-border; }

.hook-tab-item-disabled() {}


// Position modifiers
// ========================================================================

.hook-tab-bottom() {

    &::before {
        top: 0;
        bottom: auto;
    }

}

.hook-tab-bottom-item() {
    border-top: @tab-item-border-width solid transparent;
    border-bottom: none;
}

.hook-tab-left() {

    &::before {
        top: 0;
        bottom: 0;
        left: auto;
        right: 0;
        border-left: @tab-border-width solid @tab-border;
        border-bottom: none;
    }

}

.hook-tab-left-item() {
    border-right: @tab-item-border-width solid transparent;
    border-bottom: none;
}

.hook-tab-right() {

    &::before {
        top: 0;
        bottom: 0;
        left: 0;
        right: auto;
        border-left: @tab-border-width solid @tab-border;
        border-bottom: none;
    }

}

.hook-tab-right-item() {
    border-left: @tab-item-border-width solid transparent;
    border-bottom: none;
}


// Miscellaneous
// ========================================================================

.hook-tab-misc() {

    .uk-tab .uk-dropdown { margin-left: (@tab-margin-horizontal + @tab-item-padding-horizontal) }

}


// Inverse
// ========================================================================

@inverse-tab-border:                        @inverse-global-border;

.hook-inverse-tab() {

    &::before { border-color: @inverse-tab-border; }

}

.hook-inverse-tab-item() {}
.hook-inverse-tab-item-hover() {}
.hook-inverse-tab-item-active() { border-color: @inverse-global-primary-background; }
.hook-inverse-tab-item-disabled() {}
