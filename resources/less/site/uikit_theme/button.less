//
// Component: Button
//
// ========================================================================


// Variables
// ========================================================================

@button-line-height:                            30px;
@button-small-line-height:                      @global-control-small-height - (@button-border-width * 2);
@button-large-line-height:                      @global-control-large-height - (@button-border-width * 2);

@button-font-size:                              @global-small-font-size;
@button-large-font-size:                        @global-small-font-size;

@button-primary-color:                          @global-inverse-color;
@button-primary-background:                     @global-primary-background;
@button-primary-hover-background:               darken(@button-primary-background, 5%);

@button-secondary-color:                        @global-inverse-color;
@button-secondary-background:                   @global-secondary-background;
@button-secondary-hover-background:             darken(@button-secondary-background, 5%);

@button-default-background:                     transparent;
@button-default-hover-background:               transparent;
@button-default-active-background:              transparent;

@button-muted-background:                       @global-default-background;
@button-muted-hover-background:                 lighten(@button-muted-background, 5%);

@button-disabled-background:                    transparent;

@button-text-hover-color:                       @global-emphasis-color;

//
// New
//

@button-border-width:                           @global-border-width;

@button-default-border:                         @global-border;
@button-default-hover-border:                   darken(@global-border, 20%);
@button-default-active-border:                  darken(@global-border, 30%);

@button-disabled-border:                        @global-border;

@button-text-border-width:                      @global-border-width;
@button-text-border:                            @button-text-hover-color;


// Component
// ========================================================================

.hook-button() {
    transition: 0.1s ease-in-out;
    transition-property: color, background-color, border-color;
    padding: 0 16px;
    line-height: @button-line-height;
    border-radius: 5px;
}

.hook-button-hover() {}

.hook-button-active() {}


// Style modifiers
// ========================================================================

.hook-button-default() { border: @button-border-width solid @button-default-border; }

.hook-button-default-hover() { border-color: @button-default-hover-border; }

.hook-button-default-active() { border-color: @button-default-active-border; }

//
// Primary
//

.hook-button-primary() {
    border: @button-border-width solid transparent;
}

.hook-button-primary-hover() {}

.hook-button-primary-active() {}

//
// Secondary
//

.hook-button-secondary() { border: @button-border-width solid transparent; }

.hook-button-secondary-hover() {}

.hook-button-secondary-active() {}

//
// Danger
//

.hook-button-danger() { border: @button-border-width solid transparent; }

.hook-button-danger-hover() {}

.hook-button-danger-active() {}


// Disabled
// ========================================================================

.hook-button-disabled() { border-color: @button-disabled-border; }


// Size modifiers
// ========================================================================

.hook-button-small() {}

.hook-button-large() {}


// Text modifier
// ========================================================================

.hook-button-text() {

    position: relative;

    &::before {
        content: "";
        position: absolute;
        bottom: 0;
        left: 0;
        right: 100%;
        border-bottom: @button-text-border-width solid @button-text-border;
        transition: right 0.3s ease-out;
    }

}

.hook-button-text-hover() {

    &::before { right: 0; }

}

.hook-button-text-disabled() {

    &::before { display: none; }

}


// Link modifier
// ========================================================================

.hook-button-link() {}


// Miscellaneous
// ========================================================================

.hook-button-misc() {

    /* Group
     ========================================================================== */
    .uk-button-group .uk-button:first-child {
        border-radius: 5px 0 0 5px;
    }

    /*
     * Collapse border
     */

    .uk-button-group > .uk-button:nth-child(n+2),
    .uk-button-group > div:nth-child(n+2) .uk-button {
        margin-left: -@button-border-width;
        border-radius: 0 5px 5px 0;
    }

    /*
     * Create position context to superimpose the successor elements border
     * Known issue: If you use an `a` element as button and an icon inside,
     * the active state will not work if you click the icon inside the button
     * Workaround: Just use a `button` or `input` element as button
     */

    .uk-button-group .uk-button:hover,
    .uk-button-group .uk-button:active,
    .uk-button-group .uk-button.uk-active {
        position: relative;
        z-index: 1;
    }

    .uk-button-muted {
        background-color: @button-muted-background;
        color: @global-inverse-color;
    }

    .uk-button-muted:hover {
        background-color: @button-muted-hover-background;
    }
}
