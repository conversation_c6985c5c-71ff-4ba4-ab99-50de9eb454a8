//
// Component: Navbar
//
// ========================================================================


// Variables
// ========================================================================

@navbar-background:                             white;
@navbar-muted-background:                       @global-muted-background;

@navbar-nav-item-color:                         #666;
@navbar-nav-item-hover-color:                   #333;

//
// New
//

@navbar-dropdown-nav-font-size:                 @global-small-font-size;
@navbar-dropdown-box-shadow:                    0 5px 12px rgba(0,0,0,0.15);

@navbar-margin-reset:                           0;
@navbar-gutter:                                 @global-gutter;

@navbar-font-size:                              @global-font-size+10px;

@navbar-inverse-color:                          @global-inverse-color;


// Component
// ========================================================================

.hook-navbar() {}


// Container
// ========================================================================

.hook-navbar-container() {
    background: @navbar-background;
    box-shadow: 0 1px 5px #ccc;
    margin-bottom: @global-medium-gutter;
}


// Nav
// ========================================================================

.hook-navbar-nav-item() {
    transition: 0.1s ease-in-out;
    transition-property: color, background-color;
}

.hook-navbar-nav-item-hover() {}

.hook-navbar-nav-item-onclick() {}

.hook-navbar-nav-item-active() {}


// Item
// ========================================================================

.hook-navbar-item() {
    padding: 0 @global-gutter;
}


// Toggle
// ========================================================================

.hook-navbar-toggle() {}

.hook-navbar-toggle-hover() {}

.hook-navbar-toggle-icon() {}

.hook-navbar-toggle-icon-hover() {}


// Subtitle
// ========================================================================

.hook-navbar-subtitle() {}


// Style modifiers
// ========================================================================

.hook-navbar-primary() {}

.hook-navbar-transparent() {}

.hook-navbar-sticky() {}


// Dropdown
// ========================================================================

.hook-navbar-dropdown() {}

.hook-navbar-dropdown-large() {}

.hook-navbar-dropdown-dropbar() {}

.hook-navbar-dropdown-dropbar-large() {}


// Dropdown nav
// ========================================================================

.hook-navbar-dropdown-nav() {}

.hook-navbar-dropdown-nav-item() {}

.hook-navbar-dropdown-nav-item-hover() {}

.hook-navbar-dropdown-nav-subtitle() {}

.hook-navbar-dropdown-nav-header() {}

.hook-navbar-dropdown-nav-divider() {}


// Dropbar
// ========================================================================

.hook-navbar-dropbar() {}


// Miscellaneous
// ========================================================================

.hook-navbar-misc() {
    .user-dashboard {
        margin-bottom: @navbar-margin-reset;
        padding: 0;
        margin-left: @navbar-gutter; //pretizeni promenne
    }

    .uk-navbar-nav > li.uk-active > a::before {
        right: 0;
    }
}
