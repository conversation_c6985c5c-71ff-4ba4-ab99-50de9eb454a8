//
// Component: Label
//
// ========================================================================


// Variables
// ========================================================================

//
// New
//

@label-border-radius:                           2px;
@label-text-transform:                          uppercase;

@label-background:                              @global-primary-background;
@label-success-background:                      #2bbd2b;
@label-danger-background:                       #e93e3e;


// Component
// ========================================================================

.hook-label() {
    background-color: @label-background;
    border-radius: @label-border-radius;
    display: list-item;
}


// Color modifiers
// ========================================================================

.hook-label-success() {
    background-color: @label-success-background;
}

.hook-label-warning() {}

.hook-label-danger() {
    background-color: @label-danger-background;
}


// Miscellaneous
// ========================================================================

.hook-label-misc() {}


// Inverse
// ========================================================================

.hook-inverse-label() {}
