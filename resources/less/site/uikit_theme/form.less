//
// Component: Form
//
// ========================================================================


// Variables
// ========================================================================

@form-line-height:                              @form-height - (2* @form-border-width);

@form-background:                               @global-background;
@form-focus-background:                         @global-background;

@form-small-line-height:                        @form-small-height - (2* @form-border-width);
@form-large-line-height:                        @form-large-height - (2* @form-border-width);

@form-radio-background:                         transparent;

@form-stacked-margin-bottom:                    5px;

//
// New
//

@form-border-width:                             @global-border-width;
@form-border:                                   darken(@global-border, 10%);
@form-border-radius:                            5px;

@form-focus-border:                             @global-primary-background;

@form-disabled-border:                          @global-border;

@form-danger-border:                            @global-danger-background;
@form-success-border:                           @global-success-background;

@form-blank-focus-border:                       @global-border;
@form-blank-focus-border-style:                 solid;
// Component
// ========================================================================

.hook-form() {
    border: @form-border-width solid @form-border;
    border-radius: @form-border-radius;
    transition: 0.2s ease-in-out;
    transition-property: color, background-color, border;
}

.hook-form-single-line() {
    margin-bottom: @global-small-gutter;
}

.hook-form-multi-line() {
    margin-bottom: @global-small-gutter;
}

.hook-form-focus() { border-color: @form-focus-border; }

.hook-form-disabled() {}


// Style modifiers
// ========================================================================

.hook-form-danger() { border-color: @form-danger-border; }

.hook-form-success() { border-color: @form-success-border; }

.hook-form-blank() { border-color: transparent; }

.hook-form-blank-focus() {
    border-color: @form-blank-focus-border;
    border-style: @form-blank-focus-border-style;
}


// Radio and checkbox
// ========================================================================

.hook-form-radio() {
    border: 1px solid #a6a6a6;
}

.hook-form-radio-focus() {}

.hook-form-radio-checked() {
    border: unset;
}

.hook-form-radio-checked-focus() {}

.hook-form-radio-disabled() {}


// Legend
// ========================================================================

.hook-form-legend() {}


// Label
// ========================================================================

.hook-form-label() {}


// Layout
// ========================================================================

.hook-form-stacked-label() {}

.hook-form-horizontal-label() {}


// Miscellaneous
// ========================================================================

.hook-form-misc() {}
