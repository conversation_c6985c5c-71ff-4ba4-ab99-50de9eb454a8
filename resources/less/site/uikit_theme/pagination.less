//
// Component: Pagination
//
// ========================================================================


// Variables
// ========================================================================

@pagination-item-color:                                  @global-primary-background;
@pagination-item-border-radius:                          5px;
@pagination-item-hover-color:                            darken(@global-primary-background, 20%);
@pagination-item-active-background:                      @global-primary-background;
@pagination-item-active-color:                           @global-background;


// Component
// ========================================================================

.hook-pagination() {}


// Items
// ========================================================================

.hook-pagination-item() {
    transition: color 0.1s ease-in-out;
    border-radius: @pagination-item-border-radius;
}

.hook-pagination-item-hover() {}

.hook-pagination-item-active() {
    background: @pagination-item-active-background;
}

.hook-pagination-item-disabled() {}


// Miscellaneous
// ========================================================================

.hook-pagination-misc() {}


// Inverse
// ========================================================================

.hook-inverse-pagination-item() {}
.hook-inverse-pagination-item-hover() {}
.hook-inverse-pagination-item-active() {}
.hook-inverse-pagination-item-disabled() {}
