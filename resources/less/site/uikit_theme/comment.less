//
// Component: Comment
//
// ========================================================================


// Variables
// ========================================================================
@comment-title-font-size:                       16px;
@comment-meta-font-size:                        14px;

@comment-header-margin-bottom:                  @global-small-gutter;
//
// New
//

@comment-primary-padding:                       @global-gutter;
@comment-primary-background:                    @global-muted-background;


// Component
// ========================================================================

.hook-comment() {
    border-bottom: 1px solid @global-border;
}


// Sections
// ========================================================================

.hook-comment-body() {
    margin-bottom: @global-gutter;
}

.hook-comment-header() {}


// Title
// ========================================================================

.hook-comment-title() {
    font-weight: bold;
}


// Meta
// ========================================================================

.hook-comment-meta() {}


// Avatar
// ========================================================================

.hook-comment-avatar() {}


// List
// ========================================================================

.hook-comment-list-adjacent() {}

.hook-comment-list-sub() {}

.hook-comment-list-sub-adjacent() {}


// Style modifier
// ========================================================================

.hook-comment-primary() {
    padding: @comment-primary-padding;
    background-color: @comment-primary-background;
}


// Miscellaneous
// ========================================================================

.hook-comment-misc() {}
