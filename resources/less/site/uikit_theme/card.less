//
// Component: Card
//
// ========================================================================


// Variables
// ========================================================================

@card-background:                              @global-background;
@card-hover-background:                        @global-muted-background;

//
// New
//
@card-border:                                  @global-border;
@card-border-width:                            @global-border-width;
@card-border-style:                            solid;

@card-font-size:                               @global-font-size;
@card-line-height:                             @global-line-height;
@card-small-line-height:                       1;
@card-color:                                   #aaa;

@card-body-padding:                            @global-small-gutter;
@card-margin-reset:                            0;
@card-margin:                                  @global-margin;

// Component
// ========================================================================

.hook-card() {
    i {
        margin-right: @global-xsmall-gutter;
        width: 16px;
    }
    background-color: @card-background;
    border: @card-border-width @card-border-style @card-border;
    margin: @card-margin @card-margin-reset;
    padding: @global-gutter;
    display: flex;
}


// Sections
// ========================================================================

.hook-card-body() {}

.hook-card-header() {}

.hook-card-footer() {}


// Media
// ========================================================================

.hook-card-media() {}

.hook-card-media-top() {}

.hook-card-media-bottom() {}

.hook-card-media-left() {
    width: 20%;

    div {
        display: flex;
        justify-content: space-between;
        margin: @global-xsmall-gutter 16px @card-margin-reset @card-margin-reset;
    }
    h3 {
        font-size: @card-font-size;
        margin: @card-margin-reset;
    }
    p {
        color: @card-color;
        font-size: @card-font-size;
        margin: @card-margin-reset;
    }
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    border-right: @card-border-width @card-border-style @card-border;
}

.hook-card-media-right() {}


// Title
// ========================================================================

.hook-card-title() {
    line-height: @card-small-line-height;
}


// Badge
// ========================================================================

.hook-card-badge() {
    border-radius: 5px;
}


// Hover modifier
// ========================================================================

.hook-card-hover() {}


// Style modifiers
// ========================================================================

.hook-card-default() {
    background-color: @card-background;
}

.hook-card-default-title() {}

.hook-card-default-hover() {}

.hook-card-default-header() {}

.hook-card-default-footer() {}

//
// Primary
//

.hook-card-primary() {}

.hook-card-primary-title() {}

.hook-card-primary-hover() {}

//
// Secondary
//

.hook-card-secondary() {}

.hook-card-secondary-title() {}

.hook-card-secondary-hover() {}


// Miscellaneous
// ========================================================================

.hook-card-misc() {
    .uk-card-badge-bottom {
        position: absolute;
        bottom: 15px;
        right: 15px;
        padding: 0 10px;
        z-index: 1;
        height: 22px;
        font-size: .875rem;
        display: flex;
        justify-content: center;
        align-items: center;
    }
    .card-main {
        width: 80%;
        margin: @global-small-gutter @global-gutter;
        font-size: @card-font-size;
    }

    .card-main h2 {
        margin-bottom: @global-small-gutter;
    }

    .card-app-detail {
        margin: @global-small-gutter @global-small-gutter @global-small-gutter @card-margin-reset;
    }

    .card-app-detail a {
        cursor: pointer;
    }

    .card-app-detail svg {
        margin-right: 5px;
    }

    .clickable {
        cursor: pointer;
    }

    .clickable:hover {
        background-color: @card-hover-background;
        color: unset;
    }

    .app-mailbox {
        margin: 0 0 0 20px;
        padding: 0;
        line-height: @button-link-line-height;
        background: none;
        color: @button-link-color;
        border: unset;
        cursor: pointer;
    }

    .app-eye{
        margin: 0 0 0 20px;
        padding: 0;
        line-height: @button-link-line-height;
        background: none;
        color: @button-link-color;
        border: unset;
        cursor: pointer;
    }
}
