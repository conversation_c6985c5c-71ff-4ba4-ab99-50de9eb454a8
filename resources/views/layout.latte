{var $loggedUser = Auth::user()}
{var $isDev = Auth::guard('gitlab')->user() !== null}

<!DOCTYPE html>
<html lang="{str_replace('_', '-', app()->getLocale())}">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <title>DEK Errorlog</title>

        <link rel="icon" href="{asset('favicon.ico')}">
        <link href="https://fonts.googleapis.com/css2?family=IBM+Plex+Sans:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;1,100;1,200;1,300;1,400;1,500;1,600;1,700&display=swap" rel="stylesheet">
        <link rel="stylesheet" href="{asset('css/site.css')}?m={filemtime(public_path('css/site.css'))}">

        <!-- UIkit JS -->
        <script src="{asset('js/uikit.min.js')}?m={filemtime(public_path('js/uikit.min.js'))}"></script>
        <script src="{asset('js/uikit-icons.min.js')}?m={filemtime(public_path('js/uikit-icons.min.js'))}"></script>

        <script src="https://kit.fontawesome.com/3e7158a24b.js" crossorigin="anonymous"></script>

        {block head}{/block}
    </head>
    <body>
        {if url()->current() !== route('login')}
            {include partials/nav.latte, loggedUser: $loggedUser, isDev: $isDev}
        {/if}
        {include content, isDev => $isDev}
        {include partials/footer.latte}

        {x flash-message.control}

        {block bottomscripts}{/block}
    </body>
</html>
