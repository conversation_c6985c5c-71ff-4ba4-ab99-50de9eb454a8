{layout '../layout.latte'}

{block head}
    <script src="https://cdn.jsdelivr.net/npm/vue@2.7.14"></script>
{/block}

{block content}
    <div class="uk-container" id="app">
        <div class="uk-flex uk-flex-between">
            <h1 class="uk-text-large uk-margin-remove uk-width-expand">Aplikace</h1>
            <div class="uk-flex uk-flex-middle uk-flex-right uk-width-1-3">
                <form class="uk-flex" action="{route('apps.index')}">
                    <select name="sort" class="uk-select uk-form-small uk-margin-remove" aria-label="Select" style="width: 120px">
                        <option selected hidden value="">Řazení</option>
                        <option value="name" {if $sorted === 'name'}selected{/if}>Názvu</option>
                        <option value="errorsCount" {if $sorted === 'errorsCount'}selected{/if}>Počet chyb</option>
                        <option value="appId" {if $sorted === 'appId'}selected{/if}>ID aplikace</option>
                        <option value="gitlabId" {if $sorted === 'gitlabId'}selected{/if}>Gitlab ID</option>
                        <option value="visibility" {if $sorted === 'visibility'}selected{/if}>Viditelnost</option>
                        <option value="notification" {if $sorted === 'notification'}selected{/if}>Notifikace</option>
                    </select>
                    <button class="uk-button uk-button-muted uk-form-small uk-margin-small-left">Filtrovat</button>
                </form>

                <a n:if="$isDev" class="uk-button uk-button-primary uk-margin-small-left uk-form-small" style="text-wrap: nowrap" href="{route('apps.create')}"><i class="fa-regular fa-plus"></i> Přidat aplikaci</a>
            </div>
        </div>

        <div class="uk-grid-column-medium uk-grid-row-small uk-grid-match" uk-grid>
            {foreach $apps as $singleApp}
                <div class="uk-width-1-4 special-box">
                    <div class="uk-card uk-card-default uk-card-body">
                        {if $counted[$singleApp->getId()]['unresolved'] === 0}
                            <div class="uk-card-badge uk-label uk-label-success"><i class="fa-solid fa-check"></i></div>
                        {else}
                            <div class="uk-card-badge uk-label uk-label-danger">{$counted[$singleApp->getId()]['unresolved']}</div>
                        {/if}
                        <div class="uk-flex uk-flex-column uk-flex-between" style="height: 100%">
                            <h3 class="uk-card-title">
                                <a href="{route('error', [$singleApp->getSlug()])}"
                                   uk-tooltip="Zobrazit chyby aplikace {$singleApp->getTitle()}"
                                >
                                    {$singleApp->getTitle()}
                                </a>
                            </h3>
                            <div class="uk-flex uk-flex-column uk-width-1-1">
                                <div class="uk-flex uk-flex-middle uk-flex-between uk-margin-small-bottom">
                                    <p class="uk-margin-remove">Viditelnost</p>
                                    <form method="post" action="{route('apps.action', [app => $singleApp->getId()])}">
                                        {csrf}
                                        {if in_array(Auth::user()->getIdentity(), $singleApp->getUserHiddenApps()->toArray())}
                                            <button type="submit" name="visibility" value="true" class="app-eye" uk-tooltip="Aplikace je skryta z výpisu">
                                                <i class="fa-duotone fa-eye-slash fa-xl" style="--fa-primary-color: #e01b24; --fa-secondary-color: #e01b24;"></i>
                                            </button>
                                        {else}
                                            <button type="submit" name="visibility" value="false" class="app-eye" uk-tooltip="Aplikace je viditelná ve výpisu">
                                                <i class="fa-duotone fa-eye fa-xl" style="--fa-primary-color: #26a269; --fa-secondary-color: #26a269;"></i>
                                            </button>
                                        {/if}
                                    </form>
                                </div>
                                <div class="uk-flex uk-flex-middle uk-flex-between uk-margin-small-bottom">
                                    <p class="uk-margin-remove">Notifikace</p>
                                    <form method="post" action="{route('apps.action', [app => $singleApp->getId()])}">
                                        {csrf}
                                    {if ! in_array(Auth::user()->getIdentity(), $singleApp->getMailboxAlerts()->toArray())}
                                            <button type="submit" name="mailbox" value="true" class="app-mailbox" uk-tooltip="Dostávat e-maily o nových chybách">
                                                <i class="fa-duotone fa-mailbox fa-xl" style="--fa-primary-color: #e01b24; --fa-secondary-color: #e01b24;"></i>
                                            </button>
                                        {else}
                                            <button type="submit" name="mailbox" value="false" class="app-mailbox" uk-tooltip="Zrušení e-mailových notifikací">
                                                <i class="fa-duotone fa-mailbox-flag-up fa-xl" style="--fa-primary-color: #26a269; --fa-secondary-color: #26a269;"></i>
                                            </button>
                                        {/if}
                                    </form>
                                </div>
                                <hr style="margin: 0 0 10px">
                                <div class="uk-flex uk-flex-middle uk-flex-between uk-margin-small-bottom">
                                    <p class="uk-margin-remove">ID aplikace</p>
                                    <button id="aid-wrapper" n:if="$singleApp->getId()" class="uk-button" uk-tooltip="Zkopírovat" @click="copy('aid-' + {$iterator->counter})">
                                        <span id="aid-{$iterator->counter}">{$singleApp->getId()}</span>
                                    </button>
                                </div>
                                <div class="uk-flex uk-flex-middle uk-flex-between uk-margin-small-bottom">
                                    <p class="uk-margin-remove">Gitlab ID</p>
                                    <button n:if="$singleApp->getGitlabId()" class="uk-button" uk-tooltip="Zkopírovat" @click="copy('gid-' + {$iterator->counter})">
                                        <span id="gid-{$iterator->counter}">{$singleApp->getGitlabId()}</span>
                                    </button>
                                </div>
{*                                <div class="uk-flex uk-flex-middle uk-flex-between uk-margin-small-bottom">*}
{*                                    <p class="uk-margin-remove">Gitlab skupina</p>*}
{*                                    <button id="glg-wrapper" n:if="$singleApp->getGitlabGroup()" class="uk-button" uk-tooltip="Zkopírovat" @click="copy('glg-' + {$iterator->counter})">*}
{*                                        <span id="glg-{$iterator->counter}">{strtolower($singleApp->getGitlabGroup())}</span>*}
{*                                    </button>*}
{*                                </div>*}
                                <a href="{route('apps.edit', [app => $singleApp->getId()])}">
                                    <button class="uk-button uk-button-default uk-width-1-1">
                                        <i class="fa-solid fa-pencil uk-margin-small-right"></i> Upravit aplikaci
                                    </button>
                                </a>
                                <hr style="margin: 5px 0">
                                <a href="{route('apps.error-log', [appSlug => $singleApp->getSlug()])}" target="_blank">
                                    <button n:if="$singleApp->getGitlabId()" class="uk-button uk-button-primary uk-width-1-1">
                                        <i class="fa-solid fa-brackets-square uk-margin-small-right"></i> Errorlog
                                    </button>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            {/foreach}
        </div>
    </div>

    {include '../partials/paginator.latte', 'paginable' => $apps}
{/block}

{block bottomscripts}
    <script>
        let vue = new Vue({
            el: '#app',
            methods: {
                copy(id) {
                    navigator.clipboard.writeText(document.getElementById(id).innerText);
                    UIkit.notification('Zkopírováno', { status: 'success' });
                }
            }
        });
    </script>
{/block}
