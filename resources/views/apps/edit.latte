{layout '../layout.latte'}

{block content}
    <div class="uk-container">
        <div class="uk-flex uk-flex-between">
            <h1 class="uk-text-large uk-width-expand">Úprava aplikace {$app->getTitle()}</h1>
        </div>

        {if $errors->any()}
            <div class="uk-alert-danger" style="margin: 0 0 20px" uk-alert>
                <ul class="uk-list">
                    {foreach $errors->all() as $error}
                        <li>{$error}</li>
                    {/foreach}
                </ul>
            </div>
        {/if}
        <form class="uk-form-stacked" method="post" action="{route('apps.update', [app => $app->getId()])}">
            {csrf}
            {method_field('PUT')|noescape}
            <fieldset class="uk-fieldset">
                <div class="uk-margin">
                    <label class="uk-form-label" for="gid">Gitlab ID</label>
                    <input class="uk-input" id="gid" name="gid" value="{old('gid', $app->getGitlabId())}" type="number" required>

                    <label class="uk-form-label" for="glabels">Gitlab štítky <span uk-icon="question" uk-tooltip="Např. error,errorlog,bug; Všechny issues této aplikace ponesou tyto štítky."></span></label>
                    <input class="uk-input" id="glabels" name="glabels" value="{old('glabels', $app->getGitlabLabel())}" type="text" required>

                    <div class="uk-margin uk-grid-small uk-child-width-auto uk-grid">
                        <label><input class="uk-radio" type="radio" name="radio-choice" value="ssh" id="radio-ssh" {if old('radio-choice') === 'ssh'}checked{/if}> SSH klíč</label>
                        <label><input class="uk-radio" type="radio" name="radio-choice" value="oauth" id="radio-oauth" {if old('radio-choice') === 'oauth'}checked{/if}> OAuth</label>
                    </div>

                    <div id="ssh">
                        <label class="uk-form-label" for="sshkey">SSH klíč</label>
                        <textarea class="uk-textarea" id="sshkey" name="sshkey" rows="5" placeholder="----- BEGIN PUBLIC KEY -----">{old('sshkey', $app->getSsh())}</textarea>
                    </div>

                    <div id="oauth">
                        <label class="uk-form-label" for="input-oauth">Oauth</label>
                        <input class="uk-input" id="input-oauth" name="oauth_client_id" value="{old('oauth', $app->getOauthClientId())}" autocomplete="off" placeholder="Client secret" type="text">
                    </div>

                    <label class="uk-form-label" for="sshalgo">SSH Algoritmus</label>
                    <select class="uk-select" id="sshalgo" name="sshalgo" required>
                        <option hidden value="">Vyber...</option>
                        {foreach \App\Enums\SSHAlgo::cases() as $algo}
                            <option value="{$algo->value}" {if intval(old('sshalgo', $app->getSshAlgo()->value)) === $algo->value}selected{/if}>{$algo->name}</option>
                        {/foreach}
                    </select>
                </div>
            </fieldset>

            <button type="submit" class="uk-button uk-button-primary">Odeslat</button>
        </form>
    </div>
{/block}

{block bottomscripts}
    <script>
        const radioSSH = document.getElementById('radio-ssh');
        const radioOAUTH = document.getElementById('radio-oauth');
        const sshInput = document.getElementById('ssh');
        const oauthInput = document.getElementById('oauth');

        document.addEventListener('DOMContentLoaded', function () {
            function toggleFields() {
                if (radioSSH.checked) {
                    sshInput.style.display = '';
                    oauthInput.style.display = 'none';
                } else if (radioOAUTH.checked) {
                    oauthInput.style.display = '';
                    sshInput.style.display = 'none';
                }
            }

            radioSSH.addEventListener('change', toggleFields);
            radioOAUTH.addEventListener('change', toggleFields);
            toggleFields();
        });
    </script>
{/block}
