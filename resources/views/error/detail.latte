{layout '../layout.latte'}

{block content}
    <div class="uk-container">
        <div class="error-detail">
            {if $error->getDependsOn()}
                <div id="dependence" uk-modal>
                    <div class="uk-modal-dialog uk-modal-body uk-text-center uk-margin-auto-vertical">
                        <button class="uk-modal-close-outside" type="button" uk-close></button>
                        <p>Tato chyba byla oz<PERSON> jako d<PERSON> chyby č. <a href="{route('detail', [error => $error->getDependsOn()->getId()])}" uk-tooltip="title: {$error->getDependsOn()->getMessage()|truncate:30}; pos: bottom">{$error->getDependsOn()->getId()}</a>.</p>
                    </div>
                </div>
                <script>
                    UIkit.modal('#dependence').show();
                </script>
            {/if}
            <div class="uk-flex uk-flex-between">
                <div class="uk-flex uk-flex-middle">
                    {if !$error->isResolved() && $error->getUser() === null}
                        <div class="uk-label uk-label-danger">Nevyřešeno</div>
                    {elseif !$error->isResolved() && $error->getUser() != null}
                        <div class="uk-label">Řeší se</div>
                    {else}
                        <div class="uk-label uk-label-success">Vyřešeno</div>
                    {/if}
                    <p class="uk-margin-remove" style="padding: 0 10px">Celkem <strong>{$occurrencesCount}</strong> výskytů, poslední výskyt <a href="{route('apps.error-log', [appSlug => $error->getApp()->getSlug()])}?error={$error->getId()}" target="_blank">{$lastOcc}</a>.</p>
                    {if $error->hasUser()}
                        <div>
                            <img src="{$user->getAvatar()}" style="width: 25px;border-radius: 50%;margin-right: 5px" alt="">
                            <strong>{$user->getName()}</strong>
                        </div>
                    {/if}
                </div>
                <div class="uk-flex uk-flex-right uk-flex-middle">
                    {if $isDev}
                        {if !$error->getUser()}
                            <div class="uk-button-group">
                                <a href="{route('detail.action', [$error->getId(), 'own'])}" class="uk-button uk-button-small uk-button-default">Přiřadit se</a>
                                <div class="uk-inline">
                                    <button class="uk-button uk-button-default" type="button" aria-label="Toggle Dropdown"><span uk-icon="icon: triangle-down"></span></button>
                                    <div uk-dropdown="mode: click; target: !.uk-button-group;" style="min-width: 350px">
                                        <ul class="uk-list uk-list-divider">
                                            {foreach $users as $u}
                                                {continueIf $u === Illuminate\Support\Facades\Auth::guard('gitlab')->user()->getIdentity()}
                                                <li class="uk-flex uk-flex-middle uk-flex-between">
                                                    <div class="uk-width-1-4 uk-flex uk-flex-middle">
                                                        <img class="uk-border-circle uk-margin-small-right" width="40" height="40" src="{$u->getAvatar()}" alt="">
                                                        <p class="uk-margin-remove uk-text-secondary uk-text-nowrap">{$u->getName()}</p>
                                                    </div>
                                                    <a href="{route('detail.action', [$error->getId(), 'own', uid => $u->getId()])}">
                                                        <i class="fa-kit fa-regular-user-helmet-safety-circle-check fa-xl"></i>
                                                    </a>
                                                </li>
                                            {/foreach}
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            <hr class="uk-divider-vertical">
                        {/if}
                        {if !$error->isResolved() && $error->hasUser() && $error->getUser() === Illuminate\Support\Facades\Auth::guard('gitlab')->user()->getIdentity()}
                            <a href="#resolve-modal" style="background-color: forestgreen; color: white" class="uk-button uk-button-small uk-margin-small-right" uk-toggle>Vyřešit</a>
                            <div id="resolve-modal" class="uk-flex-top" uk-modal>
                                <div class="uk-modal-dialog uk-modal-body uk-margin-auto-vertical">
                                    <button class="uk-modal-close-outside" type="button" uk-close></button>
                                    <form method="get" action="{route('detail.action', [error => $error->getId(), action => 'resolve'])}" class="uk-form-stacked">
                                        <div class="uk-margin">
                                            <label class="uk-form-label uk-text-large uk-margin-bottom" for="c">Komentář</label>
                                            <div class="uk-form-controls">
                                                <textarea class="uk-textarea" id="c" type="text" name="comment" rows="5" placeholder="Stáří chyby, dočasná chyba,..."></textarea>
                                            </div>
                                        </div>

                                        <button class="uk-button uk-button-primary" type="submit">Uzavřít chybu</button>
                                    </form>
                                </div>
                            </div>
                            <a href="{route('detail.action', [error => $error->getId(), action => 'disown'])}" class="uk-button uk-button-small uk-button-danger">Odhlásit se</a>
                            <hr class="uk-divider-vertical">
                        {/if}
                        {if $error->isResolved() && $error->hasUser() && $error->getUser() === Illuminate\Support\Facades\Auth::guard('gitlab')->user()->getIdentity()}
                            <a href="{route('detail.action', [error => $error->getId(), action => 'unresolve'])}" class="uk-button uk-button-small uk-button-danger">Označit jako nevyřešené</a>
                            <hr class="uk-divider-vertical">
                        {/if}
                    {/if}
                    <a href="{route('apps.error-log', [appSlug => $error->getApp()->getSlug()])}?error={$error->getId()}" class="uk-button uk-button-small uk-button-default">Errorlog</a>
                    <hr class="uk-divider-vertical">
                    <form class="uk-flex" method="get" action="{route('error.add-dependant')}" style="width: 100px">
                        <input type="hidden" name="child-error" value="{$error->getId()}">
                        <input class="uk-input uk-form-small uk-margin-remove" type="text" name="parent-error" placeholder="Parent ID" aria-label="Parent ID" required>
                    </form>
                    {var $children = $error->getDependants()->count()}
                    <a href="{route('detail.children', [error => $error->getId()])}">
                        <button class="uk-button uk-button-primary uk-margin-small-left" uk-tooltip="Zobrazit závislé chyby" {if $children === 0}disabled{/if}>
                            <i class="fa-regular fa-list-timeline uk-margin-small-right"></i> {$children}
                        </button>
                    </a>
                </div>
            </div>
            <h2 class="uk-margin-top">
                <a href="{route('error', [$error->getApp()->getSlug()])}">{$error->getApp()->getTitle()}</a>
            </h2>
            {var $type = strtolower($error->getType()->getTitle())}
            <div class="message message-{if str_contains($type, 'notice')}notice{elseif str_contains($type, 'warning')}warning{else}danger{/if}">
                <div class="uk-flex uk-flex-middle message-header message-header-{if str_contains($type, "notice")}notice{elseif str_contains($type, "warning")}warning{else}danger{/if}">
                    <i uk-icon="warning" class="uk-margin-small-right"></i> {$error->getTypeOrException()}
                </div>
                <pre>{$error->getMessage()}</pre>
            </div>
            <table class="uk-table uk-table-divider uk-table-small">
                <tr><td class="uk-table-shrink">Soubor:</td><td>{$error->getFile()}</td></tr>
                <tr><td class="uk-table-shrink">Řádek:</td><td>{$error->getLine()}</td></tr>
                <tr><td class="uk-table-shrink">URL:</td><td>{$error->getUrl()}</td></tr>
                <tr n:if="$error->getTrace()"><td class="uk-table-shrink">Trace:</td><td><a href="{route('detail.trace', [$error->getId()])}" class="uk-text-decoration-none" target="_blank">{$error->getTrace()->getName()}</a></td></tr>
            </table>

            {$calendar->render()|noescape}

            <article n:inner-foreach="$error->getComments() as $comment" class="uk-comment">
                <header class="uk-comment-header">
                    <div class="uk-grid-medium uk-flex-middle" uk-grid>
                        <div class="uk-width-auto">
                            <img class="uk-comment-avatar" src="{$comment->getUser()->getAvatar()}" style="border-radius: 50%" width="40" height="40" alt="">
                        </div>
                        <div class="uk-width-expand">
                            <h3 class="uk-comment-title uk-margin-remove">{$comment->getUser()->getName()}</h3>
                            <ul class="uk-comment-meta uk-subnav uk-subnav-divider uk-margin-remove-top">
                                <li>{$comment->getCreatedAt()|date:'d.n.Y H:i'}</li>
                            </ul>
                        </div>
                    </div>
                </header>
                <div class="uk-comment-body">
                    <p n:if="$comment->getText()">{$comment->getText()|noescape}</p>
                    <p n:else><em>Vyřešeno bez komentáře.</em></p>
                </div>
            </article>

            <h3 class="uk-margin-remove-bottom uk-margin-top uk-text-bold">Gitlab</h3>
            <div class="uk-container uk-padding">
                <div class="uk-timeline">
                    <div n:if="!$isDev" class="uk-placeholder uk-text-center">Pro zobrazení komentářů je potřeba se přihlásit přes Gitlab Access Token.</div>
                    {foreach $comments as $comment}
                        {if $comment['system']}
                            <div class="uk-timeline-item">
                                <div class="uk-timeline-icon">
                                    <span class="uk-badge" style="background-color: white"><img src="{asset('images/gitlab.svg')}" alt="Gitlab logo"></span>
                                </div>
                                <div class="uk-timeline-content">
                                    <div class="uk-card uk-card-default uk-overflow-auto uk-flex-column" style="padding: 0;margin: 5px; border: unset">
                                        <div class="uk-card-header uk-padding-remove" style="padding: 15px">
                                            <div class="uk-grid uk-grid-small uk-flex-middle" style="line-height: 40px">
                                                <strong>{$comment['author']['name']}&nbsp;</strong>
                                                {if preg_match('#created branch \[`(.*)`\]\((.*)\) to address this issue#', $comment['body'], $matches)}
                                                    created branch <a class="uk-margin-small-right" href="{config('gitlab.homepage').$matches[2]}" target="_blank"><code>{$matches[1]}</code></a> to address this issue
                                                {elseif preg_match('#mentioned in merge request (![0-9]+)$#', $comment['body'], $matches)}
                                                    mentioned in merge request <a class="uk-margin-small-right" href="{$gurl.'/-/merge_requests/'.ltrim($matches[1], '!')}" target="_blank"><code>{$matches[1]}</code></a>
                                                {elseif preg_match('#assigned to (@(.*))$#', $comment['body'], $matches)}
                                                    assigned to <a href="{config('gitlab.homepage').'/'.$matches[2]}" target="_blank">{$matches[1]}</a>
                                                {elseif preg_match('#marked this issue as related to (\#(.*))$#', $comment['body'], $matches)}
                                                    marked this issue as related to <a href="{$gurl.'/-/issues/'.$matches[2]}" target="_blank"><code>{$matches[1]}</code></a>
                                                {elseif preg_match('#^mentioned in commit (.*)$#', $comment['body'], $matches)}
                                                    mentioned in commit <a href="{$gurl}/-/commit/{$matches[1]}" target="_blank"><code>{$matches[1]|substr:0,7}</code></a>
                                                {elseif preg_match('#^mentioned in issue \#(.*)$#', $comment['body'], $matches)}
                                                    mentioned in issue <a href="{$gurl}/-/issues/{$matches[1]}" target="_blank"><code>#{$matches[1]}</code></a>
                                                {else}
                                                    {$comment['body']}
                                                {/if}
                                                <span class="uk-label uk-margin-auto-left" style="background-color: darkorange">Gitlab</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        {else}
                            <div class="uk-timeline-item">
                                <div class="uk-timeline-icon">
                                    <span class="uk-badge" style="background-color: white; border-radius: 50%; overflow: hidden; width: 40px; height: 40px">
                                        <img src="{$comment['author']['avatar_url']}" alt="Avatar" style="min-width: 130%; height: auto;border-radius: 50%">
                                    </span>
                                </div>
                                <div class="uk-timeline-content">
                                    <div class="uk-card uk-card-default uk-overflow-auto uk-flex-column" style="padding: 0;margin: 5px">
                                        <div class="uk-card-header uk-padding-remove">
                                            <div class="uk-grid uk-grid-small uk-flex-middle" style="padding-top: 5px">
                                                <strong>{$comment['author']['name']}</strong>&nbsp;•&nbsp;{date('j.n.Y H:i',strtotime($comment['created_at']))}
                                                <span class="uk-label uk-margin-auto-left"></span>
                                            </div>
                                        </div>
                                        <div class="uk-card-body" style="padding: 5px 12px 15px 10px">
                                            {preg_replace('#^<p>|</p>$#', '', $comment['body'])|noescape}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        {/if}
                    {/foreach}
                    {if $issueState && $issueState['state'] == 'closed'}
                        <div class="uk-timeline-item">
                            <div class="uk-timeline-icon">
                                <span class="uk-badge" style="background-color: white"><img src="{asset('images/gitlab.svg')}" alt="Gitlab logo"></span>
                            </div>
                            <div class="uk-timeline-content">
                                <div class="uk-card uk-card-default uk-overflow-auto uk-flex-column" style="padding: 0;margin: 5px; border: unset">
                                    <div class="uk-card-header uk-padding-remove" style="padding: 15px">
                                        <div class="uk-grid uk-grid-small uk-flex-middle" style="line-height: 40px">
                                            <strong>{$issueState['closed_by']['name']}</strong>&nbsp;closed this issue {date('j.n.Y H:i',strtotime($issueState['closed_at']))}
                                            <span class="uk-label uk-margin-auto-left" style="background-color: darkorange">Gitlab</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    {/if}
                </div>
            </div>
            {if $isDev}
                {if !$error->getIssue()}
                    <a href="{route('detail.action', [$error->getId(), 'create-gitlab-issue'])}"><button class="uk-button uk-button-secondary uk-margin-top" type="button">Založit issue</button></a>
                {else}
                    <div class="uk-flex uk-flex-middle">
                        {if $issueState['state'] === 'opened'}
                            <a href="{route('detail.action', [error => $error->getId(), action => 'close'])}"><button class="uk-button uk-button-secondary uk-margin-top" type="button">Uzavřít issue</button></a>
                        {else}
                            <a href="{route('detail.action', [error => $error->getId(), action => 'reopen'])}"><button class="uk-button uk-button-secondary uk-margin-top" type="button">Otevřít issue</button></a>
                        {/if}
                        <a class="uk-button uk-button-default uk-margin-small-left uk-margin-top" href="{$gurl}/-/issues/{$error->getIssue()->getIid()}" target="_blank"><i class="fa-brands fa-gitlab uk-margin-small-right"></i> Detail issue</button></a>
                    </div>
                {/if}

                {if $error->getIssue() && $issueState['state'] === 'opened'}
                    <form action="{route('detail.action', [error => $error->getId(), action => 'add-comment'])}" method="post" class="uk-margin-small-top">
                        {csrf}
                        <textarea name="comment" class="uk-textarea" rows="5" required {if $isDev}placeholder="Komentář (Markdown je podporován)" {else}placeholder="Komentáře jsou povolené pouze pro uživatele přihlášené přes Gitlab Access Token." disabled{/if} aria-label="Komentář"></textarea>
                        <button class="uk-button uk-button-primary uk-margin-top" type="submit" {if !$isDev}disabled{/if}>Přidat komentář</button>
                    </form>
                {/if}
            {/if}
        </div>
    </div>
{/block}
