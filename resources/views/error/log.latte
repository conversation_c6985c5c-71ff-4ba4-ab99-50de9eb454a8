<!DOCTYPE html>
<html lang="cs">
<head>
    <link rel="stylesheet" href="{asset('css/site.css')}?m={filemtime(public_path('css/site.css'))}">
    <script src="https://kit.fontawesome.com/3e7158a24b.js" crossorigin="anonymous"></script>

    <title>{$slug} - Errorlog</title>
</head>
<body>
    <div id="custom-width" class="error-log">
        {foreach $lines as $line}
            <div n:class="$line['style'], $line['current'] ? 'current'">{$line['msg']}</div>
        {/foreach}
    </div>

    {include resource_path('views/partials/paginator.latte'), paginable => $lines}

</body>
<script>
    function getWidth() {
        return Math.max(
            document.body.scrollWidth,
            document.documentElement.scrollWidth,
            document.body.offsetWidth,
            document.documentElement.offsetWidth,
            document.documentElement.clientWidth
        );
    }
    document.getElementById('custom-width').style.width = getWidth() + 'px';

    var main = document.getElementById( 'custom-width' );

    [].map.call( main.children, Object ).sort( function ( a, b ) {
        return +a.id.match( /\d+/ ) - +b.id.match( /\d+/ );
    }).forEach( function ( elem ) {
        main.appendChild( elem );
    });
</script>
</html>
