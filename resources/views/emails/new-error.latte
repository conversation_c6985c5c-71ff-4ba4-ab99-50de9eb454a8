<style>
    /* import IBM Plex Sans font */
    @font-face {
        font-family: 'IBM Plex Sans';
        font-style: normal;
        font-weight: 100;
        font-display: swap;
        src: url('https://fonts.gstatic.com/s/ibmplexsans/v8/zYX-KVElMYYaJe8bpLHnCwLUuEpTyoUstqEm5AMlF4g.woff2') format('woff2');
    }
    * {
        font-family: 'IBM Plex Sans', sans-serif;
    }
    .uk-container {
        display: flow-root;
        box-sizing: content-box;
        max-width: 1200px;
        margin-left: auto;
        margin-right: auto;
        padding-left: 30px;
        padding-right: 30px;
    }
    .uk-width-1-5 {
        width: 20%;
    }
    .uk-width-4-5 {
        width: 80%;
    }
    .uk-card {
        position: relative;
        box-sizing: border-box;
        background-color: #fff;
        border: 1px solid #e5e5e5;
        margin: 20px 0;
        padding: 20px;
        display: flex;
    }
    .uk-card-title {
        font-size: 1.5rem;
        line-height: 1;
    }
    .uk-card-badge {
        position: absolute;
        top: 15px;
        right: 15px;
        z-index: 1;
        height: 22px;
        padding: 0 10px;
        background: #1e87f0;
        color: #fff;
        font-size: 0.875rem;
        display: flex;
        justify-content: center;
        align-items: center;
        line-height: 0;
        border-radius: 5px;
    }
    .uk-card-badge:first-child + * {
        margin-top: 0;
    }
    .uk-card-default {
        color: #666;
        background-color: #fff;
    }
    .uk-card-default .uk-card-title {
        color: #333;
    }
    .uk-card-body > :last-child,
    .uk-card-header > :last-child,
    .uk-card-footer > :last-child {
        margin-bottom: 0;
    }
    :not(.uk-grid-stack) > .uk-card-media-left {
        width: 20%;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        border-right: 1px solid #e5e5e5;
    }
    :not(.uk-grid-stack) > .uk-card-media-left div {
        display: flex;
        justify-content: space-between;
        margin: 5px 16px 0 0;
    }
    :not(.uk-grid-stack) > .uk-card-media-left h3 {
        font-size: 16px;
        margin: 0;
    }
    :not(.uk-grid-stack) > .uk-card-media-left p {
        color: #aaa;
        font-size: 16px;
        margin: 0;
    }
    .card-main {
        width: 80%;
        margin: 10px 20px;
        font-size: 16px;
    }
    .card-main h2 {
        margin-bottom: 10px;
    }
    .stack-effect-1 {
        box-shadow: -2px 2px 0 -1px #f8f8f8, -2px 2px #e5e5e5;
    }
    .uk-label {
        padding: 0 10px;
        line-height: 1.5;
        font-size: 0.875rem;
        color: #fff;
        vertical-align: middle;
        white-space: nowrap;
        background: #1e87f0;
        border-radius: 2px;
        display: list-item;
    }
    .uk-label-success {
        color: #fff;
        background-color: #2bbd2b;
    }
    .uk-label-danger {
        color: #fff;
        background-color: #e93e3e;
    }
    .uk-text-decoration-none {
        text-decoration: none !important;
    }
    .clickable {
        cursor: pointer;
    }
    .clickable:hover {
        background-color: #f8f8f8;
        color: unset;
    }
</style>
<div class="uk-container">
    <div>
        <a href="{route('detail', [error => $newError->getId()])}" class="uk-text-decoration-none uk-card uk-card-default stack-effect-1 clickable">
            {if !$newError->isResolved() && $newError->getUser() === null}
                <div class="uk-card-badge uk-label-danger">Nevyřešeno</div>
            {elseif !$newError->isResolved() && $newError->getUser() != null}
                <div class="uk-card-badge uk-label">Řeší se ({$newError->getUser()->getName()})</div>
            {else}
                <div class="uk-card-badge uk-label-success">Vyřešeno ({$newError->getUser()->getName()})</div>
            {/if}
            <div class="uk-card-media-left uk-width-1-5">
                <div>
                    <h3>Poslední</h3>
                    <p>-</p>
                </div>
                <div>
                    <h3>První</h3>
                    <p>-</p>
                </div>
                <div>
                    <h3>Počet výskytů</h3>
                    <p>{count($newError->getOccurrences())}</p>
                </div>
            </div>
            <div class="card-main uk-width-4-5">
                <h2 class="uk-card-title">{$newError->getApp()->getTitle()}</h2>
                <p>
                    <strong>{$newError->getTypeOrException()}</strong>
                    : {$newError->getMessage()|truncate:250} in {$newError->getfile()}:{$newError->getLine()} ({$newError->getFilename()})
                </p>
            </div>
        </a>
    </div>
</div>
</body>
</html>
