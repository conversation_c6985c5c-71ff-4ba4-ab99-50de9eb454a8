<div class="uk-navbar-container">
    <div class="uk-container">
        <nav class="uk-navbar">
            <div class="uk-navbar-left">
                <ul class="uk-navbar-nav">
                    <li {if \Illuminate\Support\Facades\Route::is('error', 'homepage')}class="uk-active"{/if}>
                        <a class="uk-button uk-button-text" href="{route('homepage')}"><i class="fa-duotone fa-bugs fa-xl uk-margin-small-right"></i> Chyby</a>
                    </li>
                    <li {if \Illuminate\Support\Facades\Route::is('apps.index')}class="uk-active"{/if}>
                        <a class="uk-button uk-button-text" href="{route('apps.index')}"><i class="fa-duotone fa-window fa-xl uk-margin-small-right"></i> Aplikace</a>
                    </li>
                    <li {if \Illuminate\Support\Facades\Route::is('users.index')}class="uk-active"{/if}>
                        <a class="uk-button uk-button-text" href="{route('users.index')}"><i class="fa-duotone fa-user-bounty-hunter fa-xl uk-margin-small-right"></i> Uživatelé</a>
                    </li>
                </ul>
            </div>
            <div class="uk-navbar-right">
                <form class="uk-search uk-search-default" method="get" action="{route('homepage')}">
                    <span uk-search-icon></span>
                    <input class="uk-search-input" type="search" name="q" value="{request('q') ?? ''}" placeholder="Hledat" aria-label="Hledat" autocomplete="off">
                    {if request('state')}
                        <input type="hidden" name="state" value="{request('state') ?? ''}" aria-label="state">
                    {/if}
                </form>
                {if $loggedUser}
                    <img n:if="$loggedUser->getIdentity()->getAvatar() !== null && $isDev" src="{$loggedUser->getIdentity()->getAvatar()}" class="avatar" alt="{substr($loggedUser->getIdentity()->getName(), 0, 1)}">
                    <a class="uk-navbar-item user-dashboard" href="{route('logout')}"><i class="fa-duotone fa-arrow-right-from-bracket fa-xl"></i></a>
                {/if}
            </div>
        </nav>
    </div>
</div>
