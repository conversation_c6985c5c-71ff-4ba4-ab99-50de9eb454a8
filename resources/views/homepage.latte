{layout 'layout.latte'}

{block content}
    <div class="uk-container">
        <div class="uk-flex uk-flex-between uk-flex-middle">
            <div class="uk-flex">
                <ul n:if="isset($countUnresolved) && isset($countAssigned) && isset($countResolved) && isset($countTotal)" class="uk-margin-remove uk-tab">
                    <li {if request('state') === \App\Enums\ErrorStatus::OPEN->value}class="uk-active"{/if}><a href="?state={\App\Enums\ErrorStatus::OPEN->value}{if request('q')}&q={request('q')}{/if}">Nevyřešené <span class="uk-badge uk-margin-small-left">{$countUnresolved}</span></a></li>
                    <li {if request('state') === \App\Enums\ErrorStatus::ASSIGNED->value}class="uk-active"{/if}><a href="?state={\App\Enums\ErrorStatus::ASSIGNED->value}{if request('q')}&q={request('q')}{/if}">V řešení <span class="uk-badge uk-margin-small-left">{$countAssigned}</span></a></li>
                    <li {if request('state') === \App\Enums\ErrorStatus::RESOLVED->value}class="uk-active"{/if}><a href="?state={\App\Enums\ErrorStatus::RESOLVED->value}{if request('q')}&q={request('q')}{/if}">Vyřešené <span class="uk-badge uk-margin-small-left">{$countResolved}</span></a></li>
                    <li {if request('state') === 'all'}class="uk-active"{/if}><a href="?state=all{if request('q')}&q={request('q')}{/if}">Všechny <span class="uk-badge uk-margin-small-left">{$countTotal}</span></a></li>
                </ul>
                {if request('q')}
                    <div class="uk-margin-small-left">
                        <a href="?{if request('page')}&page={request('page')}{/if}{if request('state')}&state={request('state')}{/if}" class="uk-button uk-button-default uk-button-small">Zrušit vyhledávání</a>
                    </div>
                {/if}
                {if request('state')}
                    <div class="uk-margin-small-left">
                        <a href="?{if request('page')}&page={request('page')}{/if}{if request('q')}&q={request('q')}{/if}" class="uk-button uk-button-default uk-button-small">Zrušit filtr</a>
                    </div>
                {/if}
            </div>
            <div n:if="$application"><h2 class="uk-margin-remove uk-text-bold" style="font-size: 36px">{$application->getTitle()}</h2></div>
        </div>
        {if $paginatedErrors->total() === 0 && !request('q') && !request('state')}
            <svg class="checkmark" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 52 52">
                <circle class="checkmark__circle" cx="26" cy="26" r="25" fill="none"/>
                <path class="checkmark__check" fill="none" d="M14.1 27.2l7.1 7.2 16.7-16.8"/>
            </svg>
            <h2 class="uk-text-center">Žádné chyby nebyly nalezeny</h2>
        {else}
            {foreach $paginatedErrors as $singleError}
                <div>
                    <a href="{route('detail', [error => $singleError->getId()])}" class="uk-text-decoration-none uk-card uk-card-default clickable stack-effect-{stackLayers(count($singleError->getOccurrences()))}">
                        {if !$singleError->isResolved() && $singleError->getUser() === null}
                            <div class="uk-card-badge uk-label-danger">Nevyřešeno</div>
                        {elseif !$singleError->isResolved() && $singleError->getUser() != null}
                            <div class="uk-card-badge uk-label">Řeší se ({$singleError->getUser()->getName()})</div>
                        {else}
                            <div class="uk-card-badge uk-label-success">Vyřešeno {if $singleError->getUser()}({$singleError->getUser()->getName()}{/if})</div>
                        {/if}
                        <div class="uk-card-media-left uk-width-1-5">
                            <div>
                                <h3>Poslední</h3>
                                <p>{$singleError->getLastOccurrence()->getDate()->format('d.n.Y H:i')}</p>
                            </div>
                            <div>
                                <h3>První</h3>
                                <p>{$singleError->getFirstOccurrence()->getDate()->format('d.n.Y H:i')}</p>
                            </div>
                            <div>
                                <h3>Počet výskytů</h3>
                                <p>{count($singleError->getOccurrences())}</p>
                            </div>
                        </div>
                        <div class="card-main uk-width-4-5">
                            <h2 class="uk-card-title">{$singleError->getApp()->getTitle()}</h2>
                            <p>
                                <strong>{$singleError->getTypeOrException()}</strong>
                                : {$singleError->getMessage()|truncate:100} in {$singleError->getfile()}:{$singleError->getLine()} ({$singleError->getFilename()})
                            </p>
                        </div>
                    </a>
                </div>
            {/foreach}
        {/if}

        {include 'partials/paginator.latte', 'paginable' => $paginatedErrors}
    </div>
{/block}
