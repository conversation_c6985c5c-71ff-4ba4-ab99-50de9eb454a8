{layout '../layout.latte'}

{block content}
    <div class="uk-container">
        <div n:if="$isDev" class="uk-flex uk-flex-between">
            <h1 class="uk-text-large uk-margin-remove uk-width-expand">U<PERSON><PERSON>lé</h1>
            <a class="uk-button uk-button-primary uk-margin-small-left" href="{route('users.create')}">Přidat uživatele</a>
        </div>

        <ul class="uk-list uk-list-divider uk-list-striped">
            {foreach $users as $user}
                <li class="uk-flex uk-flex-middle uk-flex-between">
                    <div class="uk-width-1-4 uk-flex uk-flex-middle">
                        <p class="uk-text-meta uk-margin-remove uk-width-1-6">{$user->getGitlabId()}</p>
                        <img class="uk-border-circle uk-margin-right" width="40" height="40" src="{$user->getAvatar()}" alt="">
                        <p class="uk-margin-remove uk-text-secondary uk-width-expand uk-text-nowrap">{$user->getName()}</p>
                    </div>
                    <div>
                        <a n:if="$isDev" href="{route('users.edit', [user => $user->getId()])}" class="uk-text-primary uk-margin-right"><i class="fa-solid fa-pencil fa-lg"></i></a>
                        {if $user->getGitlabUrl()}
                            <a href="{$user->getGitlabUrl()}" target="_blank" class="uk-margin-right"><i class="fa-brands fa-gitlab fa-lg" style="color: #fc6d27"></i></a>
                        {else}
                            <span class="uk-margin-right"><i class="fa-brands fa-gitlab fa-lg" style="color: #cdcdcd"></i></span>
                        {/if}
                        <form class="uk-display-inline" action="{route('users.destroy', $user->getId())}" method="post" id="delete-{$user->getId()}">
                            {csrf}
                            {method DELETE}
                            <i class="fa-solid fa-trash-xmark fa-lg" style="color: #e01b24; cursor: pointer;" onclick="confirm('Opravdu smazat uživatele {$user->getName()}?') ? document.getElementById('delete-{$user->getId()}').submit() : false"></i>
                        </form>
                    </div>
                </li>
            {/foreach}
        </ul>

        {include '../partials/paginator.latte', 'paginable' => $users}
    </div>
{/block}
