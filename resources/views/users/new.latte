{layout '../layout.latte'}

{block content}
    <div class="uk-container">
        <div class="uk-flex uk-flex-between">
            <h1 class="uk-text-large uk-width-expand">Nový uživatel</h1>
        </div>

        {if $errors->any()}
            <div class="uk-alert-danger" style="margin: 0 0 20px" uk-alert>
                <ul class="uk-list">
                    {foreach $errors->all() as $error}
                        <li>{$error}</li>
                    {/foreach}
                </ul>
            </div>
        {/if}
        <form class="uk-form-stacked" method="post" action="{route('users.store')}">
            {csrf}
            <fieldset class="uk-fieldset">
                <div class="uk-margin">
                    <label class="uk-form-label" for="name">Jméno</label>
                    <input class="uk-input" id="name" name="name" value="{old('name')}" type="text" autocomplete="off" required>

                    <label class="uk-form-label" for="email">E-mail</label>
                    <input class="uk-input" id="email" name="email" value="{old('email')}" type="email" autocomplete="off" required>

                    <label class="uk-form-label" for="gid">Gitlab ID</label>
                    <input class="uk-input" id="gid" name="gid" value="{old('gid')}" type="number" required>
                </div>
            </fieldset>

            <button type="submit" class="uk-button uk-button-primary">Odeslat</button>
        </form>
    </div>
{/block}
