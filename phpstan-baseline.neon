parameters:
	ignoreErrors:
		-
			message: '#^Cannot cast mixed to string\.$#'
			identifier: cast.string
			count: 5
			path: src/UriTemplate.php

		-
			message: '#^Method GuzzleHttp\\UriTemplate\\UriTemplate\:\:isAssoc\(\) has parameter \$array with no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/UriTemplate.php

		-
			message: '#^Parameter \#1 \$matches of static method GuzzleHttp\\UriTemplate\\UriTemplate\:\:expandMatch\(\) expects array\<string\>, array given\.$#'
			identifier: argument.type
			count: 1
			path: src/UriTemplate.php
