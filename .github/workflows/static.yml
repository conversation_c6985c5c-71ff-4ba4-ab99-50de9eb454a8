name: Static analysis

on:
  push:
    branches:
  pull_request:

permissions:
  contents: read

jobs:
  phpstan:
    name: PHPStan
    runs-on: ubuntu-22.04

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: '7.4'
          coverage: none
          extensions: mbstring, intl

      - name: Download dependencies
        run: composer update --no-interaction --no-progress

      - name: Download PHPStan
        run: composer bin phpstan update --no-interaction --no-progress

      - name: Execute PHPStan
        run: vendor/bin/phpstan analyze --no-progress

  php-cs-fixer:
    name: PHP-CS-Fixer
    runs-on: ubuntu-22.04

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: '7.4'
          coverage: none
          extensions: mbstring

      - name: Download dependencies
        run: composer update --no-interaction --no-progress

      - name: Download PHP CS Fixer
        run: composer bin php-cs-fixer update --no-interaction --no-progress

      - name: Execute PHP CS Fixer
        run: vendor/bin/php-cs-fixer fix --diff --dry-run

  psalm:
    name: Psalm
    runs-on: ubuntu-22.04

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: '7.4'
          coverage: none
          extensions: mbstring, intl

      - name: Download dependencies
        run: composer update --no-interaction --no-progress

      - name: Download Psalm
        run: composer bin psalm update --no-interaction --no-progress

      - name: Execute Psalm
        run: vendor/bin/psalm.phar --no-progress --output-format=github
