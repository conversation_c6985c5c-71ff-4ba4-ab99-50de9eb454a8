name: CI

on:
  push:
    branches:
  pull_request:

jobs:
  build-lowest-version:
    name: Build lowest version
    runs-on: ubuntu-22.04

    steps:
      - name: Set up PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: '7.2'
          ini-values: error_reporting=E_ALL
          coverage: 'none'
          extensions: mbstring

      - name: Checkout code
        uses: actions/checkout@v4

      - name: Install dependencies
        run: composer update --no-interaction --prefer-stable --prefer-lowest --no-progress

      - name: Run tests
        run: make test

  build:
    name: Build
    runs-on: ubuntu-22.04
    strategy:
      max-parallel: 10
      matrix:
        php: ['7.2', '7.3', '7.4', '8.0', '8.1', '8.2', '8.3', '8.4']

    steps:
      - name: Set up PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: ${{ matrix.php }}
          ini-values: error_reporting=E_ALL
          coverage: 'none'
          extensions: mbstring

      - name: Checkout code
        uses: actions/checkout@v4

      - name: Install dependencies
        run: composer update --no-interaction --no-progress

      - name: Run tests
        run: make test
